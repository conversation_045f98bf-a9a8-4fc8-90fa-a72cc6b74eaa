
// 数据持久化存储
export function saveStoreStorage<T>(newData: T) {
    const version = configStore.getVersion()  
    initVersion(version)
    const vs = localStorage.getItem('version') || version
    const storedData = JSON.parse(localStorage.getItem(`sys-v${vs}`) || '{}')
  
    // 合并新数据与现有数据
    const mergedData = { ...storedData, ...newData }
    localStorage.setItem(`sys-v${vs}`, JSON.stringify(mergedData))
}
  
  // 初始化版本
export  function initVersion(version: string) {
    const vs = localStorage.getItem('version')
    if (!vs) {
      localStorage.setItem('version', version)
    }
}
  