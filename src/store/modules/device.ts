import { Device, DeviceType } from '@/types/device'
import { defineStore } from 'pinia'
import { watch } from 'vue'
import { router } from '@/router'

export const useDeviceStore = defineStore('deviceStore', () => {
    // 添加设备类型后，返回列表页面，根据这个参数来判断刷新
    const deviceTypeIndexRefresh = ref(false)
    // 设备类型页面详情
    const deviceTypeInfo = ref({} as DeviceType) // 详情页面的type_info

    // 设备页面详情 
    const deviceIndexRefresh = ref(false)
    const deviceInfo = ref({} as Device) // 设备详情页面的device_info
    // 设置设备类型页面刷新
    const setDeviceTypeIndexRefresh =  (value: boolean) => {
        deviceTypeIndexRefresh.value = value 
    } 

    // 设置设备页面刷新 
    const setDeviceIndexRefresh = (value: boolean) => {
        deviceIndexRefresh.value = value
    }

    const setDeviceTypeInfo = (value: DeviceType) => {
        deviceTypeInfo.value = value
    }
    const getDeviceTypeInfo = () => {
        return deviceTypeInfo.value
    }

    const setDeviceInfo = (value: Device) => {
        try{
            deviceInfo.value = value
        } catch(e:any) {}
    }

    const getDeviceInfo  = () => {
        return deviceInfo.value
    } 

    return {
        deviceTypeIndexRefresh,
        setDeviceTypeIndexRefresh,
        deviceTypeInfo,
        setDeviceTypeInfo,
        getDeviceTypeInfo,
        deviceInfo,
        setDeviceInfo,
        getDeviceInfo,
        deviceIndexRefresh,
        setDeviceIndexRefresh,
    }
})