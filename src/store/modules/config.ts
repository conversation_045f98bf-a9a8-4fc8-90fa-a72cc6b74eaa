

import { defineStore } from 'pinia'


export const useConfigStore = defineStore('configStore', () => {
    const baseUrl = ref(import.meta.env.VITE_APP_BASE_URL)
    const apiUrl = ref(import.meta.env.VITE_API_URL) 
    const minioApi = ref(import.meta.env.VITE_MINIO_API)
    const dropConsole = ref(import.meta.env.VITE_DROP_CONSOLE)
    const version = ref(import.meta.env.VITE_APP_VERSION)
    const lockEncryptKey = ref(import.meta.env.VITE_LOCK_ENCRYPT_KEY)
    const openRouteInfo = ref(import.meta.env.VITE_OPEN_ROUTE_INFO)
    const wssVideo  = ref(import.meta.env.VITE_WSS_VIDEO)
    
    // 设置设备类型页面刷新

    const getBaseUrl = () : string => {
        return baseUrl.value
    }
    const setApiUrl = (url: string) => {
        apiUrl.value = url
    }
    const getApiUrl = (): string  => {
        return apiUrl.value
    }
    const setMinioApi = (url: string) => {
        minioApi.value = url
    }

    const getMinioApi = (): string  => {
        return minioApi.value
    }

    const setDropConsole = (drop: boolean) => {
        dropConsole.value = drop
    }

    const getDropConsole = (): boolean => {
        return dropConsole.value
    }
    const getVersion = (): string => {
        return version.value
    }

    const getLockEncryptKey = (): string => {
        return lockEncryptKey.value
    }

    const getOpenRouteInfo = (): boolean => {
        return openRouteInfo.value
    }
    const getWssVideo = (): string => {
        return wssVideo.value
    }



    const setDefaultConfig = () => {
        if(lockEncryptKey.value === undefined ) {
            lockEncryptKey.value = "jfsfjk1938jfj"
        }
        if(openRouteInfo.value === undefined) {
            openRouteInfo.value = false
        }
        if(dropConsole.value === undefined) {
            dropConsole.value = false
        }
    }

    const setConfig = (config: any) => {
        if(config.base_url != undefined) {
            baseUrl.value = config.base_url
        }
        if (config.api_url != undefined) {
            apiUrl.value = config.api_url
        }
        if (config.minio_api != undefined) {
            minioApi.value = config.minio_api
        }
        if (config.drop_console != undefined) {
            dropConsole.value = config.drop_console
        }
        if(config.version != undefined) {
            version.value = config.version
        }
        if(config.lock_encrypt_key != undefined) {
            lockEncryptKey.value = config.lock_encrypt_key
        }
        if(config.open_route_info != undefined) {
            openRouteInfo.value = config.open_route_info
        }
        
        // 设置默认配置
        setDefaultConfig() 
    }

    return {
        getBaseUrl,
        getApiUrl,
        getMinioApi,
        getDropConsole,
        getVersion,
        getLockEncryptKey,
        getOpenRouteInfo,
        getWssVideo,
        setConfig,
    }
})