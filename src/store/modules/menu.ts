import { defineStore } from 'pinia'
import { ref } from 'vue'
import { MenuListType } from '@/types/menu'
import { getSysStorage } from '@/utils/storage';
import { useUserStore } from '@/store/modules/user'
import { menuService } from '@/api/menuApi';
import { ElLoading } from 'element-plus';
import { UserService } from '@/api/usersApi';
import { ApiStatus } from '@/utils/http/status';
import { saveStoreStorage } from './storestorage';

// 菜单
export const useMenuStore = defineStore('menuStore', () => {
  const menuList = ref<MenuListType[]>([])
  const menuWidth = ref('')
  const displayCtl = ref(false)

  const setMenuList = (list: MenuListType[]) => { 
    menuList.value = list
    saveStoreStorage({
      menus: menuList.value,
      displayCtl: displayCtl.value,
    })
  }

  const setMenuWidth = (width: string) => (menuWidth.value = width)

  const getMenuList = () => {
    if (menuList.value.length > 0) {
      return menuList.value
    }
    let sys = getSysStorage()
    if (sys) {
      sys = JSON.parse(sys)
      const mlist = sys.menus
      displayCtl.value = sys.displayCtl
      return mlist
    }
    return []
  }

  const setNotCtlMenuList = (menuList: MenuListType[]) => {
    let mList: MenuListType[] = [];
    menuList.forEach((item) => {
      if(!item.path.startsWith("/control")) {
        mList.push(item)
      } 
    })
    setMenuList(mList as [])
  }
  const setCtlMenuList = (menuList: MenuListType[]) => {
    // 设置为control的菜单 
    let mList: MenuListType[] = [];
    menuList.forEach((item) => {
      if(item.path.startsWith("/control")) {
        mList.push(item)
      }
    })
    displayCtl.value = false;
    setMenuList(mList as [])
  }

  /*
     * 对比oneMenu是否在curMenuList中 
    */
  const checkMenuListAndOne = (curMenuList: MenuListType[], oneMenu: any): boolean => {
    for(let i = 0; i < curMenuList.length; i++) {
      let iroute = curMenuList[i]
      if(iroute.path === oneMenu.path) {
        return true
      }
      if(iroute.children && iroute.children?.length > 0 ) {
        if( checkMenuListAndOne(iroute.children, oneMenu)) {
          return true 
        }
      }
    }
    return false 
  }
  /*
    * 对比oneMenu是否在this.menuList中
  */
  const checkInMenu = (oneMenu: any):boolean => {
    return checkMenuListAndOne(menuList.value, oneMenu)
  }
  const addMenu = async (menu: any) => {
    return await menuService.addMenu(menu)
  }
  const updateMenu = async (menu: any) => {
    return await menuService.updateMenu(menu)
  }
  const deleteMenu = async (menu_id: number) => {
    return await menuService.deleteMenu({menu_id: menu_id})
  }
  const  refreshMenuList = async () => {
    const { menuList, closeLoading } = await menuService.getMenuList()
    // console.log("refresh menu list:", menuList)
    // 如果菜单列表为空，执行登出操作并跳转到登录页
    if (!Array.isArray(menuList) || menuList.length === 0) {
      closeLoading()
      useUserStore().logOut()
      throw new Error('获取菜单列表失败，请重新登录！')
    }

    // 设置菜单列表
    setMenuList(menuList as [])
    closeLoading()
  }

  const returnControl = async(id:number) => {
    const loading = ElLoading.service({
      lock: true,
      text: '正在加载中...,loading...',
      background: 'rgba(0, 0, 0, 0.9)',
      fullscreen: true 
    })
    // 返回控制台
    const resp = await  UserService.companySwitch({ company_id: id })
    if (resp.code !== ApiStatus.success) {
      ElMessage.error(resp.message)
      return
    }
    // console.log("resp:", resp.payload)
    const userStore = useUserStore()
    userStore.setToken(resp.payload.token)
    userStore.setUserInfo(resp.payload.user)
    setCtlMenuList(resp.payload.menus)
    window.location.reload();
    setTimeout(() => {loading.close()}, 3000)
  }
  const changeCompany = async (id:number, hrefLink:string) => {
    // console.log("id:", id)
    const loading = ElLoading.service({
      lock: true,
      text: '正在加载中...,loading...',
      background: 'rgba(0, 0, 0, 0.9)',
      fullscreen: true 
    })

    const resp = await  UserService.companySwitch({ company_id: id })
    if (resp.code !== ApiStatus.success) {
      ElMessage.error(resp.message)
      return
    }
    
    const userStore = useUserStore()
    userStore.setToken(resp.payload.token)
    userStore.setUserInfo(resp.payload.user)
    displayCtl.value = true;
    setNotCtlMenuList(resp.payload.menus as [])
    window.location.reload();
    setTimeout(() => {loading.close()}, 3000)
  }

  return {
    menuList,
    menuWidth,
    displayCtl,
    checkInMenu,
    addMenu,
    updateMenu,
    deleteMenu,
    setMenuList,
    setMenuWidth,
    refreshMenuList,
    getMenuList,
    returnControl,
    changeCompany,
  }
})
