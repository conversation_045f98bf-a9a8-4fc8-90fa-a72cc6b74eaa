import App from './App.vue'
import 'default-passive-events'
import { createApp } from 'vue'
import { initStore } from './store'                 // Store
import { initRouter } from './router'               // Router
import '@styles/reset.scss'                         // 重置HTML样式
import '@styles/app.scss'                           // 全局样式
import '@styles/pages.scss'                         // 公共页面样式
import '@styles/el-ui.scss'                         // 优化element样式
import '@styles/mobile.scss'                        // 移动端样式优化
import '@styles/change.scss'                        // 主题切换过渡优化
import '@styles/theme-animation.scss'               // 主题切换动画
import '@icons/system/iconfont.js'                  // 系统彩色图标
import '@icons/system/iconfont.css'                 // 系统图标
import '@styles/el-light.scss'                      // Element 自定义主题（亮色）
import '@styles/el-dark.scss'                       // Element 自定义主题（暗色）
import '@styles/dark.scss'                          // 系统主题
import '@styles/eboxapp.scss'                       // eboxapp运用的统一样式 
import '@utils/console.ts'                          // 控制台输出内容

import { useConfigStore }  from '@/store/modules/config';
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import { setupGlobDirectives } from './directives'


const app = createApp(App)
initStore(app)
initRouter(app)
setupGlobDirectives(app)

declare global {
  var configStore: ReturnType<typeof useConfigStore>;
}
// 
const configStore = useConfigStore();
globalThis.configStore = configStore;
// 提供给整个应用
// 检查是否在浏览器环境再赋值
if (typeof window !== 'undefined') {
  window.configStore = configStore;
}

// 需要放在configStore后面.因为这个language需要configStore获取版本
import language from './language'
app.use(language)

// openlayer
import "vue3-openlayers/styles.css";
import OpenLayersMap from "vue3-openlayers";


app.use(OpenLayersMap)

for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}


app.mount('#app')
