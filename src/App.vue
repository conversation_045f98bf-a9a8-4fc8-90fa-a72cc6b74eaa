<template>
  <ElConfigProvider :size="elSize" :locale="locales[language]" :z-index="3000">
    <RouterView></RouterView>
  </ElConfigProvider>
</template>

<script setup lang="ts">
  import { useUserStore } from './store/modules/user'
  import { useConfigStore } from './store/modules/config'
  import zh from 'element-plus/es/locale/lang/zh-cn'
  import en from 'element-plus/es/locale/lang/en'
  import { systemUpgrade } from './utils/upgrade'
  import { initState, saveUserData } from './utils/storage'
  import { UserService } from './api/usersApi'
  import { ApiStatus } from './utils/http/status'
  import { setThemeTransitionClass } from './utils/theme/animation'

  const userStore = useUserStore()
  const configStore = useConfigStore()
  const { language } = storeToRefs(userStore)
  const elSize = computed(() => (document.body.clientWidth >= 500 ? 'large' : 'default'))

  const locales = {
    zh: zh,
    en: en
  }


  // 获取用户信息
  const getUserInfo = async () => {
    if (userStore.isLogin) {
      const userRes = await UserService.getUserInfo()
      if (userRes.code === ApiStatus.success) {
        userStore.setUserInfo(userRes.payload)
      }
    }
  }

  // 初始化config
const configJson = "/config/config.json";
const initConfig = async() => {
  const response = await fetch(configJson);
  if (!response.ok) {
    configStore.setConfig({});
    console.warn('The configuration file config.json failed to load. The .env configuration will be used');
  } else {
    const data = await response.json();
    configStore.setConfig(data);
  }
  await getUserInfo()
}

onBeforeMount(async () => {
  await initConfig()
  setThemeTransitionClass(true)
})

onMounted(() => {
  initState()
  saveUserData()
  setThemeTransitionClass(false)
  systemUpgrade()
})

</script>
