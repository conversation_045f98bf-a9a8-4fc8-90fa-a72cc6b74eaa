import { MenuTypeEnum, SystemThemeEnum } from '@/enums/appEnum'
import { MenuThemeType, SystemThemeTypes } from '@/types/store'

// 主题设置
export interface ThemeSetting {
  name: string
  theme: SystemThemeEnum
  color: string[]
  leftLineColor: string
  rightLineColor: string
  img: string
}

// 菜单布局
export interface MenuLayout {
  name: string
  value: MenuTypeEnum
  img: string
}

// api配置
export interface EnvConfig {
  api: string  // 后台api地址前缀
  minioApi: string  // minio api地址
  version: string  // 版本号
  baseUrl: string  // 网站地址前缀
  lockEncryptKey: string // 锁屏加密秘钥
  dropConsole: boolean // 是否删除console信息
  openRouteInfo: boolean // 是否打开路由信息
}

// 系统配置
export interface SystemConfig {
  elementPlusTheme: { primary: string }
  systemInfo: {
    name: string
    login: { username: string; password: string }
  }
  // envConfig: EnvConfig
  systemThemeStyles: SystemThemeTypes
  settingThemeList: ThemeSetting[]
  menuLayoutList: MenuLayout[]
  themeList: MenuThemeType[]
  darkMenuStyles: MenuThemeType[]
  systemMainColor: readonly string[]
  systemSetting: {
    defaultMenuWidth: number
    defaultCustomRadius: string
    defaultTabStyle: string
  }
}
