import request from '@/utils/http'
import { BaseResult, PaginationResult } from '@/types/axios'
import { RoleType } from '@/types/role'




export class RoleService {
    static getRoleList(params: any): Promise<PaginationResult<RoleType[]>>  {
        return request.get({
            url: "/api/role/get_role_list",
            params,
        })
    }
    static addRole(data: any): Promise<BaseResult>  {
        return request.post({
            url: "/api/role/add_role",
            data,
        })
    }
    static updateRole(data: any): Promise<BaseResult>  {
        return request.post({
            url: "/api/role/update_role",
            data,
        })
    }
    static deleteRole(data: any): Promise<BaseResult>  {
        return request.post({
            url: "/api/role/delete_role",
            data,
        })
    }

    static getRoleMenuList(params: any): Promise<PaginationResult<RoleType[]>>  {
        return request.get({
            url: "/api/role/get_role_menu_list",
            params,
        })
    }   
}