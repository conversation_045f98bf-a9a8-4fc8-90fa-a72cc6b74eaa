import { fourDotsSpinnerSvg } from '@/assets/svg/loading'

import { MenuListType, MenuListResp } from '@/types/menu'
import { processRoute } from '@/utils/menu'
import { ElLoading } from 'element-plus'
import request from '@/utils/http'
import { ApiStatus } from '@/utils/http/status';
import { BaseResult } from '@/types/axios'
import { useUserStore } from '@/store/modules/user'

// 菜单接口
export const menuService = { 
  async importMenu(data: any): Promise<BaseResult> {
    return request.post({
      url: "/api/menu/import_menu",
      data
    })
  },
  async exportMenu(): Promise<BaseResult> {
    return request.get({
      url: "/api/menu/export_menu"
    })
  },
  async deleteMenu(data: any): Promise<BaseResult> {
    return request.post({
      url: "/api/menu/delete_menu",
      data
    })
  },
  async addMenu(data: any): Promise<BaseResult> {
    return request.post({
      url: "/api/menu/add_menu",
      data
    })
  },
  async updateMenu(data: any): Promise<BaseResult> {
    return request.post({
      url: "/api/menu/update_menu",
      data
    })
  },
  // 获取菜单列表，模拟网络请求
  async getMenuList(
    delay: number = 300
  ): Promise<{ menuList: MenuListType[]; closeLoading: () => void }> {

    const resp = await request.get<MenuListResp>({
      url: "/api/menu/get_menu_list"
    })
    if (resp.code !== ApiStatus.success) {
       useUserStore().logOut()
       throw new Error('获取菜单列表失败，请重新登录！') 
    }
    const menuList = resp.payload;
    // 如果菜单列表为空，执行登出操作并跳转到登录页
    if (!Array.isArray(menuList) || menuList.length === 0) {
      useUserStore().logOut()
      throw new Error('获取菜单列表失败，请重新登录！')
    }

    const processedMenuList: MenuListType[] = menuList.map((route) => processRoute(route))
    // console.log("processedMenuList:", processedMenuList);
    const loading = ElLoading.service({
      lock: true,
      background: 'rgba(0, 0, 0, 0)',
      svg: fourDotsSpinnerSvg,
      svgViewBox: '0 0 40 40'
    })

    return new Promise((resolve) => {
      resolve({
        menuList: processedMenuList,
        closeLoading: () => loading.close()
      })
    })


    // 获取到的菜单数据
    // const menuList = asyncRoutes
    // // 处理后的菜单数据
    // const processedMenuList: MenuListType[] = menuList.map((route) => processRoute(route))
    // console.log("processedMenuList:", processedMenuList);
    // const loading = ElLoading.service({
    //   lock: true,
    //   background: 'rgba(0, 0, 0, 0)',
    //   svg: fourDotsSpinnerSvg,
    //   svgViewBox: '0 0 40 40'
    // })

    // return new Promise((resolve) => {
    //   setTimeout(() => {
    //     resolve({
    //       menuList: processedMenuList,
    //       closeLoading: () => loading.close()
    //     })
    //   }, delay)
    // })
  }
}
