import request from '@/utils/http'
import { BaseResult, PaginationResult } from '@/types/axios'

import { ConnectInfo, Device } from '@/types/device'




export class deviceService {
    static findDeviceList(params: any): Promise<PaginationResult<Device[]>>  {
        return request.get({
            url: "/api/device/list",
            params,
        })
    }
    static addDevice(data: any): Promise<BaseResult>  {
        return request.post({
            url: "/api/device/add",
            data,
        })
    }
    static updateDevice(data: any): Promise<BaseResult>  {
        return request.post({
            url: "/api/device/update",
            data,
        })
    }
    static deletDevice(data: any): Promise<BaseResult>  {
        return request.post({
            url: "/api/device/delete",
            data,
        })
    }

    static connectList(params: any): Promise<PaginationResult<ConnectInfo[]>>  {
        return request.get({
            url: "/api/device/connect/list",
            params,
        })
    }

    static downAttribute(data:any): Promise<BaseResult> {
        return request.post({
            url: "/api/device/down/attribute",
            data,
        })
    }

    static downCommand(data:any): Promise<BaseResult> {
        return request.post({
            url: "/api/device/down/command",
            data,
        })  
    }
}