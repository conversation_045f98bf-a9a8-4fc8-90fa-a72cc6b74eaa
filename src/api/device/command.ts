import request from '@/utils/http'
import { BaseResult, PaginationResult } from '@/types/axios'

import { Command } from '@/types/device'




export class commandService {
    static findCommandList(params: any): Promise<PaginationResult<Command[]>>  {
        return request.get({
            url: "/api/device/type/command/list",
            params,
        })
    }
    static addCommand(data: any): Promise<BaseResult>  {
        return request.post({
            url: "/api/device/type/command/add",
            data,
        })
    }
    static updatCommand(data: any): Promise<BaseResult>  {
        return request.post({
            url: "/api/device/type/command/update",
            data,
        })
    }
    static deletCommand(data: any): Promise<BaseResult>  {
        return request.post({
            url: "/api/device/type/command/delete",
            data,
        })
    }

    static history(params: any): Promise<PaginationResult<Command[]>> {
        return request.get({
            url:"/api/device/command/history",
            params
        })
    }

 
}