import request from '@/utils/http'
import { BaseResult, PaginationResult } from '@/types/axios'

import { AccessPoint } from '@/types/device'

export class AccessPointService {
  static findAccessPointList(params: any): Promise<PaginationResult<AccessPoint[]>> {
    return request.get({
      url: '/api/device/access_point/list',
      params
    })
  }
  static addAccessPoint(data: any): Promise<BaseResult> {
    return request.post({
      url: '/api/device/access_point/add',
      data
    })
  }
  static updateAccessPoint(data: any): Promise<BaseResult> {
    return request.post({
      url: '/api/device/access_point/update',
      data
    })
  }
  static deleteAccessPoint(data: any): Promise<BaseResult> {
    return request.post({
      url: '/api/device/access_point/delete',
      data
    })
  }
}
