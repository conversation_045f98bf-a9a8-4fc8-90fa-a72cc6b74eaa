import request from '@/utils/http'
import { BaseResult, PaginationResult } from '@/types/axios'

import { AuthInfo, Device, DeviceType } from '@/types/device'




export class typesService {
    static findDeviceTypeList(params: any): Promise<PaginationResult<DeviceType[]>>  {
        return request.get({
            url: "/api/device/type/list",
            params,
        })
    }
    static addDeviceType(data: any): Promise<BaseResult<AuthInfo>>  {
        return request.post({
            url: "/api/device/type/add",
            data,
        })
    }
    static updateDeviceType(data: any): Promise<BaseResult>  {
        return request.post({
            url: "/api/device/type/update",
            data,
        })
    }
    static deleteDeviceType(data: any): Promise<BaseResult>  {
        return request.post({
            url: "/api/device/type/delete",
            data,
        })
    }
    static deviceTypeAuthInfo(params:any): Promise<BaseResult<AuthInfo>>  {
        return request.get({
            url: "/api/device/type/auth_info",
            params,
        })
    }

    // 获取关联的设备列表
    static findDeviceTypeDeviceList(params:any): Promise<PaginationResult<Device[]>> {
        return request.get({
            url: "/api/device/type/device/list",
            params,
        })
    }
}