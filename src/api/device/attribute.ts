import request from '@/utils/http'
import { BaseResult, PaginationResult } from '@/types/axios'

import { Attribute } from '@/types/device'




export class attributeService {
    static findAttributeList(params: any): Promise<PaginationResult<Attribute[]>>  {
        return request.get({
            url: "/api/device/type/attribute/list",
            params,
        })
    }
    static addAttribute(data: any): Promise<BaseResult>  {
        return request.post({
            url: "/api/device/type/attribute/add",
            data,
        })
    }
    static updateAttribute(data: any): Promise<BaseResult>  {
        return request.post({
            url: "/api/device/type/attribute/update",
            data,
        })
    }
    static deleteAttribute(data: any): Promise<BaseResult>  {
        return request.post({
            url: "/api/device/type/attribute/delete",
            data,
        })
    }

    // 获取属性值历史值
    static history(params:any): Promise<BaseResult>  {
        return request.get({
            url: "/api/device/attribute/history",
            params,
        })
    }

}