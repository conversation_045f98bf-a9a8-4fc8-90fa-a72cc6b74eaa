import request from '@/utils/http'
import { BaseResult, PaginationResult } from '@/types/axios'

import { Event } from '@/types/device'




export class eventService {
    static findEventList(params: any): Promise<PaginationResult<Event[]>>  {
        return request.get({
            url: "/api/device/type/event/list",
            params,
        })
    }
    static addEvent(data: any): Promise<BaseResult>  {
        return request.post({
            url: "/api/device/type/event/add",
            data,
        })
    }
    static updatEvent(data: any): Promise<BaseResult>  {
        return request.post({
            url: "/api/device/type/event/update",
            data,
        })
    }
    static deletEvent(data: any): Promise<BaseResult>  {
        return request.post({
            url: "/api/device/type/event/delete",
            data,
        })
    }

    static history(params:any): Promise<PaginationResult<Event[]>>  {
        return request.get({
            url: "/api/device/event/history",
            params,
        })
    }
}