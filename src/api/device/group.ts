import request from '@/utils/http'
import { BaseResult, PaginationResult } from '@/types/axios'

import { GroupInfo } from '@/types/device'




export class groupService {
    static findGroupList(params: any): Promise<PaginationResult<GroupInfo[]>>  {
        return request.get({
            url: "/api/device/group/list",
            params,
        })
    }
    static addGroup(data: any): Promise<BaseResult>  {
        return request.post({
            url: "/api/device/group/add",
            data,
        })
    }
    static updateGroup(data: any): Promise<BaseResult>  {
        return request.post({
            url: "/api/device/group/update",
            data,
        })
    }
    static deletGroup(data: any): Promise<BaseResult>  {
        return request.post({
            url: "/api/device/group/delete",
            data,
        })
    }

}