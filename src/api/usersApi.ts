// import request from '@/utils/http'
// import { BaseResult } from '@/types/axios'
import AppConfig from '@/config'
import request from '@/utils/http'
import { BaseResult } from '@/types/axios'
import { UserInfo } from '@/types/store'
import {  CaptchResult , PaginationResult } from '@/types/axios'
import avatar from '@imgs/user/avatar.png'
import { CompanyType, SwitchCompanyResp } from '@/types/company'


export class UserService {
  static captcha(): Promise<CaptchResult> {
    return request.get({
      url: "/api/user/captcha",
      method: "get",
    });
  };
  
  // 模拟登录接口
  static login(options: { body: string }): Promise<BaseResult> {
    const data = JSON.parse(options.body)
    return request.post({
      url: "/api/user/login",
      data: data,
    })
  }

  // 用户注册
  static register(data: any):Promise<BaseResult> {
    return request.post({
      url: "/api/user/register",
      data
    })
  }

  // 获取用户信息
  static getUserInfo(): Promise<BaseResult<UserInfo>> {
    return request.get({
      url: "/api/user/user_info",
    })

  }
  // 添加用户
  static updateUserInfo(data: any):  Promise<BaseResult> {
    return request.post({
      url: "/api/user/update",
      data
    })
  }
  
  // 添加用户
  static addSubUser(data: any):  Promise<BaseResult> {

    return request.post({
      url: "/api/user/sub_create",
      data
    })
  }
  // 更新
  static updateSubUser(data: any) :    Promise<BaseResult> {
    return request.post({
      url: "/api/user/sub_update",
      data
    })
  }
  // 删除
  static deleteSubUser(data: any) :    Promise<BaseResult> {
    return request.post({
      url: "/api/user/sub_delete",
      data
    })
  }
  // 更新用户密码
  static updateUserPasswd(data:any ):  Promise<BaseResult> {
    return request.post({
      url: "/api/user/update_passwd",
      data
    })
  }

  // 用户列表
  static listSubUser(params: any) :  Promise<PaginationResult<UserInfo[]>> {
    return request.get({
      url: "/api/user/sub_list",
      params
    })
  }


  // 公司管理
  static listCompany(params: any) : Promise<PaginationResult<CompanyType[]>> {
    return request.get({
      url: "/api/user/company_list",
      params
    })
  }
  static addCompany(data: any): Promise<BaseResult> {
    return request.post({
      url: "/api/user/company_add",
      data
    })
  }
  static updateCompany(data:any): Promise<BaseResult> {
    return request.post({
      url: "/api/user/company_update",
      data
    })
  }
  static deleteCompany(data:any):  Promise<BaseResult> {
    return request.post({
      url: "/api/user/company_del",
      data
    })
  }
  // 查询有权限的公司
  static listAuthedCompany(params: any): Promise<PaginationResult<CompanyType[]>>  {
    return request.get({
      url: "/api/user/company_authed",
      params
    })
  }

  // 切换公司
  static companySwitch(params: any): Promise<BaseResult<SwitchCompanyResp>>  {
    return request.get({
      url: "/api/user/company_switch",
      params,
    })
  }
}
