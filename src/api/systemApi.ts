import request from '@/utils/http'
import { BaseResult, PaginationResult } from '@/types/axios'
import {  SystemLogType, UploadFileResp } from '@/types/system'
import { EnvConfig } from '@/config/types';



export class SystemService {
    static areaInfo(params: any):  Promise<BaseResult> {
        return request.get({
          url: "/api/system/area_info",
          method: "get",
          params,
        });
    };

    static areaChildrenList (params:any): Promise<BaseResult>  {
        return request.get({
          url: "/api/system/area_children_list",
          method: "get",
          params,
        });
    };

    // 公共上传文件
    static uploadFile(data:FormData):Promise<BaseResult<UploadFileResp>> {
      return request.post({
        url: "/api/system/upload_file",
        data,
      })
    }

    //
    static listLog(params:any):Promise<PaginationResult<SystemLogType[]>> {
      return request.get({
        url: "/api/system/list_log",
        params,
      })
    }

    // 获取系统配置
    static getSysConfig(): Promise<BaseResult<EnvConfig>> {
      return request.get({
        url: "/api/system/get_frontend_config"
      })
    }
    static setSysConfig(data:any): Promise<BaseResult> {
      return request.post({
        url: "/api/system/set_frontend_config",
        data
      })
    }
}
  