<template>
  <el-dialog
    v-model="dialogVisible"
    title="添加设备类型"
    width="960px"
    top="5vh"
    @close="handleClose()"
  >
  <div class="page-content">
    <el-form ref="formRef" :model="formData" :rules="rules" label-width="auto">
      <!-- 1. 自定义类型或模板类型 -->
      <ElRow :gutter="20">
        <ElCol
          :xs="24"
          :sm="20"
          :md="8"
          :lg="8"
          :xl="8"
          v-for="card in typeKindList"
          :key="card.template_kind"
        >
          <TypeKindCard
            :icon="card.icon"
            :title="card.title"
            :description="card.description"
            :iconSize="card.iconSize"
            :iconBgRadius="8"
            iconColor="#fff"
            :iconBgColor="card.iconBgColor"
            :showArrow="card.showArrow"
            :isSelected="formData.template_kind === card.template_kind"
            @click="card.clickHandler"
          />
        </ElCol>
      </ElRow>
      <ElRow :gutter="20" style="margin-top: 25px">
        <ElCol :xs="24" :sm="24" :md="22" :lg="22" :xl="20">
          <el-form-item label="类型名称" prop="name">
            <el-input v-model="formData.name">
              <template #prepend>
                <i class="iconfont-sys prepend-icon" v-html="formData.icon" @click="changeIcon"></i>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="设备接入类型" prop="access_type">
            <el-radio-group v-model="formData.access_type">
              <el-radio :value="item.value" v-for="(item, index) in accessTypeList" :key="index">
                {{ item.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="设备认证类型" prop="auth_type">
            <el-radio-group v-model="formData.auth_type">
              <el-radio :value="item.value" v-for="(item, index) in authTypeList" :key="index">
                {{ item.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="设备通信类型" prop="conn_type">
            <el-select v-model="formData.conn_type" placeholder="请选择">
              <el-option
                v-for="item in ConnTypeOptions"
                :key="item.key"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="选择接入点" prop="access_point_ids">
            <el-select v-model="selectAccessPointIds" multiple placeholder="请选择">
              <el-option
                v-for="item in accessPointList"
                :key="item.access_point_id"
                :label="item.name"
                :value="item.access_point_id"
              />
            </el-select>
            <span
              >接入点为设备不同的协议接入，以及编解码处理。
              <a :href="'/#' + RoutesAlias.DeviceNetwork">创建自定义接入点</a>
            </span>
          </el-form-item>
          <el-form-item label="描述" prop="desc">
            <el-input
              :autosize="{ minRows: 4, maxRows: 4 }"
              type="textarea"
              maxlength="1000"
              show-word-limit
              v-model="formData.desc"
            />
          </el-form-item>
          <el-form-item label="标签" prop="tags">
            <div class="tags-container">
              <el-row :gutter="20" v-for="(tag, index) in formData.tags" :key="index">
                <el-col :span="11">
                  <el-input v-model="tag.key" placeholder="标签key" />
                </el-col>
                <el-col :span="11">
                  <el-input v-model="tag.value" placeholder="标签值" />
                </el-col>
                <el-col :span="2">
                  <el-button
                    size="default"
                    :icon="Delete"
                    circle
                    @click="deleteTag(index)"
                  ></el-button>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24">
                  <!-- <el-button type="text" @click="addTag">+ 添加标签</el-button> -->
                  <el-link type="primary" @click="addTag" :underline="false">+ 添加标签</el-link>
                </el-col>
              </el-row>
            </div>
          </el-form-item>
        </ElCol>
      </ElRow>
      <el-form-item>
        <div class="form-footer">
          <el-button @click="handleCancel">取消</el-button>
          <el-button type="primary" @click="handleSubmit">提交</el-button>
        </div>
      </el-form-item>
    </el-form>
  </div>
  </el-dialog>
</template>

<script setup lang="ts">
  import { FormInstance, FormRules } from 'element-plus'
  import { router } from '@/router'
  import { Delete } from '@element-plus/icons-vue'
  import TypeKindCard from '@/components/custom/cards/TypeKindCard.vue'
  import { typesService } from '@/api/device/typesApi'
  import { ApiStatus } from '@/utils/http/status'
  import { accessTypeList } from '@/utils/device'
  import { useDeviceStore } from '@/store/modules/device'
  import { useWorktabStore } from '@/store/modules/worktab'
  import { AccessPointService } from '@/api/device/accessPoint'
  import { AccessPoint, DeviceType, KeyValue } from '@/types/device'
  import { RoutesAlias } from '@/router/modules/routesAlias'
  import { ConnTypeOptions } from "@/utils/device";

  const emit = defineEmits(['success'])
  const dialogVisible = ref(false)
  

  const selectAccessPointIds = ref([])
  const formRef = ref<FormInstance>()
  const formData = reactive({
    template_kind: 1,
    template_lib_id: '',
    name: '',
    icon: '&#xe784;',
    access_type: 1,
    auth_type: 1,  // 默认为1,即一型一密
    conn_type: '',
    access_point_ids: {} as Record<string, string>,
    desc: '',
    tags: [] as KeyValue[],
  })

  const rules = reactive<FormRules>({
    name: [
      { required: true, message: '请输入类型名称', trigger: 'blur' },
      { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
    ],
    auth_type: [{ required: true, message: '请选择认证类型', trigger: 'blur' }],
    access_type: [{ required: true, message: '请选择接入类型', trigger: 'blur' }],
    conn_type: [{ required: true, message: '请选择连接类型', trigger: 'blur' }],
    access_point_ids: [{ required: true, message: '请选择接入协议', trigger: 'blur' }]
  })

  const changeIcon = () => {
    console.log('change icon')
  }

  const authTypeList = ref([
    { value: 1, label: '一型一密' },
    { value: 2, label: '一机一密'},
  ])



  const accessPointList = ref<AccessPoint[]>([])

  const typeKindList = ref([
    {
      template_kind: 1,
      title: '创建自定义类型',
      description: '从零起步，根据需求添加功能定义、规则、任务等，适用于个性化硬件设备。',
      icon: '&#xe602;',
      iconSize: 20,
      iconBgColor: '#67C23A',
      showArrow: false,
      clickHandler: () => {
        // console.log("创建自定义类型");
        formData.template_kind = 1
      }
    },
    {
      template_kind: 2,
      title: '从模板库导入',
      description: '使用标准类型的功能定义，快速开发物联网设备和应用。',
      icon: '&#xe693;',
      iconSize: 20,
      iconBgColor: '#67C23A',
      showArrow: false,
      clickHandler: () => {
        formData.template_kind = 2
      }
    },
    {
      template_kind: 3,
      title: '从产品库快速导入',
      description: '使用发布到 ThingsCloud 公共产品库的设备类型，快速接入产品，开箱即用。',
      icon: '&#xe66e;',
      iconSize: 20,
      iconBgColor: '#67C23A',
      showArrow: false,
      clickHandler: () => {
        formData.template_kind = 3
      }
    }
  ])

  const deleteTag = (index: number) => {
    formData.tags.splice(index, 1)
  }

  const addTag = () => {
    formData.tags.push({ key: '', value: '' })
  }


  // const resetFormData = ()  => {
  //   formData = {
  //     template_kind: 1,
  //     template_lib_id: '',
  //     name: '',
  //     icon: '&#xe784;',
  //     access_type: 1,
  //     auth_type: 1,  // 默认为1,即一型一密
  //     conn_type: '',
  //     access_point_ids: {} as Record<string, string>,
  //     desc: '',
  //     tags: [] as KeyValue[],
  //   }
  // }


  const gotoList = () => {
    dialogVisible.value = false
    const devStore = useDeviceStore()
    devStore.setDeviceTypeIndexRefresh(true)
    devStore.setDeviceTypeInfo({} as DeviceType) // 清空设备类型详情信息
    useWorktabStore().removeTab('/device/types/new')
    router.push({ path: '/device/types' })
  }

  const handleClose = () => {
    dialogVisible.value = false
  }

  const handleCancel = () => {
    gotoList()
  }

  const handleSubmit = async () => {
    if (!formRef.value) return
    await formRef.value.validate(async (valid) => {
      if (valid) {
        if (formData.template_kind === 2 || formData.template_kind === 3) {
          if (formData.template_lib_id === '') {
            ElMessage.error('请选择模板或产品库选择模板')
            return
          }
        }
        // 将选中的接入点id转为map并存入formData中
        formData.access_point_ids = selectAccessPointIds.value.reduce(
          (acc, key) => {
            acc[key] = key // 默认值
            return acc
          },
          {} as Record<string, string>
        )

        let res = await typesService.addDeviceType(formData)
        if (res.code === ApiStatus.success) {
          ElMessage.success('添加成功!')
          // gotoList()
          emit("success")
          handleClose()
        } else {
          ElMessage.error(res.message)
        }
      }
    })
  }

onMounted(async () => {
  let params = { page: 1, page_size: 100 }
  let resp = await AccessPointService.findAccessPointList(params)
  // console.log(resp)
  if (resp.code === ApiStatus.success) {
    accessPointList.value = resp.payload.list
  }
})

const show = () => {
  dialogVisible.value = true
}

defineExpose({
  show,
})
</script>

<style lang="scss" scoped>
  .prepend-icon {
    font-size: 25px;
    color: rgb(var(--art-primary));
  }

  .prepend-icon:hover {
    cursor: pointer;
  }

  .tags-container {
    gap: 12px; // 添加行间距
    // display: flex;
    // flex-direction: column;
    width: 100vw;
  }

  .form-footer {
    display: flex;
    justify-content: center; // 添加这行使内容水平居中
    width: 100%; // 确保占据全部宽度
    margin-top: 20px; // 可选：添加一些顶部间距
  }
</style>
