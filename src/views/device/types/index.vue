<template>
  <div class="page-content">
    <el-row :gutter="20">
      <el-col :sm="24" :md="24" :lg="24">
        <my-table-bar
          :showTop="true"
          @search="search"
          @reset="resetForm(searchFormRef)"
          :layout="'search, refresh'"
        >
          <template #top>
            <el-form :model="searchForm" ref="searchFormRef" label-width="82px">
              <el-row :gutter="20">
                <art-form-input label="设备名称" prop="name" v-model="searchForm.name" />
              </el-row>
            </el-form>
          </template>
          <template #bottom>
            <el-button @click="newDeviceType" size="default" v-ripple>添加设备类型</el-button>
          </template>
        </my-table-bar>
      </el-col>
    </el-row>
    <el-divider />
      <div class="card-container">
        <el-space wrap>
        <!-- <div class="card-grid"> -->
          <el-card v-for="(item, index) in deviceTypeList" 
                :key="index" 
                class="box-card"
                @click="handleCardClick($event, item)"
              >
                <template #header>
                  <div class="card-header">
                      <!-- <span>{{ getTypeName(item.type) }}</span> -->
                      {{ item.name }}
                      <div  class="enable-class">
                          <div  v-if="item.status === 1">
                              <div class="online-class"></div>
                              <el-link type="primary" :underline="false" class="link-btn"  @click="handleDisadabled(item)">启用</el-link>
                          </div>
                          <div v-else>
                              <div class="offline-class"></div>
                              <el-link type="primary" :underline="false" class="link-btn"  @click="handleEndabled(item)">禁用</el-link>
                          </div>
                          
                      </div>
                  </div>
              </template>
              <!-- 卡片内容 -->
              <div class="card-content">
                  <!-- 这里可以放置卡片的具体内容 -->
                  <el-row>
                    <el-col :span="18">
                      <p class="text-p">类型ID：{{ item.device_type_id }}</p>
                      <p class="text-p">设备类型: {{ item?.name }}</p>
                      <p class="text-p">接入类型: {{ accessTypeToLabel(item.access_type) }}</p>
                      <p class="text-p">通信类型: {{ item.conn_type }}</p>
                      <p v-if="item.tags?.length  > 0 " class="card-tag-class">
                        <span v-for="tag in item.tags">
                          <el-tag type="info" v-if="tag.value !== ''">{{ tag.value }}</el-tag>
                          <el-tag type="info" v-if="tag.value === ''">无标签</el-tag>
                        </span>
                      </p>
                      <p v-else class="card-tag-class"><span><el-tag type="info">无标签</el-tag></span></p>
                    </el-col>
                  </el-row>
                  
              </div>

              <template #footer>
                  <div class="footer-div">
                      <el-link type="primary" size="small" class="footer-btn" :underline="false" @click="handleEdit(item)">编辑</el-link>
                      <el-divider direction="vertical" />
                      <el-link type="danger" size="small" class="footer-btn" :underline="false" @click="handleDelete(item)">删除</el-link>
                      <el-divider direction="vertical" />
                      <el-link type="success" size="small" class="footer-btn" :underline="false" @click="handleDetail(item)">详情</el-link>
                  </div>
            </template>
          </el-card>
        </el-space>
        <!-- </div> -->
        <div class="pagination-container" v-if="total > pageSize">
            <el-pagination
                v-model:current-page="currentPage"
                v-model:page-size="pageSize"
                :page-sizes="[10, 20, 50, 100]"
                :background="true"
                layout="total, sizes, prev, pager, next"
                :total="total"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>
      </div>
    <AddUpdate ref="addUpdateRef" @success="initData" />
    <New ref="newRef" @success="initData" />
  </div>
</template>

<script lang="ts" setup>
  import { typesService } from '@/api/device/typesApi'
  import { DeviceType } from '@/types/device'
  import { ApiStatus } from '@/utils/http/status';
  import { Status, accessTypeToLabel } from "@/utils/device";
  import AddUpdate from './detail/widget/addUpdate.vue';
  import New from './new.vue';
  import { FormInstance } from 'element-plus'
  import { router } from '@/router'
  import { useDeviceStore } from '@/store/modules/device'


  const deviceStore = useDeviceStore()

  const addUpdateRef = ref<InstanceType<typeof AddUpdate>>()
  const newRef = ref<InstanceType<typeof New>>()

    // 分页相关变量
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

const searchForm = ref({
    name: '',
    page: currentPage.value,
    page_size: pageSize.value,
})
  const search = async () => {
    let params = { ...searchForm.value }
    let resp = await typesService.findDeviceTypeList(params)
    if (resp.code === ApiStatus.success) {
      deviceTypeList.value = resp.payload.list
      total.value = resp.payload.total
    }
  }


  const handleSizeChange = async (val: number) => {
    pageSize.value = val
    searchForm.value.page_size = val
    await search()
  }

  const handleCurrentChange = async (val: number) => {
    currentPage.value = val
    searchForm.value.page = val
    await search()
  } 



  const newDeviceType = () => {
    // router.push({ path: '/device/types/new' })
    nextTick(() => {
      newRef.value?.show()
    })
  }


  const searchFormRef = ref<FormInstance>()
  const resetForm = (formEl: FormInstance | undefined) => {
    if (!formEl) return
    formEl.resetFields()
  }

  const handleDisadabled = async (card: DeviceType) => {
    card.status = Status.Disabled;
    let resp = await typesService.updateDeviceType(card)
    if (resp.code === ApiStatus.success) {
      ElMessage.success('禁用成功')
      initData()
    } else {  
      ElMessage.error("禁用失败:" + resp.message)
    }
  }

  const handleEndabled =async (card: DeviceType) => {
    card.status = Status.Enabled;
    let resp = await typesService.updateDeviceType(card)
    if (resp.code === ApiStatus.success) {
      ElMessage.success('启用成功 ')
      initData()
    } else {  
      ElMessage.error("启用失败:" + resp.message)
    }
  }
  const handleCardClick = (event: MouseEvent, card: DeviceType) => {
    // 检查点击是否来自卡片内部的操作按钮
    const target = event.target as HTMLElement;
    if (
      target.closest('.footer-div') || 
      target.closest('.enable-class') ||
      target.closest('.link-btn')
    ) {
      return; // 如果是操作按钮区域，不处理点击
    }
    handleDetail(card);
  }

  const handleDetail = (card: DeviceType) => {
    deviceStore.setDeviceTypeInfo(card)
    router.push({ path: '/device/types/detail' })
  }

  const handleEdit = (card: DeviceType) => {
    nextTick(() => {
      addUpdateRef.value?.showDialog(card)
    })
  }

  const handleDelete = async (card: DeviceType) => {
    ElMessageBox.confirm(`确定要删除 ${card.name} 吗?`, '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(async () => {
      let resp  = await  typesService.deleteDeviceType({ device_type_id: card.device_type_id})
      if (resp.code === ApiStatus.success) {
        ElMessage.success('删除成功')
        initData()
      } else {
        ElMessage.error("删除失败:" + resp.message)
      }
    })
  } 

  onMounted(async () => {
    await initData()
  })

  const deviceTypeList = ref<DeviceType[]>([
    // {
    //   id: 5,
    //   name: '物联网设备',
    //   access_type: "直连设备",
    //   device_count: 20,
    //   icon: "&#xe784;",
    //   type_id: '',
    //   created_at: ''
    // }
  ])

  const pageParams = ref({ page_size: 20, page: 1 })
  const initData = async () => {
    let params = { ...pageParams.value }
    let resp = await typesService.findDeviceTypeList(params)
    if (resp.code === ApiStatus.success) {
      deviceTypeList.value = resp.payload.list
      total.value = resp.payload.total
    }
  }


  watch(
    () => deviceStore.deviceTypeIndexRefresh,
    (newVal:boolean) => {
      if (newVal) {
        initData()
        deviceStore.setDeviceTypeIndexRefresh(false)
      }
    }
  )
</script>


<style scoped>
  /* 添加以下样式防止点击事件冒泡 */


.card-container {
  /* display: flex; */
  /* justify-content: center; */
  width: 100%;
}

.card-grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 20px;
  max-width: 100vw; /* 根据你的需求调整 */
  margin: 0 auto;
}

.box-card {
  width: 400px;
  height:  320px;
}

.box-card:hover {
  cursor: pointer;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
  transform: translateY(-1px);
}


.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}


.footer-div {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.footer-btn {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  width: 100px;   /*设置一个固定宽度*/
  text-align: center;
}


.enable-class {
    float: right;
}



.online-class {
  background: #67C23A; 
  border-radius: 50%;
  width: 10px;
  height: 10px;
  float:left;
  margin-top: 3px;
}

.offline-class {
  background: #F56C6C;
  border-radius: 50%;
  width: 10px;
  height: 10px;
  float: left;
  margin-top: 3px;
}

.link-btn {
  float: right;
  width: 30px;
  height: 10px;
  margin-top: 1px;
  margin-left: 10px;
}

.no-border-card {
  border: none !important;
  box-shadow: none !important; /* 如果有阴影的话也一并去除 */
}


.card-tag-class {
  margin-top: 10px;
}
</style>

<style lang="scss" scoped>
  .page-title {
    font-size: 30px;
  }
</style>
