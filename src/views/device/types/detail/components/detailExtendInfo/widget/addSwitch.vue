<template>
    <el-dialog
        v-model="dialogVisible"
        :title="title"
        :width="dialogWidth"
        @close="closeDialog"
    >
        <el-form   :model="formData"  ref="formRef"   :rules="rules" label-width="auto" >
            <el-form-item label="名字" prop="name">
                <el-input v-model="formData.name" placeholder="请输入名字"></el-input>
            </el-form-item>
            <el-form-item label="标识符" prop="identifier">
                <el-input v-model="formData.identifier" placeholder="请输入标识符"></el-input>
            </el-form-item>
            <div>
              <el-form-item label="开关值类型" prop="value_type">
                <el-select v-model="dataOption.value_type" placeholder="请选择开关值类型">
                  <el-option v-for="item in switchOptions"
                    :key="item.key"
                    :label="item.label"
                    :value="item.value_type">
                    <span style="float: left">{{ item.label }}</span>
                    <span style="float: right;color: var(--el-text-color-secondary);font-size: 13px;">
                      {{ item.value_label }}
                    </span>
                  </el-option>
                </el-select>
              </el-form-item>
              <el-row>
                <el-col :span="11">
                  <el-form-item :label="switchOnName()" prop="on_text">
                    <el-input v-model="dataOption.on_text" placeholder=""></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="2"></el-col>
                <el-col :span="11">
                  <el-form-item :label="switchOffName()" prop="off_text">
                    <el-input v-model="dataOption.off_text" placeholder=""></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
            
            <el-form-item label="描述" prop="desc">
                <el-input 
                    :rows="3"
                    type="textarea"
                    v-model="formData.desc" 
                    placeholder="请输入描述" >
                </el-input>
            </el-form-item>
        </el-form>
        <template #footer>
            <div class="dialog-footer">
                <el-button @click="closeDialog">取消</el-button>
                <el-button type="primary" @click="handleSubmit">确定</el-button>
            </div>
        </template>
    </el-dialog>
</template>


<script setup lang="ts">
import { useDeviceStore } from '@/store/modules/device'
import { ExtendInfo, SwitchDataOption } from '@/types/device'
import { FormInstance, FormRules } from 'element-plus'


const emit = defineEmits(['success'])

const dialogVisible = ref(false)
const title = ref('添加开关')
const dialogWidth = ref('750px')

const formRef = ref<FormInstance>()

const dataOption = ref<SwitchDataOption>({
    value_type: '',
    on_text: '',
    off_text: ''
})


const formData = ref<ExtendInfo>({
    name: "",
    identifier: '',
    data_type: 'switch',
    data_options: { } as SwitchDataOption,
    enabled: true,
    desc: "",
    create_time: 0,
    is_standard: false,
})

const rules = reactive<FormRules>({
    name: [
      { required: true, message: '请输入名称', trigger: 'blur' },
      { min: 1, max: 20, message: '长度在 1 到 20 个字符', trigger: 'blur' }
    ],
    identifier: [{ required: true, message: '请输入参数标识符', trigger: 'blur'}], 
  })

  const resetFormData = () => {
    formData.value = {
        name: "",
        identifier: '',
        data_type: 'switch',
        data_options: { } as SwitchDataOption,
        enabled: true,
        desc: "",
        create_time: 0,
        is_standard: false,
    }
}

  const switchOptions = ref([
    { key: 1,  value_type: 'boolean', value_label: "Boolean",  label: 'True/False' },
    { key: 2,  value_type: 'number',  value_label: "Number", label: '1/0'},
    { key: 3,  value_type: 'text',    value_label: "Text",  label: 'On/Off'}
  ])

  const switchOnName = () => {
    if (dataOption.value.value_type === 'boolean') {
      return "True 名称" 
    } else if (dataOption.value.value_type === 'number') {
      return "1 名称" 
    } else {
      return "On 名称" 
    }
  }

  const switchOffName = () => {
    if (dataOption.value.value_type === 'boolean') {
      return "False 名称" 
    } else if (dataOption.value.value_type === 'number') {
      return "0 名称" 
    } else {
      return "Off 名称" 
    }
  }



const closeDialog = () => {
    resetFormData()
    dialogVisible.value = false 
}

const deviceStore = useDeviceStore()

const handleSubmit = async () => {
    if (!formRef.value) return;  
    await formRef.value.validate(async (valid) => {
        if (!valid) return;
        formData.value.data_options = dataOption.value;
        let devTypeInfo = deviceStore.getDeviceTypeInfo();
        devTypeInfo.extend_info.push(formData.value)
        deviceStore.setDeviceTypeInfo(devTypeInfo)
        emit('success')
        closeDialog()
    })
}


const show = () => {
    dialogVisible.value = true
}

defineExpose({
    show,
})
</script>