<template>
    <el-dialog
        v-model="dialogVisible"
        :title="title"
        :width="dialogWidth"
        @close="closeDialog"
    >
        <el-form   :model="formData"  ref="formRef"   :rules="rules" label-width="auto" >
            <el-form-item label="名字" prop="name">
                <el-input v-model="formData.name" placeholder="请输入名字"></el-input>
            </el-form-item>
            <el-form-item label="标识符" prop="identifier">
                <el-input v-model="formData.identifier" placeholder="请输入标识符"></el-input>
            </el-form-item>
            <div>
              <el-row>
                <el-col :span="24">
                  <el-form-item label="单位" prop="unit">
                    <el-input v-model="dataOption.unit" placeholder="请输入单位"></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="11">
                  <el-form-item label="默认值" prop="default">
                    <el-input v-model="dataOption.default" placeholder="请输入默认值"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="2"></el-col>
                <el-col :span="11">
                  <el-form-item label="精度" prop="step">
                    <el-select v-model="dataOption.step" placeholder="请选择精度">
                      <el-option label="1" value="1" />
                      <el-option label="0.1" value="0.1" />
                      <el-option label="0.01" value="0.01" />
                      <el-option label="0.001" value="0.001" />
                      <el-option label="0.0001" value="0.0001" />
                      <el-option label="0.00001" value="0.00001" />
                      <el-option label="0.000001" value="0.000001" />
                    </el-select>
                  </el-form-item>
                </el-col>   
              </el-row>
              <el-row>
                <el-col :span="11">
                  <el-form-item label="最大值" prop="max">
                    <el-input v-model="dataOption.max" placeholder="请输入最大值"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="2"></el-col>
                <el-col :span="11">
                  <el-form-item label="最小值" prop="min">
                    <el-input v-model="dataOption.min" placeholder="请输入最小值"></el-input>
                  </el-form-item>
                </el-col>   
              </el-row>
            </div>
            <el-form-item label="描述" prop="desc">
                <el-input 
                    :rows="3"
                    type="textarea"
                    v-model="formData.desc" 
                    placeholder="请输入描述" >
                </el-input>
            </el-form-item>
        </el-form>
        <template #footer>
            <div class="dialog-footer">
                <el-button @click="closeDialog">取消</el-button>
                <el-button type="primary" @click="handleSubmit">确定</el-button>
            </div>
        </template>
    </el-dialog>
</template>


<script setup lang="ts">
import { typesService } from '@/api/device/typesApi'
import { useDeviceStore } from '@/store/modules/device'
import { ExtendInfo, NumberDataOption, Param, TextDataOption } from '@/types/device'
import { ApiStatus } from '@/utils/http/status'
import { FormInstance, FormRules } from 'element-plus'


const emit = defineEmits(['success'])

const dialogVisible = ref(false)
const title = ref('添加数字')
const dialogWidth = ref('750px')

const formRef = ref<FormInstance>()

const dataOption = ref<NumberDataOption>({
    default: '',
    unit: '',
    step: '',
    max: '',
    min: ''
})

const formData = ref<ExtendInfo>({
    name: "",
    identifier: '',
    data_type: 'number',
    data_options: {} as NumberDataOption,
    enabled: true,
    desc: "",
    create_time: 0,
    is_standard: false,
})

const resetFormData = () => {
    formData.value = {
        name: "",
        identifier: '',
        data_type: 'number',
        data_options: {} as NumberDataOption,
        enabled: true,
        desc: "",
        create_time: 0,
        is_standard: false
    }
}

const rules = reactive<FormRules>({
    name: [
      { required: true, message: '请输入名称', trigger: 'blur' },
      { min: 1, max: 20, message: '长度在 1 到 20 个字符', trigger: 'blur' }
    ],
    identifier: [{ required: true, message: '请输入参数标识符', trigger: 'blur'}], 
  })





const closeDialog = () => {
    resetFormData()
    dialogVisible.value = false 
}

const deviceStore = useDeviceStore()

const handleSubmit = async () => {
    if (!formRef.value) return;  
    await formRef.value.validate(async (valid) => {
        if (!valid) return;
        formData.value.data_options = dataOption.value;
        let devTypeInfo = deviceStore.getDeviceTypeInfo();
        devTypeInfo.extend_info.push(formData.value)
        deviceStore.setDeviceTypeInfo(devTypeInfo)
        emit('success')
        closeDialog()
    })
}


const show = () => {
    dialogVisible.value = true
}

defineExpose({
    show,
})
</script>