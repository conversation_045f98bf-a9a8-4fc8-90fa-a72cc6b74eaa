<template>
    <el-dialog
        v-model="dialogVisible"
        :title="title"
        :width="dialogWidth"
        @close="closeDialog"
    >
        <el-form   :model="formData"  ref="formRef"   :rules="rules" label-width="auto" >
            <el-form-item label="名字" prop="name">
                <el-input v-model="formData.name" placeholder="请输入名字"></el-input>
            </el-form-item>
            <el-form-item label="标识符" prop="identifier">
                <el-input v-model="formData.identifier" placeholder="请输入标识符"></el-input>
            </el-form-item>
            <el-form-item label="默认值" prop="default">
                <el-date-picker
                    v-model="dataOption.default"
                    type="date"
                    placeholder="选择日期"
                    format="YYYY/MM/DD"
                    value-format="YYYY-MM-DD"
                />
            </el-form-item>
            <el-form-item label="描述" prop="desc">
                <el-input 
                    :rows="3"
                    type="textarea"
                    v-model="formData.desc" 
                    placeholder="请输入描述" >
                </el-input>
            </el-form-item>
        </el-form>
        <template #footer>
            <div class="dialog-footer">
                <el-button @click="closeDialog">取消</el-button>
                <el-button type="primary" @click="handleSubmit">确定</el-button>
            </div>
        </template>
    </el-dialog>
</template>


<script setup lang="ts">
import { typesService } from '@/api/device/typesApi'
import { useDeviceStore } from '@/store/modules/device'
import { ExtendInfo, Param, TextDataOption } from '@/types/device'
import { ApiStatus } from '@/utils/http/status'
import { FormInstance, FormRules } from 'element-plus'


const emit = defineEmits(['success'])

const dialogVisible = ref(false)
const title = ref('添加日期')
const dialogWidth = ref('750px')

const formRef = ref<FormInstance>()

const dataOption = ref<TextDataOption>({
    default: ''
})

const formData = ref<ExtendInfo>({
    name: "",
    identifier: '',
    data_type: 'date',
    data_options: { default: ""} as TextDataOption,
    enabled: true,
    desc: "",
    create_time: 0,
    is_standard: false,
})

const rules = reactive<FormRules>({
    name: [
      { required: true, message: '请输入名称', trigger: 'blur' },
      { min: 1, max: 20, message: '长度在 1 到 20 个字符', trigger: 'blur' }
    ],
    identifier: [{ required: true, message: '请输入参数标识符', trigger: 'blur'}], 
  })

const resetFormData = () => {
    formData.value = {
        name: "",
        identifier: '',
        data_type: 'date',
        data_options: { } as TextDataOption,
        enabled: true,
        desc: "",
        create_time: 0,
        is_standard: false,
    }
}



const closeDialog = () => {
    resetFormData()
    dialogVisible.value = false 
}

const deviceStore = useDeviceStore()

const handleSubmit = async () => {
    if (!formRef.value) return;  
    await formRef.value.validate(async (valid) => {
        if (!valid) return;
        formData.value.data_options = dataOption.value;
        let devTypeInfo = deviceStore.getDeviceTypeInfo();
        devTypeInfo.extend_info.push(formData.value)
        deviceStore.setDeviceTypeInfo(devTypeInfo)
        emit('success')
        closeDialog()
    })
}


const show = () => {
    dialogVisible.value = true
}

defineExpose({
    show,
})
</script>