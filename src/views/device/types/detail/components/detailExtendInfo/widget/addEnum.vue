<template>
    <el-dialog
        v-model="dialogVisible"
        :title="title"
        :width="dialogWidth"
        @close="closeDialog"
    >
        <el-form   :model="formData"  ref="formRef"    label-position="top"  :rules="rules" label-width="auto" >
            <el-form-item label="名字" prop="name">
                <el-input v-model="formData.name" placeholder="请输入名字"></el-input>
            </el-form-item>
            <el-form-item label="标识符" prop="identifier">
                <el-input v-model="formData.identifier" placeholder="请输入标识符"></el-input>
            </el-form-item>
            <div  style="margin-bottom: 15px;">
              <el-row>
                <el-col  :span="11">枚举值</el-col>
                <el-col  :span="11">枚举描述</el-col>
                <el-col  :span="2"></el-col>
              </el-row>
              <el-row :gutter="20" v-for="(tag, index) in enumTmpList" :key="index" style="margin-top: 5px;">
                <el-col :span="11">
                  <el-input v-model="tag.key" placeholder="枚举名称" />
                </el-col>
                <el-col :span="10">
                  <el-input v-model="tag.value" placeholder="枚举描述" />
                </el-col>
                <el-col :span="3">
                  <el-button  size="default" :icon="Delete" circle  @click="deleteEnumItem(index)"/>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24">
                  <!-- <el-button type="text" @click="addEnumItem">+ 添加枚举项</el-button> -->
                  <el-link type="primary" :underline="false" @click="addEnumItem">+ 添加枚举项</el-link>
                </el-col>
              </el-row>
            </div>
            <el-form-item label="描述" prop="desc">
                <el-input 
                    :rows="3"
                    type="textarea"
                    v-model="formData.desc" 
                    placeholder="请输入描述" >
                </el-input>
            </el-form-item>
        </el-form>
        <template #footer>
            <div class="dialog-footer">
                <el-button @click="closeDialog">取消</el-button>
                <el-button type="primary" @click="handleSubmit">确定</el-button>
            </div>
        </template>
    </el-dialog>
</template>


<script setup lang="ts">
import { useDeviceStore } from '@/store/modules/device'
import { EnumDataOption, ExtendInfo  } from '@/types/device'
import { Delete } from '@element-plus/icons-vue'
import { FormInstance, FormRules } from 'element-plus'


const emit = defineEmits(['success'])

const dialogVisible = ref(false)
const title = ref('添加选项')
const dialogWidth = ref('750px')

const formRef = ref<FormInstance>()

const dataOption = ref<EnumDataOption>({
    enum_map: {} as { [key: string]: string },
})

const formData = ref<ExtendInfo>({
    name: "",
    identifier: '',
    data_type: 'enum',
    data_options: { } as EnumDataOption,
    enabled: true,
    desc: "",
    create_time: 0,
    is_standard: false,
})

const rules = reactive<FormRules>({
    name: [
      { required: true, message: '请输入名称', trigger: 'blur' },
      { min: 1, max: 20, message: '长度在 1 到 20 个字符', trigger: 'blur' }
    ],
    identifier: [{ required: true, message: '请输入参数标识符', trigger: 'blur'}], 
  })

const resetFormData = () => {
    formData.value = {
        name: "",
        identifier: '',
        data_type: 'enum',
        data_options: { } as EnumDataOption,
        enabled: true,
        desc: "",
        create_time: 0,
        is_standard: false,
    }
}


const enumTmpList = ref([{ key: '', value: '' }])
const deleteEnumItem = (index: number) => {
    enumTmpList.value.splice(index, 1)
}

const addEnumItem = () => {
    enumTmpList.value.push({ key: '', value: '' })
}

const enumTmpListToEnumMap = () => {
    let tmp_map = {} as { [key: string]: string };
    enumTmpList.value.forEach((item) => {
        tmp_map[item.key] = item.value
    })
    return tmp_map;
}

const closeDialog = () => {
    resetFormData()
    dialogVisible.value = false 
}

const deviceStore = useDeviceStore()

const handleSubmit = async () => {
    if (!formRef.value) return;  
    await formRef.value.validate(async (valid) => {
        if (!valid) return;
        formData.value.data_options = enumTmpListToEnumMap();
        let devTypeInfo = deviceStore.getDeviceTypeInfo();
        devTypeInfo.extend_info.push(formData.value)
        deviceStore.setDeviceTypeInfo(devTypeInfo)
        emit('success')
        closeDialog()
    })
}


const show = () => {
    dialogVisible.value = true
}

defineExpose({
    show,
})
</script>