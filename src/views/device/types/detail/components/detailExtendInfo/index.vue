<template>
    <div class="function-definition-container">
        <div class="text-h3">编辑扩展信息</div>
        <p class="description">
            为该类型下的设备定义扩展信息，以满足各业务场景的设备管理需求。常见的扩展信息例如：设备位置、维护人、到期时间、合同编号、所属单位等。
        </p>
        <el-table :data="extendInfos"  style="width: 100%">
            <el-table-column prop="name" label="名称"  />
            <el-table-column prop="data_type" label="类型">
                <template #default="scope">
                    <span>{{ genInfoType(scope.row) }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="options" label="默认值">
                <template #default="scope">
                    <span>{{ scope.row.data_options?.default }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="desc" label="描述"  />
            <el-table-column prop="enabled" label="启用"  >
                <template #default="scope">
                    <el-switch 
                        v-model="scope.row.enabled"   
                        @change="handleChange"
                        :active-value="true"
                        :inactive-value="false"
                        active-color="#13ce66" 
                        inactive-color="#ff4949" 
                    />
                </template>
            </el-table-column>
        
            <el-table-column label="操作" width="120">
              <template #default="scope">
                <!-- <el-button link type="primary" size="small" >编辑</el-button> -->
                <el-button link type="danger" size="small" @click="deleteExtendInfo(scope.row)">删除</el-button>
              </template>
            </el-table-column>
        </el-table>
        
        <!-- <div> -->
            <!-- <el-button type="primary" size="default"  plain @click="addExtendInfo">添加扩展信息</el-button> -->
        <!-- </div> -->
        <div  class="add-button">
            <el-dropdown >
                <span class="el-dropdown-link">
                    <el-button type="primary" size="default" :icon="Plus" plain >添加扩展信息
                        <el-icon class="el-icon--right">
                            <arrow-up />
                        </el-icon>
                        
                    </el-button>
                </span>
                <template #dropdown>
                <el-dropdown-menu>
                    <el-dropdown-item @click="addStringInfo">
                        <el-icon><Document /></el-icon>文字
                    </el-dropdown-item>
                    <el-dropdown-item @click="addNumberInfo">
                        <i class="iconfont-sys iconsys-jisuanqi_2"></i>数值
                    </el-dropdown-item>
                    <el-dropdown-item @click="addSwitchInfo">
                        <el-icon><TurnOff /></el-icon>开关
                    </el-dropdown-item>
                    <el-dropdown-item @click="addEnumInfo">
                        <el-icon><Operation /></el-icon>选项
                    </el-dropdown-item>
                    <el-dropdown-item divided  @click="addDateInfo">
                        <el-icon><Calendar /></el-icon>日期
                    </el-dropdown-item>
                </el-dropdown-menu>
                </template>
            </el-dropdown>
        </div>

        <AddText ref="addTextRef" @success="updateDeviceType" />
        <AddNumber ref="addNumberRef" @success="updateDeviceType" />
        <AddSwitch ref="addSwitchRef" @success="updateDeviceType" />
        <AddEnum ref="addEnumRef" @success="updateDeviceType" />
        <AddDate ref="addDateRef" @success="updateDeviceType" />

    </div>
</template>

<script setup lang="ts">
import { ExtendInfo } from '@/types/device';
import { Plus } from '@element-plus/icons-vue'
import AddText from './widget/addText.vue';
import AddNumber from './widget/addNumber.vue';
import AddSwitch from './widget/addSwitch.vue';
import AddEnum from './widget/addEnum.vue';
import AddDate from './widget/addDate.vue';
import { useDeviceStore } from '@/store/modules/device';
import { typesService } from '@/api/device/typesApi';
import { ApiStatus } from '@/utils/http/status';

const deviceStore  = useDeviceStore()
const deviceTypeInfo = computed(() => deviceStore.deviceTypeInfo); 

const addTextRef = ref<InstanceType<typeof AddText>>();
const addNumberRef = ref<InstanceType<typeof AddNumber>>();
const addSwitchRef = ref<InstanceType<typeof AddSwitch>>();
const addEnumRef = ref<InstanceType<typeof AddEnum>>();
const addDateRef = ref<InstanceType<typeof AddDate>>();


const standardInfo = ref<ExtendInfo[]>([
    {
        name: '设备位置',
        identifier: 'location',
        data_type: 'location',
        data_options: { default: "" },
        enabled: false,
        desc: '',
        create_time: 0,
        is_standard: true,
    },
    {
        name:"物联卡ICCID",
        identifier: "iccid",
        data_type: 'string',
        data_options: { default: "" },
        enabled: false,
        desc: '',
        create_time: 0,
        is_standard: true,
    },
]);

const extendInfos = ref<ExtendInfo[]>([]);

const genInfoType = (row: ExtendInfo) => {
    if (row.data_type === 'string') {
        return '文字'
    } else if (row.data_type === 'number') {
        return '数字'
    } else if (row.data_type === 'switch') {
        return '开关'
    } else if (row.data_type === 'enum') {
        return '选项'
    } else if (row.data_type === 'date') {
        return '日期'
    } else if (row.data_type === 'time') {
        return '时间'
    } else if (row.data_type === 'location') {
        return '位置'
    } else {
        return "未知"
    }
}

const handleChange =async () => {
    let devTypeInfo = useDeviceStore().getDeviceTypeInfo();
    if (devTypeInfo.extend_info === undefined || devTypeInfo.extend_info === null) {
        devTypeInfo.extend_info = []
    }
    devTypeInfo.extend_info = extendInfos.value;
    deviceStore.setDeviceTypeInfo(devTypeInfo);
    await updateDeviceType();
}

const updateDeviceType = async () => {
    let devTypeInfo = useDeviceStore().getDeviceTypeInfo();
    if (devTypeInfo.extend_info === undefined || devTypeInfo.extend_info === null) {
        devTypeInfo.extend_info = []
    }
    let resp =  await typesService.updateDeviceType(devTypeInfo)
    if (resp.code === ApiStatus.success) {
        // ElMessage.success('更新成功')
        refreshExtendInfo();
    } else {
        ElMessage.error("更新失败")
    }     
} 

const deleteExtendInfo = async (row: ExtendInfo) => {
    let devTypeInfo = useDeviceStore().getDeviceTypeInfo();
    if (devTypeInfo.extend_info === undefined || devTypeInfo.extend_info === null) {
        devTypeInfo.extend_info = []
    }
    devTypeInfo.extend_info = devTypeInfo.extend_info.filter((item) => item.identifier !== row.identifier);
    deviceStore.setDeviceTypeInfo(devTypeInfo);
    await updateDeviceType();
}

const refreshExtendInfo = () => {
    extendInfos.value = [];
    let devTypeInfo = useDeviceStore().getDeviceTypeInfo();
    if (devTypeInfo.extend_info === undefined || devTypeInfo.extend_info === null) {
        devTypeInfo.extend_info = []
    }
    // console.log("extendinfo", devTypeInfo.extend_info);
    extendInfos.value.push(...devTypeInfo.extend_info);
}


const addStringInfo = () => {
    nextTick(() => {
        addTextRef.value?.show();
    })
}

const addNumberInfo = () => {
    nextTick(() => {
        addNumberRef.value?.show();
    })
}

const addSwitchInfo = () => {
    nextTick(() => {
        addSwitchRef.value?.show();
    })
}

const addEnumInfo = () => {
    nextTick(() => {
        addEnumRef.value?.show();
    })
}
const addDateInfo = () => {
    nextTick(() => {
        addDateRef.value?.show();
    })
}


watch(
    () => deviceTypeInfo.value.device_type_id,
    (newVal) => {
    //   console.log("new val:", newVal)
      refreshExtendInfo();
    },
    { immediate: true }
)
</script>

<style lang="scss" scoped>

.function-definition-container {
    // padding: 20px;
    // max-width: 1200px;
    margin: 0 auto; 
    padding-bottom: 40px;
}
  
.description {
    color: #666;
    margin-bottom: 20px;
}

.add-button {
    margin-left: 10rem;
    margin-top: 15px;
    // text-align: right;
}

</style>