<template>
    <div class="modbus-config-content">
      <h3>Modbus 寄存器设置</h3>
      <p class="description">
        当设备类型的接入协议选择 <strong>Modbus RTU</strong> 透传时，可设置设备 Modbus 寄存器地址，
        用于设备属性和 Modbus 消息之间的自动转换。
      </p>
  
      <el-divider />
  
      <div class="property-conversion">
        <h4>属性智能转换</h4>
        <el-switch v-model="enableSmartConversion" active-text="开启" inactive-text="关闭" />
        <p class="hint">
          开启后，ThingsCloud 将使用 Modbus 寄存器地址表，自动解析设备上报的 Modbus 消息，
          并将属性下发转换为 Modbus 消息。
        </p>
      </div>
  
      <el-divider />
  
      <div class="custom-data-stream">
        <h4>自定义数据流</h4>
        <p>请选择 Modbus RTU 格式的自定义数据流。</p>
        <el-button type="primary" @click="showDataStreamDialog">
          设置自定义数据流（已设置 modbus - TCP）
        </el-button>
      </div>
    </div>
  </template>
  
  <script setup lang="ts">
  import { ref } from 'vue';
  
  const enableSmartConversion = ref(true);
  
  const showDataStreamDialog = () => {
    // Implement dialog for data stream configuration
    console.log('Show data stream configuration dialog');
  };
  </script>
  
  <style scoped>
  .modbus-config-content {
    padding: 20px;
  }
  
  .modbus-config-content h3 {
    margin-top: 0;
    color: #303133;
  }
  
  .description {
    color: #606266;
    line-height: 1.6;
  }
  
  .property-conversion,
  .custom-data-stream {
    margin: 20px 0;
  }
  
  .property-conversion h4,
  .custom-data-stream h4 {
    margin: 0 0 10px 0;
    color: #303133;
  }
  
  .hint {
    color: #909399;
    font-size: 14px;
    margin-top: 10px;
    line-height: 1.6;
  }
  </style>