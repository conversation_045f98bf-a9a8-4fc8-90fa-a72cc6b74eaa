<template>
    <div class="page-content">
        <el-form label-width="auto">
            <el-form-item label="认证方式:" >
                <template #label>
                    <div >认证方式:</div>
                </template>
                <div >{{ getAuthTypeName(deviceTypeInfo.auth_type) }}</div>
            </el-form-item>
            <el-form-item label="ClientID:" >
                <template #label>
                    <div >ClientID:</div>
                </template>
                <div >{{ formData?.client_id }}</div>
            </el-form-item >
            <el-form-item label="用户名:" >
                <template #label>
                    <div >用户名:</div>
                </template>
                <div >{{ formData?.username }}</div>
            </el-form-item>
            <el-form-item label="密码:" >
                <template #label>
                    <div >密码:</div>
                </template>
                <div >{{ formData?.password }}</div>
            </el-form-item>
        </el-form>
    </div>
</template>

<script setup lang="ts">
import { typesService } from '@/api/device/typesApi'
import { AuthInfo } from '@/types/device'
import { ApiStatus } from '@/utils/http/status'
import { useDeviceStore } from '@/store/modules/device'
import { getAuthTypeName } from '@/utils/device'

const deviceStore = useDeviceStore()

const deviceTypeInfo = computed(() => deviceStore.deviceTypeInfo)

const formData = ref<AuthInfo>()

const getDeviceTypeAuthInfo = async () => {
    let params = {
        device_type_id: deviceTypeInfo.value?.device_type_id
    }   
    let resp = await typesService.deviceTypeAuthInfo(params)
    if (resp.code === ApiStatus.success ) {
        formData.value = resp.payload;
    }
    // console.log(resp
}

onMounted(async () => {
    console.log('detailAuthInfo.vue')
    await getDeviceTypeAuthInfo()
})
</script>


<style lang="scss" scoped>
:deep(.el-form-item__label) {
    @apply text-p; /* 如果使用 Tailwind */
    /* 或 */
    // font-size: 1.5rem;
    // font-weight: 600;
}
</style>