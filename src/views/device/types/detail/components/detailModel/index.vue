<template>
    <div class="function-definition-container">
      <div class="text-h3">编辑功能定义</div>
      <p class="description">
        功能定义用来描述不同设备的能力、包括属性、事件和命令，从而加快设备接入和应用开发。功能定义对设备类型中的所有设备都有效。
      </p>
  
      <!-- <div class="sections-container"> -->
        <!-- 属性部分 -->
        <el-card class="section-card">
          <template #header>
            <div class="card-header">
              <span>属性</span>
            </div>
          </template>
          <el-table :data="properties"  style="width: 100%">
            <el-table-column prop="name" label="属性名称"  />
            <el-table-column prop="identifier" label="属性标识符"  />
            <el-table-column prop="attr_type" label="属性类型" >
              <template #default="scope">
                <el-text class="mx-1" :type="GetAttrTypeCode(scope.row.attr_type)" size="small">
                  {{ AttrTypeToLabel(scope.row.attr_type) }}
                </el-text>
              </template>
            </el-table-column>
            <el-table-column prop="data_type" label="数据类型" >
              <template #default="scope">
                {{ DataTypeToLabel(scope.row.data_type) }}
              </template>
            </el-table-column>
            <el-table-column prop="data_options" label="扩展选项" width="350">
              <template #default="scope">
                <div style="white-space: pre-line"> {{ dataOptionsToText(scope.row) }}</div>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120">
              <template #default="scope">
                <el-button link type="primary" size="small" @click="editAttribute(scope.row)">编辑</el-button>
                <el-button link type="danger" size="small" @click="deleteAttribute(scope.row)" >删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <div class="add-button">
            <el-button type="primary" size="default"  plain @click="addAttribute">添加属性</el-button>
          </div>
        </el-card>
  
        <!-- 事件部分 -->
        <el-card class="section-card">
          <template #header>
            <div class="card-header">
              <span>事件</span>
            </div>
          </template>
          <el-table :data="events"  style="width: 100%">
            <el-table-column prop="name" label="事件名称"  />
            <el-table-column prop="identifier" label="事件标识符" />
            <el-table-column prop="params" label="事件参数" >
              <template #default="scope">
                <div v-for="(param, index) in scope.row.params" :key="index">
                  {{ param.name }} ({{ param.data_type }}): {{ param.desc }}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120">
              <template #default="scope">
                <el-button link type="primary" size="small"  @click="editEvent(scope.row)">编辑</el-button>
                <el-button link type="danger" size="small"  @click="deleteEvent(scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <div class="add-button">
            <el-button type="primary"  size="default"  plain @click="addEvent">添加事件</el-button>
          </div>
        </el-card>
  
        <!-- 命令部分 -->
        <el-card class="section-card">
            <template #header>
                <div class="card-header">
                    <span>命令</span>
                </div>
            </template>
            <el-table :data="commands"  style="width: 100%">
            <el-table-column prop="name" label="命令名称" width="180" />
            <el-table-column prop="identifier" label="命令标识符" width="180" />
            <el-table-column prop="send_params" label="命令参数" >
              <template #default="scope">
                <div v-for="(param, index) in scope.row.send_params" :key="index">
                  {{ param.name }} ({{ param.data_type }}): {{ param.desc }}
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="reply_params" label="回复参数" >
              <template #default="scope">
                <div v-for="(param, index) in scope.row.reply_params" :key="index">
                  {{ param.name }} ({{ param.data_type }}): {{ param.desc }}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120">
                <template #default="scope">
                <el-button link type="primary" size="small"  @click="editCommand(scope.row)" >编辑</el-button>
                <el-button link type="danger" size="small"   @click="deleteCommand(scope.row)">删除</el-button>
                </template>
            </el-table-column>
            </el-table>
            <div class="add-button">
              <el-button type="primary"  size="default"  plain @click="addCommand">添加命令</el-button>
            </div>
        </el-card>
      <!-- </div> -->
  
      <!-- TSL 部分 -->
      <div class="tsl-section">
          <h3>功能定义 TSL</h3>
          <p>TSL（Tiling Specification Language）用于描述功能定义元信息，采用 JSON 格式。</p>
          <el-button class="tsl-section-btn" @click="showJsonDialog">查看功能定义TSL</el-button>
      </div>
      <!-- JSON 数据弹框 -->
      <el-dialog
        v-model="jsonDialogVisible"
        title="查看功能定义 TSL"
        width="70%"
        top="5vh"
      >
        <el-alert
          title="请按照正确的 JSON 格式编辑功能定义数据"
          type="info"
          show-icon
          class="alert-message"
        />
        <!-- <el-input
          type="textarea"
          :rows="20"
          v-model="jsonData"
          class="json-editor"
        ></el-input> -->
        <div class="json-viewer">
          <pre>{{ jsonData }}</pre>
        </div>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="jsonDialogVisible = false">确定</el-button>
            <!-- <el-button type="primary" @click="importJson">确认导入</el-button> -->
          </span>
        </template>
      </el-dialog>
  
      <!-- TSL 部分 -->
      <div class="tsl-section">
          <h3>功能定义开发文档</h3>
          <p>开发文档描述了功能定义的相关消息通信协议，便于开发者使用，采用 Markdown 和 HTML 格式。</p>
          <el-button class="tsl-section-btn" @click="showTslViewDoc">查看功能定义文档</el-button>
      </div>

      <AddUpdateAttribute  ref="addUpdateAttributeRef" @handleSuccess="initAttributeData"/>
      <AddUpdateEvent ref="addUpdateEventRef" @handleSuccess="initEventData" />
      <AddUpdateCommand  ref="addUpdateCommandRef" @handleSuccess="initCommandData" />
      <DocView ref="docViewRef" />
    </div>
  </template>
  
  <script setup lang="ts">
  import { Attribute, Event, Command, NumberDataOption, SwitchDataOption, TextDataOption, EnumDataOption } from '@/types/device'
  import { ref } from 'vue'

  import { attributeService } from '@/api/device/attribute'
  import { eventService } from '@/api/device/event'
  import { ApiStatus } from '@/utils/http/status'
  import { useDeviceStore } from '@/store/modules/device'
  import { AttrTypeToLabel, DataTypeToLabel, GetAttrTypeCode } from '@/utils/device'
  import AddUpdateAttribute from './widget/addUpdateAttribute.vue'
  import AddUpdateEvent from './widget/addUpdateEvent.vue'
  import AddUpdateCommand from './widget/addUpdateCommand.vue'
  import { commandService } from '@/api/device/command'
  import DocView from './widget/docView.vue'


  const deviceStore = useDeviceStore()
  const deviceTypeInfo = computed(() => deviceStore.deviceTypeInfo)

  const deviceTypeID = ref(deviceTypeInfo.value.device_type_id)

  const addUpdateAttributeRef = ref<InstanceType<typeof AddUpdateAttribute>>()
  const addUpdateEventRef = ref<InstanceType<typeof AddUpdateEvent>>()
  const addUpdateCommandRef = ref<InstanceType<typeof AddUpdateCommand>>()
  const docViewRef = ref<InstanceType<typeof DocView>>()

  // 响应式数据
  const properties = ref<Attribute[]>([])
  
  const events = ref<Event[]>([])
  
  const commands = ref<Command[]>([])
  
  const tslContent = ref<string>(
    JSON.stringify(
      {
        version: '1.0',
        properties: properties.value,
        events: events.value,
        commands: commands.value
      },
      null,
      2
    )
  )
  
  // 方法
  const addAttribute = () => {
    nextTick(() => addUpdateAttributeRef.value?.add())
  }
  const editAttribute = (row: Attribute) => {
    console.log("row:", row.data_options)
    nextTick(() => addUpdateAttributeRef.value?.update(row))
  }
  const deleteAttribute =async (row: Attribute) => {
    let resp = await attributeService.deleteAttribute({ attr_id: row.attr_id })// .then((resp:) => {
    if (resp.code === ApiStatus.success) {
      await initAttributeData()
      ElMessage.success('删除成功')
    } else {
      ElMessage.error(resp.message)
    }   
  }

  
  const addEvent = () => {
    nextTick(() => addUpdateEventRef.value?.add())
  }

  const editEvent = (row: Event) => {
    nextTick(() => addUpdateEventRef.value?.update(row))
  }

  const deleteEvent =async (row: Event) => {
    let resp = await eventService.deletEvent({ event_id: row.event_id })
    if (resp.code === ApiStatus.success) {
      await initEventData()
      ElMessage.success('删除成功')
    } else {
      ElMessage.error(resp.message)
    }   
  }
  
  const addCommand = () => {
    nextTick(() => addUpdateCommandRef.value?.add())
  }
  
  const editCommand = (row: Command) => {
    nextTick(() => addUpdateCommandRef.value?.update(row))
  }
  
  const deleteCommand = async (row: Command) => {
    let resp = await commandService.deletCommand({ command_id: row.command_id })
    if (resp.code === ApiStatus.success) {
      await initCommandData()
      ElMessage.success('删除成功')
    } else {
      ElMessage.error(resp.message)
    } 
  }

  // 新增的JSON相关状态
  const jsonDialogVisible = ref(false)
  const editingJson = ref('')
  
  // 计算属性：将当前数据转换为JSON字符串
  const jsonData = computed({
    get: () => {
      return JSON.stringify(
        {
          name: deviceTypeInfo.value.name,
          device_type_id: deviceTypeInfo.value.device_type_id,
          access_type: deviceTypeInfo.value.access_type,
          desc: deviceTypeInfo.value.desc,
          icon: deviceTypeInfo.value.icon,
          attributes: properties.value,
          events: events.value,
          commands: commands.value
        },
        null,
        2
      )
    },
    set: (value) => {
      editingJson.value = value
    }
  })
  
  // 显示JSON弹框
  const showJsonDialog = () => {
    jsonDialogVisible.value = true
  }


  // 显示开发文档弹窗
  const showTslViewDoc = () => {
    const devTypeInfo = useDeviceStore().getDeviceTypeInfo()
    nextTick(() => docViewRef.value?.show(devTypeInfo, properties.value, events.value, commands.value))
  }

  // dataOption是一个对象，需要转为字符串才可显示在表格中
  const dataOptionsToText = (row: Attribute) => {
    // console.log(row)
    let optionText = "";
    if (row.data_type === "number") {
      let dataOption = row.data_options as NumberDataOption;
     
      if(dataOption.unit !== "") {
        optionText += "单位: "+ dataOption.unit + ",  ";
      } 
      if (dataOption.min !== "") {
        optionText += "最小值:" + dataOption.min + ",  ";
      } 
      if (dataOption.max !== "") {
        optionText += "最大值:" + dataOption.max + ",  ";
      } 
      if (dataOption.step !== "") {
        optionText += "精度:" + dataOption.step + "  ";
      }
    } else if (row.data_type === "switch") {
      let dataOption = row.data_options as SwitchDataOption;
      optionText += "值类型:" + dataOption.value_type + "   ";
      optionText += "值名称:" + dataOption.on_text + "/" + dataOption.off_text;
    } else if (row.data_type === "text") {
      let dataOption = row.data_options as TextDataOption;
      optionText += "默认值:" + dataOption.default;
    } else if (row.data_type === "enum") {
      let dataOption = row.data_options as EnumDataOption;
      Object.entries(dataOption.enum_map).forEach(([key, label]) => {
        optionText += `${key} - ${label};  \n `;
      });
    }
    return optionText
  }

  
  const queryParams = ref({
    page: 1,
    limit: 20,
    device_type_id: deviceTypeID.value,
    attr_id: "",
  })

  const initAttributeData = async () => {
    let params = queryParams.value;
    let resp = await attributeService.findAttributeList(params)
    if (resp.code === ApiStatus.success) {
      properties.value = resp.payload.list
    }
  }

  const initEventData = async() => {
    let params = queryParams.value;
    let resp = await eventService.findEventList(params)
    if (resp.code === ApiStatus.success) {
      events.value = resp.payload.list
    }
  }

  const initCommandData = async() => {
    let params = queryParams.value;
    let resp = await commandService.findCommandList(params)
    if (resp.code === ApiStatus.success) {
      commands.value = resp.payload.list
    }
  }

  onMounted(async () => {
    // console.log('onMounted')
    await initAttributeData()
    await initEventData()
    await initCommandData()
  })


 
  watch(
    () => deviceTypeInfo.value.device_type_id,
    async (newVal) => {
      console.log("new val:", newVal)
      deviceTypeID.value = newVal
      queryParams.value.device_type_id = newVal
      await initAttributeData()
      await initEventData()
      await initCommandData()
    },
    { immediate: true }
  )
  </script>
  
  <style lang="scss" scoped>
  .function-definition-container {
      // padding: 20px;
      // max-width: 1200px;
      margin: 0 auto; 
      padding-bottom: 40px;
    }
    
    .description {
      color: #666;
      margin-bottom: 20px;
    }
    
  //   .sections-container {
  //     display: flex;
  //     flex-direction: column;
  //     gap: 20px;
  //   }
    
    .section-card {
      margin-bottom: 20px;
    }
    
    .card-header {
      font-weight: bold;
      font-size: 16px;
    }
    
    .add-button {
      margin-top: 15px;
      text-align: right;
    }
    
    .tsl-section {
      margin-top: 35px;
    }
    
    .tsl-section h2 {
      margin-bottom: 10px;
    }
    .tsl-section-btn {
      margin-top: 10px;
    }
    
    .el-textarea {
      margin-top: 10px;
    }
  
  
  // json弹出框
  .action-bar {
    margin-bottom: 20px;
    text-align: right;
  }
  
  // .json-editor {
  //   font-family: monospace;
  //   margin-top: 15px;
  //   background-color: #1a1a1a;
  // }
  
  .json-viewer {
    margin-top: 15px;
    padding: 15px;
    background-color: #1a1a1a;
    border-radius: 4px;
    max-height: 60vh;
    overflow: auto;
  }

  .json-viewer pre {
    margin: 0;
    color: #f8f8f2;
    font-family: 'Consolas', 'Monaco', monospace;
    white-space: pre-wrap;
    word-wrap: break-word;
  }

  .alert-message {
    margin-bottom: 15px;
  }
  </style>