<template>
    <el-dialog  
          v-model="paramVisible"
          :title="title"
          :width="dialogWidth"
          @close="closeParamDialog"
          top="5vh" 
        >
      <el-form 
              ref="paramFormRef"
              :model="paramFormData"
              :rules="rules" 
              label-width="auto" 
              label-position="top"
              class="property-form"
            >
          <div class="section">
                <h3>参数名称</h3>
                <el-form-item label="填写参数名称" prop="name">
                    <el-input v-model="paramFormData.name" placeholder="请输入属性名称" />
                </el-form-item>
                
                <el-form-item label="参数标识符" prop="identifier">
                    <el-input v-model="paramFormData.identifier" placeholder="请输入参数标识符" />
                </el-form-item>
            
          </div>
              
          <div class="section">
              <h3>数据类型</h3>
              <el-form-item label="选择数据类型" prop="data_type">
                  <el-select v-model="paramFormData.data_type" placeholder="请选择数据类型">
                    <el-option 
                      v-for="attr in getDatatypeList()"
                      :label="attr.label"
                      :value="attr.value"
                      :key="attr.id"
                    />
                  </el-select>
              </el-form-item>
              
              <!--------------- 数据类型后面的数据属性dataOptions  ---------------->
              <!-- 数值类型 -->
              <div v-if="paramFormData.data_type === 'number'">
                <el-row>
                  <el-col :span="24">
                    <el-form-item label="单位" prop="unit">
                      <el-input v-model="numberDataOptions.unit" placeholder="请输入单位"></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="11">
                    <el-form-item label="默认值" prop="default">
                      <el-input v-model="numberDataOptions.default" placeholder="请输入默认值"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="2"></el-col>
                  <el-col :span="11">
                    <el-form-item label="精度" prop="step">
                      <el-select v-model="numberDataOptions.step" placeholder="请选择精度">
                        <el-option label="1" value="1" />
                        <el-option label="0.1" value="0.1" />
                        <el-option label="0.01" value="0.01" />
                        <el-option label="0.001" value="0.001" />
                        <el-option label="0.0001" value="0.0001" />
                        <el-option label="0.00001" value="0.00001" />
                        <el-option label="0.000001" value="0.000001" />
                      </el-select>
                    </el-form-item>
                  </el-col>   
                </el-row>
                <el-row>
                  <el-col :span="11">
                    <el-form-item label="最大值" prop="max">
                      <el-input v-model="numberDataOptions.max" placeholder="请输入最大值"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="2"></el-col>
                  <el-col :span="11">
                    <el-form-item label="最小值" prop="min">
                      <el-input v-model="numberDataOptions.min" placeholder="请输入最小值"></el-input>
                    </el-form-item>
                  </el-col>   
                </el-row>
              </div>
              <!-- 开关类型 -->
              <div v-if="paramFormData.data_type === 'switch'">
                <el-form-item label="开关值类型" prop="value_type">
                  <el-select v-model="switchDataOptions.value_type" placeholder="请选择开关值类型">
                    <el-option v-for="item in switchOptions"
                      :key="item.key"
                      :label="item.label"
                      :value="item.value_type">
                      <span style="float: left">{{ item.label }}</span>
                      <span style="float: right;color: var(--el-text-color-secondary);font-size: 13px;">
                        {{ item.value_label }}
                      </span>
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-row>
                  <el-col :span="11">
                    <el-form-item :label="switchOnName()" prop="on_text">
                      <el-input v-model="switchDataOptions.on_text" placeholder=""></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="2"></el-col>
                  <el-col :span="11">
                    <el-form-item :label="switchOffName()" prop="off_text">
                      <el-input v-model="switchDataOptions.off_text" placeholder=""></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
              <!-- 文本类型 -->
              <div v-if="paramFormData.data_type === 'text'">
                <el-form-item label="默认值" prop="default">
                  <el-input v-model="textDataOptions.default" placeholder="请输入默认值"></el-input>
                </el-form-item>
              </div>
              <!-- 枚举类型 -->
              <div v-if="paramFormData.data_type === 'enum'"  style="margin-bottom: 15px;">
                <el-row>
                  <el-col  :span="11">枚举值</el-col>
                  <el-col  :span="11">枚举描述</el-col>
                  <el-col  :span="2"></el-col>
                </el-row>
                <el-row :gutter="20" v-for="(tag, index) in enumTmpList" :key="index" style="margin-top: 5px;">
                  <el-col :span="11">
                    <el-input v-model="tag.key" placeholder="" />
                  </el-col>
                  <el-col :span="10">
                    <el-input v-model="tag.value" placeholder="枚举描述" />
                  </el-col>
                  <el-col :span="3">
                    <el-button  size="default" :icon="Delete" circle  @click="deleteEnumItem(index)"/>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="24">
                    <el-link type="primary" :underline="false" @click="addEnumItem">+ 添加枚举项</el-link>
                  </el-col>
                </el-row>
              </div>
            
              <el-form-item label="参数描述" prop="desc">
                  <el-input 
                  v-model="paramFormData.desc" 
                  type="textarea" 
                  :rows="3" 
                  placeholder="请输入属性描述"
                  />
              </el-form-item>
          </div>
          <div class="form-actions">
            <el-button @click="closeParamDialog">取消</el-button>
            <el-button  @click="save"  >确定</el-button>
          </div>
      </el-form>
    </el-dialog>
</template>

<script setup lang="ts">
import { EnumDataOption, NumberDataOption, Param, SwitchDataOption, TextDataOption } from '@/types/device';
import { Delete } from '@element-plus/icons-vue'
import { FormInstance, FormRules } from 'element-plus';
import { DataTypeList } from '@/utils/device'

const emit = defineEmits(["save", "update"])

const paramFormData = reactive<Param>({
    name: "",
    identifier: "",
    data_type: "",
    data_options: {},
    desc: "",
})

const paramFormRef =  ref<FormInstance>()
const title = ref("标题");
const paramVisible = ref(false);
const dialogWidth = ref("750px");
const dialogType = ref("add");
const isObjectOrList = ref(false);

const getDatatypeList = () => {
  if (isObjectOrList.value === true ){
    return DataTypeList.filter(item => item.value !== 'object' && item.value !== 'list')
  } else {
    return DataTypeList
  }
}

const rules = reactive<FormRules>({
    name: [
      { required: true, message: '请输入名称', trigger: 'blur' },
      { min: 1, max: 20, message: '长度在 1 到 20 个字符', trigger: 'blur' }
    ],
    identifier: [{ required: true, message: '请输入参数标识符', trigger: 'blur'}], 
    data_type: [{ required: true, message: '请输入参数类型', trigger: 'blur'}], 
  })

  let numberDataOptions = ref<NumberDataOption>({
    default: "",
    min: "",
    max: "",
    step: "",
    unit: '',
  })
  
  let enumDataOptions = ref<EnumDataOption>({
    enum_map: {} as { [key: string]: string },
  })
  let switchDataOptions = ref<SwitchDataOption>({
    value_type: "number",
    on_text: "",
    off_text: "",
  })
  let textDataOptions = ref<TextDataOption>({
    default: "",
  })

  const enumTmpList = ref([{ key: '', value: '' }])
  const deleteEnumItem = (index: number) => {
    enumTmpList.value.splice(index, 1)
  }

  const addEnumItem = () => {
    enumTmpList.value.push({ key: '', value: '' })
  }


  const enumTmpListToEnumMap = () => {
    let tmp_map = {} as { [key: string]: string };
    enumTmpList.value.forEach((item) => {
      tmp_map[item.key] = item.value
    })
    return tmp_map;
  }

  const switchOptions = ref([
    { key: 1,  value_type: 'boolean', value_label: "Boolean",  label: 'True/False' },
    { key: 2,  value_type: 'number',  value_label: "Number", label: '1/0'},
    { key: 3,  value_type: 'text',    value_label: "Text",  label: 'On/Off'}
  ])

  const switchOnName = () => {
    if (switchDataOptions.value.value_type === 'boolean') {
      return "True 名称" 
    } else if (switchDataOptions.value.value_type === 'number') {
      return "1 名称" 
    } else {
      return "On 名称" 
    }
  }

  const switchOffName = () => {
    if (switchDataOptions.value.value_type === 'boolean') {
      return "False 名称" 
    } else if (switchDataOptions.value.value_type === 'number') {
      return "0 名称" 
    } else {
      return "Off 名称" 
    }
  }

 
const resetForm = () => {
  paramFormData.name = ''
  paramFormData.identifier = ''
  paramFormData.data_type = ''
  paramFormData.data_options = {}
  paramFormData.desc = ''

  //
  numberDataOptions.value.default = ""
  numberDataOptions.value.max = ""
  numberDataOptions.value.min = ""
  numberDataOptions.value.step = ""
  numberDataOptions.value.unit = ""
  // 
  switchDataOptions.value.off_text = ""
  switchDataOptions.value.on_text = ""
  switchDataOptions.value.value_type = ""
  //
  textDataOptions.value.default = ""
  //
  enumTmpList.value = [];
}

const rowDataOptionToForm = (row: Param) => {
  if(row.data_type === "number") {
    let dataOption = row.data_options as NumberDataOption;
    numberDataOptions.value = dataOption;
  } else if (row.data_type === "switch") {
    let dataOption = row.data_options as SwitchDataOption;
    switchDataOptions.value = dataOption;
  } else if (row.data_type === "enum") {
    let dataOption = row.data_options as EnumDataOption;
    enumDataOptions.value = dataOption;
    enumTmpList.value = Object.entries(dataOption.enum_map).map(([key, value]) => ({
      key,
      value,
    }));;
  } else if (row.data_type === "text") {
    let dataOption = row.data_options as TextDataOption;
    textDataOptions.value = dataOption;
  }
}

const save =async () => {
  if (!paramFormRef.value) return;
  
  await paramFormRef.value.validate(async (valid) => {
    if (!valid) return;
    switch (paramFormData.data_type) {
      case 'number':
        paramFormData.data_options = numberDataOptions;
        break;
      case 'enum':
        enumDataOptions.value.enum_map = enumTmpListToEnumMap();
        paramFormData.data_options = enumDataOptions;
        break;
      case 'switch':
        paramFormData.data_options = switchDataOptions;
        break;
      case 'text':
        paramFormData.data_options = textDataOptions;
        break;
      default:
        paramFormData.data_options = {};
    }
    let one = <Param>{};
    one.name = paramFormData.name;
    one.identifier = paramFormData.identifier;
    one.data_type = paramFormData.data_type;
    one.data_options = paramFormData.data_options;
    one.desc = paramFormData.desc;
    if(  dialogType.value === "add"){
      emit("save", one)
    } else {
      emit("update", one)
    }
    closeParamDialog()
  })
}

const closeParamDialog = () => {
  resetForm()
  paramVisible.value = false ;
  isObjectOrList.value = false;
}

const add = (addTitle: string, isObjOrList: boolean = false ) => {
  title.value = addTitle;
  dialogType.value = "add"
  paramVisible.value = true;
  isObjectOrList.value = isObjOrList;
}

const update = (upTitle: string, row: Param, isObjOrList: boolean = false ) => {
  title.value = upTitle
  paramFormData.name = row.name
  paramFormData.identifier = row.identifier
  paramFormData.data_type = row.data_type
  paramFormData.data_options = row.data_options
  paramFormData.desc = row.desc
  rowDataOptionToForm(row)
  dialogType.value = "update"
  paramVisible.value = true 
  isObjectOrList.value = isObjOrList;

}

defineExpose({
  add,
  update,
})

</script>


<style scoped>

.section {
  margin-bottom: 10px;
  padding: 10px;
  background-color:var(--art-main-bg-color); /* #f9f9f9; */
  border-radius: 4px;
}

.section h2, .section h3 {
  margin-top: 0;
  color: #333;
}

.section h2 {
  font-size: 18px;
  margin-bottom: 15px;
}

.section h3 {
  font-size: 16px;
  margin-bottom: 5px;
}

.description {
  color: #666;
  margin-bottom: 15px;
}

.property-form {
  margin-top: 20px;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}
</style>