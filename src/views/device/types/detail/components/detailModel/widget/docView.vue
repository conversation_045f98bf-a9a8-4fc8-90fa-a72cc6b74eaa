<template>

  
    <!-- 开发文档弹框 -->
    <el-dialog
      v-model="docDialogVisible"
      title="功能定义开发文档"
      width="750px"
      top="5vh" 
    >
      <div class="doc-container">
        <div class="doc-tabs">
          <el-tabs v-model="activeDocTab">
            <el-tab-pane label="Markdown格式" name="markdown">
              <el-input
                type="textarea"
                :rows="20"
                v-model="markdownContent"
                class="doc-editor"
                placeholder="请输入Markdown格式的开发文档..."
              ></el-input>
            </el-tab-pane>
            <el-tab-pane label="HTML格式" name="html">
              <el-input
                type="textarea"
                :rows="20"
                v-model="htmlContent"
                class="doc-editor"
                placeholder="请输入HTML格式的开发文档..."
              ></el-input>
            </el-tab-pane>
            <el-tab-pane label="预览" name="preview">
              <div v-if="activeDocTab === 'preview'" class="preview-content" >
                <BytemdViewer  :tabindex="2" :plugins="plugins" :value="markdownContent" class="custom-markdown-viewer"></bytemdViewer>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="docDialogVisible = false">确定</el-button>
          <!-- <el-button type="primary" @click="saveDocumentation">确定</el-button> -->
        </span>
      </template>
    </el-dialog>
  </template>


<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'

import { AttrTypeToLabel, DataTypeToLabel } from '@/utils/device';
import { Attribute, Event, Command, DeviceType, Param } from '@/types/device'
import moment from 'moment';

import { Viewer as BytemdViewer  }  from '@bytemd/vue-next' ; // 导入编辑器组件
import gfm from '@bytemd/plugin-gfm'
import highlight from '@bytemd/plugin-highlight'
import 'bytemd/dist/index.css'

// 定义插件
const plugins = [
  gfm(),
  highlight()
]

const properties = ref<Attribute[]>([])
const events = ref<Event[]>([])
const commands = ref<Command[]>([])
const detailInfo = ref<DeviceType>();

// 新增的开发文档相关状态
const docDialogVisible = ref(false)
const activeDocTab = ref('preview')
const markdownContent = ref('')
const htmlContent = ref('')




// 显示开发文档弹框
const showDocumentDialog = () => {
  // 这里可以添加从服务器获取文档内容的逻辑
  markdownContent.value = generateMarkdownDoc()
  htmlContent.value = generateHtmlDoc()
  docDialogVisible.value = true
}

const propertiesToJson = () => {
  const result = Object.fromEntries(
    properties.value.map(attr => [
      attr.identifier,
      attr.data_type === "number" ? 0 : ""
    ])
  );

  const resultJson = JSON.stringify(result,null,2);
  return resultJson;
}

const eventsToJson = () => {
  const result = Object.fromEntries(
    events.value.map(evt => [
      evt.identifier,
      Object.fromEntries(
        evt.params.map(param => [
          param.identifier,
          param.data_type === "number" ? 0 : ""
        ])
      )
    ])
  );
  const resultJson = JSON.stringify(result,null,2);
  return resultJson;
}

const properteisToDarray = () => {
  const twoDArray = properties.value.map(attr => [
    attr.name,
    attr.identifier,
    attr.attr_type,
    attr.data_type,
    typeof attr.data_options === 'object' 
        ? JSON.stringify(attr.data_options) 
        : String(attr.data_options),
    attr.desc,
  ]);
  return twoDArray
}

const eventParamsToDarray = () => {
  return events.value.map(event => 
    event.params.map(param => [
      param.name,
      param.identifier,
      param.data_type,
      typeof param.data_options === 'object' 
        ? JSON.stringify(param.data_options) 
        : String(param.data_options),
      param.desc
    ].map(String)) // 确保每个元素都是 string
  ).flat(); // 如果希望所有 params 合并成一个二维数组，而不是按 event 分组
}

const ondComandToJson = (cmdIdentifier: string , params: Param[]): string => {
  const paramsObj = Object.fromEntries(
    params.map(param => [
      param.identifier,
      param.data_type.toLowerCase() === "number" ? 0 : ""
    ])
  );
  const result = { [cmdIdentifier]: paramsObj };
  return JSON.stringify(result, null, 2);
}


const paramsToDarray = (params: Param[]) => {
  let result = params.map(param => [
      param.name,
      param.identifier,
      param.data_type,
      typeof param.data_options === 'object' 
        ? JSON.stringify(param.data_options) 
        : String(param.data_options),
      param.desc
    ].map(String)) // 确保每个元素都是 string
  return result
}

class MarkdownBuilder {
  private content: string = ' \n';

  addHeading(text: string, level: number = 1): this {
    this.content += `${'#'.repeat(level)} ${text}\n\n`;
    return this;
  }

  addSecodeHeading(text: string, level: number = 1): this {
    this.content += `${'##'.repeat(level)} ${text}\n\n`;
    return this;
  }
  addThreeHeading(text: string, level: number = 1): this {
    this.content += `${'###'.repeat(level)} ${text}\n\n`;
    return this;
  }
  addFourHeading(text: string, level: number = 1): this {
    this.content += `${'####'.repeat(level)} ${text}\n\n`;
    return this;
  }

  addParagraph(text: string): this {
    this.content += `${text}\n\n`;
    return this;
  }

  addList(items: string[]): this {
    this.content += items.map(item => `- ${item}`).join('\n') + '\n\n';
    return this;
  }

  addCode(code: string, language: string = ''): this {
    this.content += `\`\`\`${language}\n${code}\n\`\`\`\n\n`;
    return this;
  }

  addLink(text: string, url: string): this {
    this.content += `[${text}](${url})\n\n`;
    return this;
  }

  /**
   * 添加表格
   * @param headers 表头数组
   * @param rows 表格行数据，二维数组
   * @param alignments 每列对齐方式，可选：'left' | 'center' | 'right'，默认为'left'
   */
   addTable(headers: string[], rows: string[][], alignments: Array<'left' | 'center' | 'right'> = []): this {
    if (rows.some(row => row.length !== headers.length)) {
      // throw new Error('每行的列数必须与表头列数一致');
      ElMessage.error('每行的列数必须与表头列数一致');
      return this
    }

    // 生成表头行
    this.content += `| ${headers.join(' | ')} |\n`;

    // 生成分隔线
    const separators = headers.map((_, index) => {
      const alignment = alignments[index] || 'left';
      switch (alignment) {
        case 'left': return ':---';
        case 'center': return ':---:';
        case 'right': return '---:';
        default: return '---';
      }
    });
    this.content += `| ${separators.join(' | ')} |\n`;

    // 生成数据行
    for (const row of rows) {
      this.content += `| ${row.join(' | ')} |\n`;
    }

    this.content += '\n'; // 添加空行分隔
    return this;
  }

  build(): string {
    this.content += '\n'; // 添加空行
    return this.content;
  }
}

// 生成Markdown格式的文档
const generateMarkdownDoc = () => {
  const builder = new MarkdownBuilder()
        .addHeading(`${detailInfo.value?.name} 功能定义文档`)
        .addParagraph(`${moment().format('YYYY-MM-DD HH:mm:ss')}`)
        .addSecodeHeading("属性")
        .addThreeHeading("属性集合")
        .addCode(propertiesToJson(), "json")
        .addThreeHeading("属性说明")
        .addTable(["属性标识符","属性名称", "属性类型","数据类型", "选项", "描述"], properteisToDarray(), ["center"])
        .addParagraph("---")
        .addSecodeHeading("事件")
        .addThreeHeading("上报")
        .addFourHeading("事件消息格式")
        .addCode(eventsToJson())
        .addFourHeading("事件参数")
        .addTable(["参数标识符", "参数名称", "数据类型", "扩展选项", "描述"], eventParamsToDarray(), ["center"])
    builder.addParagraph("---")
    builder.addSecodeHeading("命令")
    commands.value.forEach((cmd) =>{
      builder.addThreeHeading(cmd.name)
      builder.addParagraph(cmd.desc)
      builder.addFourHeading("命令消息格式")
      builder.addCode(ondComandToJson(cmd.identifier, cmd.send_params))
      builder.addTable(["参数标识符", "参数名称", "数据类型", "扩展选项", "描述"],paramsToDarray(cmd.send_params), ["center"] )
      builder.addFourHeading("回复消息格式")
      builder.addCode(ondComandToJson(cmd.identifier, cmd.reply_params))
      builder.addTable(["参数标识符", "参数名称", "数据类型", "扩展选项", "描述"],paramsToDarray(cmd.reply_params), ["center"] )
    })
    return builder.build()
}

// 生成HTML格式的文档
const generateHtmlDoc = () => {
  return `<h1>${detailInfo.value?.name} 功能定义文档</h1>
<section>
  <h2>属性</h2>
  ${properties.value.map(prop => `<div class="property">
    <h3>${prop.name} (${prop.identifier})</h3>
    <ul>
      <li>类型: ${AttrTypeToLabel(prop.attr_type)}</li>
      <li>数据类型: ${DataTypeToLabel(prop.data_type)}</li>
      <li>描述: ${prop.desc || '无'}</li>
    </ul>
  </div>`).join('\n')}
</section>

<section>
  <h2>事件</h2>
  ${events.value.map(event => `<div class="event">
    <h3>${event.name} (${event.identifier})</h3>
    <ul>
      ${event.params.map(param => `<li>${param.name}: ${param.data_type} - ${param.desc || '无'}</li>`).join('\n')}
    </ul>
  </div>`).join('\n')}
</section>

<section>
  <h2>命令</h2>
  ${commands.value.map(cmd => `<div class="command">
    <h3>${cmd.name} (${cmd.identifier})</h3>
    <h4>输入参数:</h4>
    <ul>
      ${cmd.send_params.map(param => `<li>${param.name}: ${param.data_type} - ${param.desc || '无'}</li>`).join('\n')}
    </ul>
    <h4>输出参数:</h4>
    <ul>
      ${cmd.reply_params.map(param => `<li>${param.name}: ${param.data_type} - ${param.desc || '无'}</li>`).join('\n')}
    </ul>
  </div>`).join('\n')}
</section>`
}

// 保存文档
const saveDocumentation = async () => {
  try {
    // 这里添加保存到服务器的逻辑
    ElMessage.success('文档保存成功')
    docDialogVisible.value = false
  } catch (error) {
    ElMessage.error('文档保存失败')
  }
}

// 修改TSL部分的按钮
const show = (devTypeInfo: DeviceType, attrs: Attribute[], evts: Event[], cmds: Command[]  ) => {
  detailInfo.value = devTypeInfo
  properties.value = attrs
  events.value = evts 
  commands.value = cmds
  showDocumentDialog()
}


defineExpose({
  show,
})
</script>

<style lang="scss" scoped>

.doc-container {
  margin-top: -20px;
}

.doc-editor {
  font-family: monospace;
  margin-top: 10px;
}

.preview-content {
  // 穿透到 BytemdViewer 内部
  :deep(.custom-markdown-viewer) {
    h1, h2, h3, h4 {
      margin: 10px 0;
    }
    // 表格样式
    table {
      border-collapse: collapse;
      width: 100%;
      margin: 20px 0;
      border: 1px solid #ddd;

      th, td {
        border: 1px solid #ddd;
        padding: 8px;
        text-align: left;
      }

      th {
        background-color: #f2f2f2;
      }
    }

    // 其他 Markdown 元素样式
    pre {
      background-color: #f5f5f5;
      padding: 1em;
      border-radius: 4px;
    }

    //
    hr {
      border: none;
      height: 2px;
      background: linear-gradient(90deg, transparent, #4a90e2, transparent);
      margin: 2rem 0;
      position: relative;
      
      &::before {
        content: "❖";
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        background: white;
        padding: 0 10px;
        color: #4a90e2;
      }
    }
  }
}

</style>