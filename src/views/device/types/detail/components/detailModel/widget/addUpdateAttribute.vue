<template>
    <el-dialog
        v-model="dialogVisible"
        :title="dialogType === 'add' ? '添加属性' : '编辑属性'"
        :width="dialogWidth"
        @close="closeDialog"
        top="5vh" 
    >
        <div class="add-property-container">
        <el-alert show-icon  :closable="false">
          <a href="#">什么是设备属性?</a>
        </el-alert>
        
        <el-form 
            ref="formRef"
            :model="formData"
            :rules="rules" 
            label-width="auto" 
            label-position="top"
            class="property-form"
        >
            <div class="section">
            <h3>属性名称</h3>
            <el-form-item label="填写属性名称" prop="name">
                <el-input v-model="formData.name" placeholder="请输入属性名称" />
            </el-form-item>
            
            <el-form-item label="属性标识符" prop="identifier">
                <el-input v-model="formData.identifier" placeholder="请输入属性标识符" />
            </el-form-item>
          
            </div>
            
            <div class="section">
            <h3>属性类型</h3>
            <el-form-item prop="attr_type">
                <el-radio-group v-model="formData.attr_type">
                  <el-radio 
                    v-for="attr in AttrTypeList"
                    :label="attr.label"
                    :value="attr.value"
                    :key="attr.id"
                    >
                  </el-radio>
                </el-radio-group>
            </el-form-item>
            </div>
            
            <div class="section">
            <h3>数据类型</h3>
            <el-form-item label="选择数据类型" prop="data_type">
                <el-select v-model="formData.data_type" placeholder="请选择数据类型">
                  <el-option 
                    v-for="attr in DataTypeList"
                    :label="attr.label"
                    :value="attr.value"
                    :key="attr.id"
                  />
                </el-select>
            </el-form-item>
            
            <!--------------- 数据类型后面的数据属性dataOptions  ---------------->
            <!-- 数值类型 -->
            <div v-if="formData.data_type === 'number'">
              <el-row>
                <el-col :span="24">
                  <el-form-item label="单位" prop="unit">
                    <el-input v-model="numberDataOptions.unit" placeholder="请输入单位"></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="11">
                  <el-form-item label="默认值" prop="default">
                    <el-input v-model="numberDataOptions.default" placeholder="请输入默认值"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="2"></el-col>
                <el-col :span="11">
                  <el-form-item label="精度" prop="step">
                    <el-select v-model="numberDataOptions.step" placeholder="请选择精度">
                      <el-option label="1" value="1" />
                      <el-option label="0.1" value="0.1" />
                      <el-option label="0.01" value="0.01" />
                      <el-option label="0.001" value="0.001" />
                      <el-option label="0.0001" value="0.0001" />
                      <el-option label="0.00001" value="0.00001" />
                      <el-option label="0.000001" value="0.000001" />
                    </el-select>
                  </el-form-item>
                </el-col>   
              </el-row>
              <el-row>
                <el-col :span="11">
                  <el-form-item label="最大值" prop="max">
                    <el-input v-model="numberDataOptions.max" placeholder="请输入最大值"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="2"></el-col>
                <el-col :span="11">
                  <el-form-item label="最小值" prop="min">
                    <el-input v-model="numberDataOptions.min" placeholder="请输入最小值"></el-input>
                  </el-form-item>
                </el-col>   
              </el-row>
            </div>
            <!-- 开关类型 -->
            <div v-if="formData.data_type === 'switch'">
              <el-form-item label="开关值类型" prop="value_type">
                <el-select v-model="switchDataOptions.value_type" placeholder="请选择开关值类型">
                  <el-option v-for="item in switchOptions"
                    :key="item.key"
                    :label="item.label"
                    :value="item.value_type">
                    <span style="float: left">{{ item.label }}</span>
                    <span style="float: right;color: var(--el-text-color-secondary);font-size: 13px;">
                      {{ item.value_label }}
                    </span>
                  </el-option>
                </el-select>
              </el-form-item>
              <el-row>
                <el-col :span="11">
                  <el-form-item :label="switchOnName()" prop="on_text">
                    <el-input v-model="switchDataOptions.on_text" placeholder=""></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="2"></el-col>
                <el-col :span="11">
                  <el-form-item :label="switchOffName()" prop="off_text">
                    <el-input v-model="switchDataOptions.off_text" placeholder=""></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
            <!-- 文本类型 -->
            <div v-if="formData.data_type === 'text'">
              <el-form-item label="默认值" prop="default">
                <el-input v-model="textDataOptions.default" placeholder="请输入默认值"></el-input>
              </el-form-item>
            </div>
            <!-- 枚举类型 -->
            <div v-if="formData.data_type === 'enum'"  style="margin-bottom: 15px;">
              <el-row>
                <el-col  :span="11">枚举值</el-col>
                <el-col  :span="11">枚举描述</el-col>
                <el-col  :span="2"></el-col>
              </el-row>
              <el-row :gutter="20" v-for="(tag, index) in enumTmpList" :key="index" style="margin-top: 5px;">
                <el-col :span="11">
                  <el-input v-model="tag.key" placeholder="枚举名称" />
                </el-col>
                <el-col :span="10">
                  <el-input v-model="tag.value" placeholder="枚举描述" />
                </el-col>
                <el-col :span="3">
                  <el-button  size="default" :icon="Delete" circle  @click="deleteEnumItem(index)"/>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24">
                  <!-- <el-button type="text" @click="addEnumItem">+ 添加枚举项</el-button> -->
                  <el-link type="primary" :underline="false" @click="addEnumItem">+ 添加枚举项</el-link>
                </el-col>
              </el-row>
            </div>
            <!-- 键值对 -->
            <div v-if="formData.data_type === 'object'" style="margin-bottom: 15px;">
              <el-table :data="objectDataOptios.attr_list"  style="width: 100%">
                <el-table-column prop="name" label="参数名称" width="180" />
                <el-table-column prop="identifier" label="参数标识符" width="180" />
                <!-- <el-table-column prop="data_type" label="参数类型" /> -->
                <el-table-column prop="data_type" label="数据类型" >
                  <template #default="scope">
                    {{ DataTypeToLabel(scope.row.data_type) }}
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="120">
                  <template #default="scope">
                    <el-link class="action-elink" type="primary" :underline="false" @click="updateObjectItem(scope.row)">编辑</el-link>
                    <el-link class="action-elink" type="danger" :underline="false" @click="deleteObjectParamItem(scope.row)">删除</el-link>
                  </template>
                </el-table-column>
              </el-table>
              <el-link type="primary" :underline="false" @click="addObjectItem">+ 添加键值对</el-link>
            </div>
            <!-- list数组类型 -->
            <div v-if="formData.data_type === 'list'" style="margin-bottom: 15px;">
              <el-table :data="listDataOptions.param_list"  style="width: 100%">
                <el-table-column prop="name" label="参数名称" width="180" />
                <el-table-column prop="identifier" label="参数标识符" width="180" />
                <!-- <el-table-column prop="data_type" label="参数类型" /> -->
                <el-table-column prop="data_type" label="数据类型" >
                  <template #default="scope">
                    {{ DataTypeToLabel(scope.row.data_type) }}
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="120">
                  <template #default="scope">
                    <el-link  class="action-elink" type="primary" :underline="false" @click="updateListItem(scope.row)">编辑</el-link>
                    <el-link  class="action-elink" type="danger" :underline="false" @click="deleteListParamItem(scope.row)">删除</el-link>
                  </template>
                </el-table-column>
              </el-table>
              <el-link type="primary" :underline="false" @click="addListItem">+ 添加列表参数</el-link>
            </div>
            <el-form-item label="属性描述" prop="desc">
                <el-input 
                v-model="formData.desc" 
                type="textarea" 
                :rows="3" 
                placeholder="请输入属性描述"
                />
            </el-form-item>
            </div>
            
            <div class="form-actions">
              <!-- <el-button type="primary" @click="saveAndContinue">保存并继续添加</el-button> -->
              <el-button @click="closeDialog">取消</el-button>
              <el-button type="success" @click="save">保存</el-button>
            </div>
        </el-form>
        </div>

        <AddUpdateParam  ref="addUpdateObjectRef" @save="addObjectParamItem" @update="updateObjectParamItem" />
        <AddUpdateParam  ref="addUpdateListRef" @save="addListParamItem" @update="updateListParamItem" />
    </el-dialog>
  </template>
  
  <script lang="ts" setup>
  import { reactive } from 'vue'
  import { ElMessage, FormInstance, FormRules } from 'element-plus'
  import { Attribute, EnumDataOption, ListDataOption, NumberDataOption, ObjectDataOption, Param, SwitchDataOption, TextDataOption } from '@/types/device'
  import { AttrTypeList, DataTypeList } from '@/utils/device'
  import { Delete } from '@element-plus/icons-vue'
  import { attributeService } from '@/api/device/attribute'
  import { ApiStatus } from '@/utils/http/status'
  import { DataTypeToLabel } from '@/utils/device'
  import { useDeviceStore } from '@/store/modules/device'
  import AddUpdateParam from './addUpdateParam.vue'
import { validateAlphanumeric } from '@/utils/formdt'
  const deviceStore = useDeviceStore()
  const deviceTypeInfo = computed(() => deviceStore.deviceTypeInfo)
  const dialogVisible = ref(false)
  const dialogType = ref('add')
  const dialogWidth = ref('750px')

  const emit = defineEmits(['handleSuccess'])

  const addUpdateObjectRef = ref<InstanceType<typeof AddUpdateParam>>();
  const addUpdateListRef = ref<InstanceType<typeof AddUpdateParam>>();

  const formRef = ref<FormInstance>()
  const formData = ref<Attribute>({
    device_type_id: "", 
    attr_id: "",
    name: "",
    identifier: "",
    attr_type: "device",
    data_type: "",
    data_options: {},
    desc: ""
  })
  

  const rules = reactive<FormRules>({
    name: [
      { required: true, message: '请输入类型名称', trigger: 'blur' },
      { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
    ],
    identifier: [
      { required: true, message: '请输入属性标识符', trigger: 'blur'},
      { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' },
      { validator: validateAlphanumeric, trigger: 'blur' }], 
    data_type: [{ required: true, message: '请选择数据类型', trigger: 'blur'}],
  })

  let numberDataOptions = reactive<NumberDataOption>({
    default: "",
    min: "",
    max: "",
    step: "",
    unit: '',
  })
  
  let enumDataOptions = reactive<EnumDataOption>({
    enum_map: {} as { [key: string]: string },
  })
  let switchDataOptions = reactive<SwitchDataOption>({
    value_type: "number",
    on_text: "",
    off_text: "",
  })
  let textDataOptions = reactive<TextDataOption>({
    default: "",
  })

  let objectDataOptios = reactive<ObjectDataOption>({
      attr_list: [] as Param[],
  });

  let listDataOptions  = reactive<ListDataOption>({
      param_list: [] as Param[],
  });

  const enumTmpList = ref([{ key: '', value: '' }])
  const deleteEnumItem = (index: number) => {
    enumTmpList.value.splice(index, 1)
  }

  const addEnumItem = () => {
    enumTmpList.value.push({ key: '', value: '' })
  }

  const addObjectItem = () => {
    nextTick(() => addUpdateObjectRef.value?.add("添加键值对", true))
  }
  
  const updateObjectItem = (row: Param) => {
    nextTick(() => addUpdateObjectRef.value?.update("编辑键值对",row, true))
  }

  const addObjectParamItem = (one: Param) => {
    if (objectDataOptios.attr_list  === undefined  ) {
      objectDataOptios.attr_list = [] as Param[];
    }
    objectDataOptios.attr_list.push(one);
  }

  const updateObjectParamItem = (newOne: Param) => {
    // 找到 identifier 匹配的项的索引
    const index = objectDataOptios.attr_list.findIndex(
      (param) => param.identifier === newOne.identifier
    );  
    objectDataOptios.attr_list[index] = newOne;
  }

  const deleteObjectParamItem = (delOne: Param) => {
    // 找到 identifier 匹配的项的索引
    const index = objectDataOptios.attr_list.findIndex(
      (param) => param.identifier === delOne.identifier
    );  
    objectDataOptios.attr_list.splice(index, 1);
  }

  const addListItem = () => {
    nextTick(() => addUpdateListRef.value?.add("添加列表参数", true))
  }
  
  const updateListItem = (row: Param) => {
    nextTick(() => addUpdateListRef.value?.update("修改列表参数",row, true))
  }

  const addListParamItem = (one: Param) => {
    if (listDataOptions.param_list  === undefined  ) {
      listDataOptions.param_list = [] as Param[];
    }
    listDataOptions.param_list.push(one);
  }

  const updateListParamItem = (newOne: Param) => {
    // 找到 identifier 匹配的项的索引
    const index = listDataOptions.param_list.findIndex(
      (param) => param.identifier === newOne.identifier
    );  
    listDataOptions.param_list[index] = newOne;
  }

  const deleteListParamItem = (delOne: Param) => {
    // 找到 identifier 匹配的项的索引
    const index = listDataOptions.param_list.findIndex(
      (param) => param.identifier === delOne.identifier
    );  
    listDataOptions.param_list.splice(index, 1);
  }

 


  const enumTmpListToEnumMap = () => {
    let tmp_map = {} as { [key: string]: string };
    enumTmpList.value.forEach((item) => {
      tmp_map[item.key] = item.value
    })
    return tmp_map;
  }

  const switchOptions = ref([
    { key: 1,  value_type: 'boolean', value_label: "Boolean",  label: 'True/False' },
    { key: 2,  value_type: 'number',  value_label: "Number", label: '1/0'},
    { key: 3,  value_type: 'text',    value_label: "Text",  label: 'On/Off'}
  ])

  const switchOnName = () => {
    if (switchDataOptions.value_type === 'boolean') {
      return "True 名称" 
    } else if (switchDataOptions.value_type === 'number') {
      return "1 名称" 
    } else {
      return "On 名称" 
    }
  }

  const switchOffName = () => {
    if (switchDataOptions.value_type === 'boolean') {
      return "False 名称" 
    } else if (switchDataOptions.value_type === 'number') {
      return "0 名称" 
    } else {
      return "Off 名称" 
    }
  }


  const handleFormSubmit = async (isContinue = false) => {
  if (!formRef.value) return;
  
  await formRef.value.validate(async (valid) => {
    if (!valid) return;
    
    // 设置 data_options 的公共逻辑
    switch (formData.value.data_type) {
      case 'number':
        formData.value.data_options = numberDataOptions;
        break;
      case 'enum':
        enumDataOptions.enum_map = enumTmpListToEnumMap();
        formData.value.data_options = enumDataOptions;
        break;
      case 'switch':
        formData.value.data_options = switchDataOptions;
        break;
      case 'text':
        formData.value.data_options = textDataOptions;
        break;
      case "object":
        formData.value.data_options = objectDataOptios;
        break;
      case "list":
        formData.value.data_options = listDataOptions;
        break;
      default:
        formData.value.data_options = {};
    }
    // console.log("formData:", formData.value)
    // 根据是否有 attr_id 决定调用哪个 API
    const apiCall = formData.value.attr_id  ? 
      attributeService.updateAttribute : 
      attributeService.addAttribute;
    
    const res = await apiCall(formData.value);
    if (res.code === ApiStatus.success) {
      ElMessage.success(formData.value.attr_id ? '更新成功!' : '添加成功!');
      
      if (!isContinue) {
        emit("handleSuccess");
        closeDialog();
      } else {
        resetForm();
      }
    } else {
      ElMessage.error(res.message);
    }
  });
};

// 原来的两个函数简化为调用公共函数
const saveAndContinue = () => handleFormSubmit(true);
const save = () => handleFormSubmit(false);

const resetForm = () => {
    formData.value.device_type_id = ''
    formData.value.name = ''
    formData.value.identifier = ''
    formData.value.attr_id = ''
    formData.value.attr_type = 'device'
    formData.value.data_type = ''
    formData.value.desc = ''
    formData.value.data_options = {};
    enumTmpList.value = [{ key: '', value: '' }] 
    numberDataOptions = {} as NumberDataOption;
    switchDataOptions = {} as SwitchDataOption;
    textDataOptions = {} as TextDataOption;
    enumDataOptions = {} as EnumDataOption;
    objectDataOptios.attr_list = [] as Param[];
    listDataOptions.param_list = [] as Param[];
}

const closeDialog = () => {
    resetForm()
    enumTmpList.value = [{ key: '', value: '' }]
    emit("handleSuccess")
    dialogVisible.value = false
}

// 将一个 Attribute的dataOption填入表单，按类型
const rowDataOptionToForm = (row: Attribute) => {
    if(row.data_type === "number") {
      let dataOption = row.data_options as NumberDataOption;
      numberDataOptions = dataOption;
    } else if (row.data_type === "switch") {
      let dataOption = row.data_options as SwitchDataOption;
      switchDataOptions = dataOption;
    } else if (row.data_type === "enum") {
      let dataOption = row.data_options as EnumDataOption;
      enumDataOptions = dataOption;
      enumTmpList.value = Object.entries(dataOption.enum_map).map(([key, value]) => ({
        key,
        value,
      }));;
    } else if (row.data_type === "text") {
      let dataOption = row.data_options as TextDataOption;
      textDataOptions = dataOption;
    } else if (row.data_type === "object") {
      let dataOption = row.data_options as ObjectDataOption;
      objectDataOptios = dataOption;
    } else if (row.data_type === "list") {
      let dataOption = row.data_options as ListDataOption;
      listDataOptions = dataOption;
    }

}

watch(
    () => deviceTypeInfo.value.device_type_id,
    (newVal) => {
      // console.log("new val:", newVal)
      formData.value.device_type_id = newVal
    },
    { immediate: true }
)

const add = () => {
  let devTypeinfo =  deviceStore.getDeviceTypeInfo()
  formData.value.device_type_id = devTypeinfo.device_type_id; 
  dialogVisible.value = true
}


  const update = (row: Attribute) => {
    let devTypeinfo =  deviceStore.getDeviceTypeInfo()
    formData.value.device_type_id = devTypeinfo.device_type_id; 
    formData.value.name = row.name;
    formData.value.attr_id = row.attr_id
    formData.value.attr_type = row.attr_type
    formData.value.data_type = row.data_type
    formData.value.data_options = row.data_options
    formData.value.identifier = row.identifier
    formData.value.desc = row.desc
    rowDataOptionToForm(row)
    dialogVisible.value = true
    dialogType.value = 'update'
  }
  
  defineExpose({
    add,
    update,
  })

  </script>
  
  <style scoped>
  .add-property-container {
    max-width: 800px;
    margin: 0 auto;
    /* padding: 20px; */
  }
  
  .section {
    margin-bottom: 10px;
    padding: 10px;
    background-color:var(--art-main-bg-color); /* #f9f9f9; */
    border-radius: 4px;
  }
  
  .section h2, .section h3 {
    margin-top: 0;
    color: #333;
  }
  
  .section h2 {
    font-size: 18px;
    margin-bottom: 15px;
  }
  
  .section h3 {
    font-size: 16px;
    margin-bottom: 5px;
  }
  
  .description {
    color: #666;
    margin-bottom: 15px;
  }
  
  .property-form {
    margin-top: 20px;
  }
  
  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
  }

  </style>