<template>
    <el-dialog
        v-model="dialogVisible"
        :title="dialogType === 'add' ? '添加事件' : '编辑事件'"
        :width="dialogWidth"
        @close="closeDialog"
        top="5vh" 
    >
        <div class="add-property-container">
        <el-alert show-icon  :closable="false">
          <a href="#">什么是事件?</a>
        </el-alert>
        
        <el-form 
            ref="formRef"
            :model="formData"
            :rules="rules" 
            label-width="auto" 
            label-position="top"
            class="property-form"
        >
            <div class="section">
            <h3>事件名称</h3>
            <el-form-item label="填写事件名称" prop="name">
                <el-input v-model="formData.name" placeholder="请输入事件名称" />
            </el-form-item>
            
            <el-form-item label="事件标识符" prop="identifier">
                <el-input v-model="formData.identifier" placeholder="请输入事件标识符" />
            </el-form-item>
          
            </div>
            
            
          <div class="section">
            <h3>事件参数</h3>
            <el-table :data="formData.params"  style="width: 100%">
              <el-table-column prop="name" label="参数名称" width="180" />
              <el-table-column prop="identifier" label="参数标识符" width="180" />
              <!-- <el-table-column prop="data_type" label="参数类型" /> -->
              <el-table-column prop="data_type" label="数据类型" >
                <template #default="scope">
                  {{ DataTypeToLabel(scope.row.data_type) }}
                </template>
              </el-table-column>
              <el-table-column label="操作" width="120">
                <template #default="scope">
                  <el-link class="action-elink" type="primary" :underline="false" @click="showEditParam(scope.row)">编辑</el-link>
                  <el-link class="action-elink" type="danger" :underline="false"  @click="deleteParam(scope.row)">删除</el-link>
                </template>
              </el-table-column>
            </el-table>
            <el-link type="primary" :underline="false" @click="showAddParam">+ 添加参数</el-link>
          </div>

          <div class="section">
            <h3>事件描述</h3>
            <el-form-item label="" prop="desc">
              <el-input 
                v-model="formData.desc" 
                type="textarea" 
                :rows="3" 
                placeholder="请输入事件描述"
                />
            </el-form-item>
          </div>
            
            <div class="form-actions">
              <el-button @click="closeDialog">取消</el-button>
              <el-button type="success" @click="save">保存</el-button>
            </div>
        </el-form>
        </div>

        <AddUpdateParam ref="addUpdateParamRef" @save="addParamItem" @update="updateParamItem" />
    </el-dialog>
  </template>
  
  <script lang="ts" setup>
  import { reactive } from 'vue'
  import { ElMessage, FormInstance, FormRules } from 'element-plus'
  import {  Event,Param } from '@/types/device'
  import AddUpdateParam from "./addUpdateParam.vue";
  import { DataTypeToLabel } from '@/utils/device'
  import { ApiStatus } from '@/utils/http/status'
  import { useDeviceStore } from '@/store/modules/device'
  import { eventService } from '@/api/device/event';
import { validateAlphanumeric } from '@/utils/formdt';
  const deviceStore = useDeviceStore()
  const deviceTypeInfo = computed(() => deviceStore.deviceTypeInfo)

  const dialogVisible = ref(false)
  const dialogType = ref('add')
  const dialogWidth = ref('750px')


  const addUpdateParamRef = ref<InstanceType<typeof AddUpdateParam>>()

  const emit = defineEmits(['handleSuccess'])

  
  const formRef = ref<FormInstance>()
  const formData = reactive<Event>({
    device_type_id: deviceTypeInfo.value.device_type_id, 
    event_id: "",
    name: "",
    identifier: "",
    params: [] as Param[],
    desc: ""
  })


  const rules = reactive<FormRules>({
    name: [
      { required: true, message: '请输入类型名称', trigger: 'blur' },
      { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
    ],
    identifier: [
      { required: true, message: '请输入属性标识符', trigger: 'blur'},
      { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' },
      { validator: validateAlphanumeric, trigger: 'blur' }], 
  })


 
  const showAddParam = () => {
    let title = "添加参数"
    nextTick(() => addUpdateParamRef.value?.add(title, true))
  }

  const showEditParam = (row: Param) => {
    console.log("row:", row)
    let title = "编辑参数"
    nextTick(() => addUpdateParamRef.value?.update(title, row, true))
  }

  const deleteParam = (row: Param) => {
    // 找到 identifier 匹配的项的索引
    const index = formData.params.findIndex(
      (param) => param.identifier === row.identifier
    );
    // 如果找到匹配项，则更新
    if (index !== -1) {
      formData.params.splice(index, 1); // 删除该索引位置的元素
    } else {
      ElMessage.error("删除${newOne.identifier}的参数项失败.请刷新")
    }
  }

  const addParamItem = (one: Param) => {
    formData.params.push(one);
  }
  const updateParamItem = (newOne: Param) => {
    // 找到 identifier 匹配的项的索引
    const index = formData.params.findIndex(
      (param) => param.identifier === newOne.identifier
    );
    // 如果找到匹配项，则更新
    if (index !== -1) {
      formData.params[index] = newOne;
    } else {
      ElMessage.error("更新${newOne.identifier}的参数项失败.请重新编辑")
    }
  }


  const handleFormSubmit = async (isContinue = false) => {
  if (!formRef.value) return;
  
  await formRef.value.validate(async (valid) => {
    if (!valid) return;
        
    // 根据是否有 attr_id 决定调用哪个 API
    const apiCall = formData.event_id ? 
      eventService.updatEvent : 
      eventService.addEvent;
    const res = await apiCall(formData);
    if (res.code === ApiStatus.success) {
      ElMessage.success(formData.event_id ? '更新成功!' : '添加成功!');
      if (!isContinue) {
        emit("handleSuccess");
        closeDialog();
      } else {
        resetForm();
      }
    } else {
      ElMessage.error(res.message);
    }
  });
};

// 原来的两个函数简化为调用公共函数
const save = () => handleFormSubmit(false);

  const resetForm = () => {
    formData.device_type_id = ''
    formData.name = ''
    formData.identifier = ''
    formData.event_id = ""
    formData.desc = ''
    formData.params = [] as Param[]
  }

  const closeDialog = () => {
    resetForm()
    dialogVisible.value = false
  }


  const add = () => {
    let devTypeinfo =  deviceStore.getDeviceTypeInfo()
    formData.device_type_id = devTypeinfo.device_type_id; 
    dialogVisible.value = true
    dialogType.value = 'add'
  }


  const update = (row: Event) => {
    let devTypeinfo =  deviceStore.getDeviceTypeInfo()
    formData.device_type_id = devTypeinfo.device_type_id; 
    formData.name = row.name;
    formData.identifier = row.identifier
    formData.desc = row.desc
    formData.params = row.params
    dialogVisible.value = true
    dialogType.value = 'update'
  }
  
  defineExpose({
    add,
    update,
  })
  </script>
  
  <style scoped>
  .add-property-container {
    max-width: 800px;
    margin: 0 auto;
    /* padding: 20px; */
  }
  
  .section {
    margin-bottom: 10px;
    padding: 10px;
    background-color:var(--art-main-bg-color); /* #f9f9f9; */
    border-radius: 4px;
  }
  
  .section h2, .section h3 {
    margin-top: 0;
    color: #333;
  }
  
  .section h2 {
    font-size: 18px;
    margin-bottom: 15px;
  }
  
  .section h3 {
    font-size: 16px;
    margin-bottom: 5px;
  }
  
  .description {
    color: #666;
    margin-bottom: 15px;
  }
  
  .property-form {
    margin-top: 20px;
  }
  
  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
  }
  </style>