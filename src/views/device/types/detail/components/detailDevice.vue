<template>
    <div class="data-info-container">
      <div class="header">
        <h3 class="text-h3">设备列表</h3>
        <div class="operation-buttons">
          <el-button type="primary" @click="handleAddDevice" size="default">
            <el-icon><Plus /></el-icon>
            关联设备
          </el-button>
          <!-- <el-button @click="handleShowSettings"   size="default">
            <el-icon><Setting /></el-icon>
            显示设置
          </el-button> -->
          <el-button @click="handleRefresh"  size="default">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </div>
      
      <el-table
        :data="tableData"
        style="width: 100%"
        stripe
        v-loading="loading"
      >
        <el-table-column prop="name" label="设备名称" width="180" />
        <el-table-column prop="device_id" label="设备ID" width="180" />
        <el-table-column prop="active_online" label="在线状态">
          <template #default="{ row }">
            <el-tag :type="row.active_online === 1 ? 'success' : 'danger'">
              {{ getOnlineStatus(row.active_online) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="active_time" label="活跃时间" />
        <el-table-column prop="alarm" label="告警状态">
          <template #default="scope">
            <el-tag :type="getAlertTagType(scope.row)">
              {{ scope.row.alarm?.length > 0 ? scope.row.alarm?.length : '' }}
            </el-tag>
          </template>
        </el-table-column>
        <!-- <el-table-column label="操作" width="150"> -->
          <!-- <template #default="{ row }"> -->
            <!-- <el-button size="small" @click="handleEdit(row)">编辑</el-button> -->
            <!-- <el-button size="small" type="danger" @click="handleDelete(row)">删除</el-button> -->
          <!-- </template> -->
        <!-- </el-table-column> -->
      </el-table>
  
      <!-- 添加设备对话框 -->
      <el-dialog v-model="dialogVisible" title="关联设备">
        <MyTable ref="tableRef"
          :columns="columns"
          :query-params="deviceParams"
          row-key="device_id"
          @page-request="pageRequest"
          >
          <template #operation="{ scope }">
            <div class="table_operation">
              <el-link
                type="primary"
                :underline="false"
                @click="clickSelect(scope.row)"
              >
                选择关联
              </el-link>
            </div>
          </template>
        </MyTable>
      </el-dialog>
    </div>
  </template>
  
  <script lang="ts" setup>
  import { ref } from 'vue'
  import { ElMessage } from 'element-plus'
  import { Plus, Setting, Refresh } from '@element-plus/icons-vue'
  import { useDeviceStore } from '@/store/modules/device'
  import { typesService } from '@/api/device/typesApi'
  import { Device } from '@/types/device'
  import { ApiStatus } from '@/utils/http/status'
import { deviceService } from '@/api/device/device'
import MyTable from '@/components/custom/tables/MyTable.vue'
  const deviceStore = useDeviceStore()
  const deviceTypeInfo = computed(() => deviceStore.deviceTypeInfo)

  const tableRef = ref<InstanceType<typeof MyTable>>()
  const deviceOptions = ref<Device[]>([])

  const tableData = ref<Device[]>([])
  
  const loading = ref(false)

  const slots = defineSlots<{
    operation?: (props: { row: any }) => any
  }>()

  const dialogVisible = ref(false)
 
  

  const columns = ref([
    { label: '设备ID', prop: 'device_id' },
    { label: '设备名称', prop: 'name' },
    { 
      label: '设备类型',
      prop: 'device_type_info',
      formatter: (row: Device) => {
        if (row.device_type_info !== undefined && row.device_type_info !== null) {
          return row.device_type_info.name
        } else {
          return "";
        }
      }, 
    },
    { 
      label: '在线状态', 
      prop: 'active_online',
      formatter: (row: Device) => {
        return getOnlineStatus(row.active_online)
      }
    },
    {
      label: "操作",
      prop: "operation",
      rowSlot: true,
      required: true ,
      fixed: "right",
    }
  ])


const pageRequest = (queryParams:any, callback:any) => {
  let params = { ...queryParams };
  const promise = new Promise(async (resolve) => {
    let resp = await deviceService.findDeviceList(params);
    resolve({
      data: resp.payload.list,
      total: resp.payload.total,
    });
  });
  callback(promise);
};


  const getAlertTagType = (row: Device) => {
    if (row.alarm === undefined || row.alarm === null) {
      return 'info'
    }
    let  alarmLen = Object.keys(row.alarm).length
    if (alarmLen === 0) {
      return 'success'
    }  else if (alarmLen < 3 ){
      return "warning"
    }  else if (alarmLen >= 3 ){
      return "danger"
    } else {
      return "info"
    }
  }
  const getOnlineStatus = (status: number) => {
    return status === 1 ? '在线' : '离线'
  }
  
  const clickSelect = async (row: Device) => {
    row.device_type_id = deviceStore.getDeviceTypeInfo().device_type_id;
    let resp = await  deviceService.updateDevice(row)
    if (resp.code === ApiStatus.success) {
      ElMessage.success('设备关联成功')
      await handleRefresh()
      dialogVisible.value = false
    } else {
      ElMessage.error(resp.message)
    }
  }

  const handleAddDevice = () => {
    dialogVisible.value = true
  }
  

  

  
  const handleRefresh =async () => {
    loading.value = true
    // 模拟异步请求
    await initTableData()
    loading.value = false
    ElMessage.success('数据已刷新')
  }
  



  const params = ref({
      device_type_id: deviceTypeInfo.value.device_type_id
  })

  const deviceParams = ref({page: 1, page_size: 10})
  const initDeviceData = async () => {
    let resp = await deviceService.findDeviceList(deviceParams.value)
    if (resp.code === ApiStatus.success) {
      deviceOptions.value = resp.payload.list
    } else {
      ElMessage.error(resp.message)
    }
  }
  
  const initTableData = async () => {
    if (params.value.device_type_id === undefined || params.value.device_type_id === "") {
      return
    }
    let resp = await typesService.findDeviceTypeDeviceList(params.value)
    if (resp.code === ApiStatus.success) {
      tableData.value = resp.payload.list
    } else {
      ElMessage.error(resp.message)
    }
  }

  onMounted(async () => {
    await initDeviceData()
  })

  watch(
    () => deviceTypeInfo.value.device_type_id,
    async (newVal) => {
      console.log("new val:", newVal)
      params.value.device_type_id = newVal;
      await initTableData();
    },
    { immediate: true }
  )
  </script>
  
  <style scoped>
  .data-info-container {
    padding: 20px;
    background-color: var(--art-main-bg-color);
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  }
  
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }
  
  .title {
    margin: 0;
    color: #303133;
  }
  
  .operation-buttons .el-button {
    margin-left: 10px;
  }
  
  .el-icon {
    margin-right: 5px;
  }
  </style>