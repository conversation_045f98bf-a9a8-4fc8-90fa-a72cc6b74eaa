<template>
    <div class="setting-class">
        <div class="text-item-class">
            <div class="text-h3">类型描述</div>
            <div class="text-desc">
                {{ deviceTypeInfo.desc }}
            </div>
            <div class="text-btn">
                <el-button plain size="default"  @click="showUpdateInfo">编辑</el-button>
            </div>
        </div>


        <div class="text-item-class">
            <div class="text-h3">
                标签
            </div>
            <div class="text-desc">
                <el-table :data="deviceTypeInfo.tags">
                    <el-table-column prop="key" label="标签名称"></el-table-column>
                    <el-table-column prop="value" label="标签值"></el-table-column>
                </el-table>
            </div>
            <div class="text-btn">
                <el-button plain size="default"  @click="showUpdateInfo">添加/编辑标签</el-button>
            </div>
        </div>

        <div class="text-item-class">
            <div class="text-h3">
                发布到产品库
            </div>
            <div class="text-desc">
                将设备类型作为产品模板，发布到 公共产品库。 其他用户在创建设备类型时，可通过产品 ID，快速创建该设备类型。
            </div>
            <div class="text-btn">
                <el-button plain size="default" @click="publishProduct" >发布</el-button>
            </div>
        </div>
        <div class="text-item-class">
            <div class="text-h3">
                移除设备类型
            </div>
            <div class="text-desc">
                仅当设备类型没有绑定的设备时，才可以移除。移除设备类型将自动移除设备类型下的所有规则、任务、告警规则，该操作无法恢复。
            </div>
            <div class="text-btn">
                <el-button plain size="default" type="danger" @click="removeDeviceType" >移除设备类型</el-button>
            </div>
        </div>


        <AddUpdate ref="addUpdateRef" />
   
    </div>
</template>

<script setup lang="ts">
import { useDeviceStore } from '@/store/modules/device';
import AddUpdate from '../../widget/addUpdate.vue';
import { typesService } from '@/api/device/typesApi';
import { ApiStatus } from '@/utils/http/status';
import { DeviceType } from '@/types/device';
import { router } from '@/router';



const addUpdateRef =  ref<InstanceType<typeof AddUpdate>>()

const deviceStore = useDeviceStore()
const deviceTypeInfo  = computed(() => deviceStore.deviceTypeInfo)

const showUpdateInfo = () => {
    nextTick(() => {
        addUpdateRef.value?.showDialog(deviceTypeInfo.value)
        // updateDeviceInfoRef.value?.show(deviceInfo.value)
    })
}

const publishProduct = () => {
    ElMessage.warning("暂未实现")
}



const removeDeviceType = () => {
    ElMessageBox.confirm(`确定要删除 ${deviceTypeInfo.value.name} 吗?`, '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(async () => {
      let resp  = await  typesService.deleteDeviceType({ device_type_id: deviceTypeInfo.value.device_type_id})
      if (resp.code === ApiStatus.success) {
        deviceStore.setDeviceTypeInfo({} as DeviceType)
        deviceStore.setDeviceTypeIndexRefresh(true)
        ElMessage.success('删除成功')
        router.push("/device/types")
      } else {
        ElMessage.error("删除失败:" + resp.message)
      }
    })
}

</script>

<style lang="css" scoped>
.setting-class { 
    width: 100%;
    margin: 0 auto;
    background-color: var(--art-background-color);
    padding-left: 20px;
    padding-right: 20px;
    /* padding-top: 20px; */
    padding-bottom: 50px;
}


.text-item-class {
    margin-top: 25px;
}

.text-btn {
    margin-top: 10px;

}

</style>