<template>
<el-dialog
        v-model="dialogVisible"
        :title="title"
        :width="dialogWidth"
        @close="closeDialog"
    >
        <el-form ref="formRef" :model="formData" :rules="rules" label-width="auto">
            <el-form-item label="图标" prop="icon">
                <!-- <UserAvatar   @update="update" :avatar="formData.icon" /> -->
            </el-form-item>
            <el-form-item label="类型名称" prop="name">
                <el-input v-model="formData.name" />
            </el-form-item>
            <el-form-item label="是否启用" prop="status">
                <el-switch v-model="formData.status" :inactive-value="-1" :active-value="1" />
            </el-form-item>
            <el-form-item label="设备接入类型" prop="access_type">
                <el-radio-group v-model="formData.access_type">
                <el-radio :value="item.value" v-for="(item, index) in accessTypeList" :key="index">
                    {{ item.label }}
                </el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item label="设备认证类型" prop="auth_type">
                <el-radio-group v-model="formData.auth_type">
                <el-radio :value="item.value" v-for="(item, index) in authTypeList" :key="index">
                    {{ item.label }}
                </el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item label="设备通信类型" prop="conn_type">
                <el-select v-model="formData.conn_type" placeholder="请选择">
                <el-option
                    v-for="item in connTypeOptions"
                    :key="item.key"
                    :label="item.label"
                    :value="item.value"
                />
                </el-select>
            </el-form-item>
            <el-form-item label="描述" prop="desc">
                <el-input
                    :autosize="{ minRows: 4, maxRows: 4 }"
                    type="textarea"
                    maxlength="200"
                    show-word-limit
                    v-model="formData.desc"
                />
            </el-form-item>
            <el-form-item label="设备标签" prop="tags">
              <div class="tags-container">
                <el-row :gutter="20" v-for="(tag, index) in formData.tags" :key="index">
                  <el-col :span="11">
                    <el-input v-model="tag.key" placeholder="标签key" />
                  </el-col>
                  <el-col :span="11">
                    <el-input v-model="tag.value" placeholder="标签值" />
                  </el-col>
                  <el-col :span="2">
                    <el-button
                      size="default"
                      :icon="Delete"
                      circle
                      @click="deleteTag(index)"
                    ></el-button>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="24">
                        <el-link type="primary" @click="addTag" :underline="false">+ 添加标签</el-link>
                    </el-col>
                </el-row>
              </div>
            </el-form-item>
        </el-form>
        <template #footer>
        <div class="dialog-footer">
            <el-button @click="closeDialog">取消</el-button>
            <el-button type="primary" @click="handleSubmit">提交</el-button>
        </div>
        </template>
    </el-dialog>
</template>


<script lang="ts" setup>
import { typesService } from '@/api/device/typesApi';
import { DeviceType, KeyValue } from '@/types/device';
import { ApiStatus } from '@/utils/http/status';
import { FormInstance, FormRules } from 'element-plus';
import { Delete } from '@element-plus/icons-vue'
import { accessTypeList } from '@/utils/device'

const emit = defineEmits(['success'])
const formRef = ref<FormInstance>();

const dialogVisible  = ref(false);
const title = ref('编辑设备类型');

const formData = ref({
    name: '',
    icon: "",
    status: 1,
    access_type: 1,
    auth_type: 1,
    conn_type: "",
    desc: "",
    tags: [] as KeyValue[],
})

const rules = reactive<FormRules>({
    name: [
        { required: true, message: '请输入类型名称', trigger: 'blur' },
        { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
    ],
    status: [
        { required: true, message: '请选择状态', trigger: 'blur' },
    ],
    access_type: [
        { required: true, message: '请选择接入类型', trigger: 'blur' },
    ],
    auth_type: [
        { required: true, message: '请选择认证类型', trigger: 'blur' },
    ],
})

const authTypeList = ref([
    { value: 1, label: '一型一密' },
    { value: 2, label: '一机一密'},
  ])

  const connTypeOptions = ref([
    { key: 1, value: 'wifi', label: 'WIFI' },
    { key: 2, value: 'ethernet', label: '以太网' },
    { key: 3, value: '4g', label: '蜂窝网络4G' },
    { key: 4, value: '5g', label: '蜂窝网络5G' },
    { key: 5, value: 'nb-iot', label: 'NB-IoT' },
    { key: 6, value: 'other', label: '其他' }
  ])


const deleteTag = (index: number) => {
    formData.value.tags.splice(index, 1)
}
  
const addTag = () => {
    formData.value.tags.push({ key: '', value: '' })
}
  

const showDialog = (item: DeviceType) => {
    formData.value.name = item.name;
    formData.value.icon = item.icon;
    formData.value.status = item.status;
    formData.value.desc = item.desc;
    formData.value.tags = item.tags;
    formData.value.access_type = item.access_type;
    formData.value.auth_type = item.auth_type;
    formData.value.conn_type = item.conn_type;
    curItem.value = item;
    dialogVisible.value = true;
}

const update = (icon: string) => {
    formData.value.icon = icon;
};

const closeDialog = () => {
    dialogVisible.value = false;
}

const setCurInfo = () => {
    curItem.value!.name = formData.value.name;
    curItem.value!.desc = formData.value.desc;  
    curItem.value!.icon = formData.value.icon;
    curItem.value!.tags = formData.value.tags;
    curItem.value!.status = formData.value.status;
    curItem.value!.access_type = formData.value.access_type;
    curItem.value!.auth_type = formData.value.auth_type;
    curItem.value!.conn_type = formData.value.conn_type;
}

const curItem = ref<DeviceType>();
const handleSubmit = async () => {
    if (!formRef.value) return
    await formRef.value.validate(async (valid) => {
        if (valid) {
            setCurInfo()
            let res = await typesService.updateDeviceType(curItem.value)
            if (res.code === ApiStatus.success) {
                ElMessage.success('修改成功!')
                emit("success")
                closeDialog()
            } else {
                ElMessage.error(res.message)
            }
        }
    })
}



const dialogWidth = ref("50%")
    const handleResize = () => {
        // 根据屏幕宽度调整对话框宽度
    const windowWidth = window.innerWidth;
    if (windowWidth < 768) {
        dialogWidth.value = "90%"
    } else if(windowWidth > 768 && windowWidth < 1000) {
        dialogWidth.value = "70%"
    } else if(windowWidth > 1000 && windowWidth < 1440) {
        dialogWidth.value = "50%"
    } else {
        dialogWidth.value = "50%"
    }
}

onMounted(() => {
    window.addEventListener('resize', handleResize)
    handleResize() // 初始化响应式布局
})

defineExpose({
    showDialog,
})
</script>