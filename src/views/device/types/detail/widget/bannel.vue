<template>
    <div class="device-header">
      <!-- 主标题区域 -->
      <div class="header-main">
        <div class="title-section">
            <i
                class="iconfont-sys device-icon"
                v-html="deviceTypeInfo?.icon"
                :style="{ color: 'rgb(var(--art-primary))', fontSize: 60 + 'px'}"
            ></i>
            <span class="text-h3">{{ deviceTypeInfo?.name }}</span>
            <el-tag :type="statusTagType" effect="light" class="status-tag">
                {{ statusText }}
            </el-tag>
            <el-button class="edit-btn" size="small"  plain @click="handleEdit">
                <el-icon><Edit /></el-icon>
                <span>编辑</span>
            </el-button>
        </div>
        
        <!-- 元信息区域 -->
        <div class="meta-section">
          <div class="meta-grid">
            <!-- 第一列 -->
            <div class="meta-column">
              <div class="meta-item">
                <span class="meta-label">设备类型ID:</span>
                <span class="meta-value code">{{ deviceTypeInfo?.device_type_id }}</span>
              </div>
              
              <div class="meta-item">
                <span class="meta-label">设备类型Code:</span>
                <span class="meta-value code">{{ deviceTypeInfo?.device_type_code || '' }}</span>
              </div>
              
              
            </div>
            
            <!-- 第二列 -->
            <div class="meta-column">
              <div class="meta-item">
                <span class="meta-label">认证类型:</span>
                  {{ getAuthTypeName(deviceTypeInfo?.auth_type)  }}
              </div>
              <div class="meta-item">
                <span class="meta-label">接入类型:</span>
                  {{ accessTypeToLabel(deviceTypeInfo?.access_type)  }}
              </div>
            </div>
        

            <!-- 第三列 -->
            <div class="meta-column">
              <div class="meta-item">
                <span class="meta-label">创建时间:</span>
                <span class="meta-value">{{ formatDate(deviceTypeInfo?.created_at) }}</span>
              </div>
              
              <div class="meta-item">
                <span class="meta-label">更新时间:</span>
                <span class="meta-value">{{ formatDate(deviceTypeInfo?.updated_at) }}</span>
              </div>
              
            </div>
          </div>
          
          <!-- 描述信息 -->
          <div class="description" v-if="deviceTypeInfo?.desc">
            <el-text class="desc-label">设备类型描述:</el-text>
            <el-text class="desc-content">{{ deviceTypeInfo.desc }}</el-text>
          </div>
          
          <!-- 标签信息 -->
          <div class="tags-section" v-if="deviceTypeInfo?.tags?.length">
            <el-text class="tags-label">设备标签:</el-text>
            <div class="tags-container">
              <el-tag 
                v-for="tag in deviceTypeInfo.tags" 
                :key="tag.key" 
                type="info" 
                size="small"
                class="custom-tag"
              >
                {{ tag.key }}: {{ tag.value }}
              </el-tag>
            </div>
          </div>
        </div>
      </div>
      
      <AddUpdate ref="addUpdateRef" />
    </div>
  </template>
  
  <script lang="ts" setup>
  import { computed, ref, nextTick } from 'vue'
  import { Edit } from '@element-plus/icons-vue'
  import { useDeviceStore } from '@/store/modules/device'
  import AddUpdate from './addUpdate.vue'
  import { accessTypeToLabel } from '@/utils/device'
  

  // 组件逻辑
  const deviceStore = useDeviceStore()

  const addUpdateRef = ref<InstanceType<typeof AddUpdate>>()
  
  const deviceTypeInfo = computed(() => deviceStore.deviceTypeInfo)
  
  const getAuthTypeName = (authType: number): string => {
    const authTypeMap: Record<number, string> = {
      1: '一型一密',
      2: '一机一密',
    }
    return authTypeMap[authType] || '未知'
  }
  
  // 状态显示转换
  const statusText = computed(() => {
    const statusMap: Record<number, string> = {
      "-1": '禁用',
      1: '启用',
      2: '维护中'
    }
    return deviceTypeInfo.value?.status !== undefined 
      ? statusMap[deviceTypeInfo.value.status] || '未知'
      : '未知'
  })
  
  const statusTagType = computed(() => {
    const typeMap: Record<number, 'success' | 'danger' | 'warning' | 'info'> = {
      "-1": 'danger',
      1: 'success',
      2: 'warning'
    }
    return deviceTypeInfo.value?.status !== undefined 
      ? typeMap[deviceTypeInfo.value.status] || 'info'
      : 'info'
  })
  
  const handleEdit = (): void => {
    nextTick(() => {
      if (deviceTypeInfo.value) {
        addUpdateRef.value?.showDialog(deviceTypeInfo.value)
      }
    })
  }
  
 
  
  const formatDate = (dateString: string | undefined): string => {
    if (!dateString) return 'N/A'
    try {
      return new Date(dateString).toLocaleString()
    } catch {
      return 'N/A'
    }
  }
  </script>
  
  <style lang="scss" scoped>
  .device-header {
    padding: 16px 0;
    margin-bottom: 24px;
    border-bottom: 1px solid var(--el-border-color-light);
  }
  
  .header-main {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }
  
  .title-section {
    display: flex;
    align-items: center;
    gap: 12px;
    flex-wrap: wrap;
  }
  
  .text-h3 {
    margin: 0;
    padding: 0;
    display: flex;
    align-items: center;
    line-height: 1;
  }
  .device-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 60px;
  }
  
  .device-title {
    margin: 0;
    font-size: 24px;
    font-weight: 600;
    color: var(--el-text-color-primary);
    line-height: 1.3;
  }
  
  .status-tag {
    font-weight: 500;
    margin-left: 8px;
  }
  
  .edit-btn {
    padding: 5px 11px;
    
    span {
      margin-left: 4px;
    }
  }
  
  .meta-section {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }
  
  .meta-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 16px 24px;
  }
  
  .meta-column {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }
  
  .meta-item {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    color: var(--el-text-color-secondary);
    font-size: 14px;
    line-height: 1.5;
  }
  
  .meta-label {
    font-weight: 500;
    flex-shrink: 0;
    width: 100px;
    text-align: right;
  }
  
  .meta-value {
    color: var(--el-text-color-regular);
    
    &.code {
      font-family: monospace;
      word-break: break-all;
    }
  }
  
  .meta-link {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    font-weight: 500;
    transition: color 0.2s;
    
    &:hover {
      color: var(--el-color-primary-light-3);
    }
    
    .el-icon {
      font-size: 12px;
      margin-left: 2px;
    }
  }
  
  .description {
    display: flex;
    gap: 8px;
    margin-top: 8px;
    
    .desc-label {
      font-weight: 500;
      color: var(--el-text-color-secondary);
      flex-shrink: 0;
    }
    
    .desc-content {
      color: var(--el-text-color-regular);
      line-height: 1.5;
    }
  }
  
  .tags-section {
    display: flex;
    gap: 8px;
    margin-top: 8px;
    
    .tags-label {
      font-weight: 500;
      color: var(--el-text-color-secondary);
      flex-shrink: 0;
    }
    
    .tags-container {
      display: flex;
      flex-wrap: wrap;
      gap: 6px;
    }
    
    .custom-tag {
      :deep(.el-tag__content) {
        display: flex;
        align-items: center;
        gap: 4px;
      }
    }
  }
  
  .group-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
  }
  
  @media (max-width: 768px) {
    .meta-grid {
      grid-template-columns: 1fr;
    }
    
    .meta-item {
      align-items: center;
    }
    
    .device-title {
      font-size: 20px;
    }
    
    .description, .tags-section {
      flex-direction: column;
      align-items: flex-start;
    }
  }
</style>