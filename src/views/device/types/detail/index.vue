<template>
  <div class="page-content">
    <div class="detail-header">
      <Bannel></Bannel>
    </div>

    <div class="detail-content">
      <el-tabs v-model="activeName" class="detail-item-tabs" @tab-click="handleClick">
        <el-tab-pane label="关联设备" name="devices">
          <DetailDevice></DetailDevice>
        </el-tab-pane>
        <el-tab-pane label="功能定义" name="model">
          <DetailModel></DetailModel>
        </el-tab-pane>
        <el-tab-pane label="扩展信息" name="extend_info">
          <DetailExtendInfo></DetailExtendInfo>
        </el-tab-pane>
        
        <!-- <el-tab-pane label="自定义数据流" name="topics">
          <DetailTopics></DetailTopics>
        </el-tab-pane> -->
        <!-- <el-tab-pane label="modbus设置" name="modbus">
                <DetailModbus></DetailModbus>
            </el-tab-pane> -->
        <!-- <el-tab-pane label="告警" name="alarm">
          <DetailAlarm></DetailAlarm>
        </el-tab-pane> -->
        <el-tab-pane label="设置" name="settings">
          <DetailSettings></DetailSettings>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import type { TabsPaneContext } from 'element-plus'
  import { useDeviceStore } from '@/store/modules/device'
  import { useWorktabStore } from '@/store/modules/worktab'
  import { router } from '@/router'
  import DetailDevice from './components/detailDevice.vue'
  import DetailModel from './components/detailModel/index.vue'
  import DetailExtendInfo from './components/detailExtendInfo/index.vue'
  import DetailTopics from './components/detailTopics.vue'
  import DetailAlarm from './components/detailAlarm.vue'
  import DetailSettings from './components/detailSettings/index.vue'
  // import DetailAuthInfo from "./components/detailAuthInfo.vue"
  import Bannel from './widget/bannel.vue'
  // import DetailModbus from './components/detailModbus.vue';

  const activeName = ref('devices')
  const deviceStore = useDeviceStore()

  const deviceTypeInfo = computed(() => deviceStore.deviceTypeInfo)

  onMounted(() => {
    activeName.value = 'devices'
    if (deviceTypeInfo.value.device_type_id === '' || deviceTypeInfo.value.device_type_id === undefined) {
      useWorktabStore().removeTab('/device/types/detail')
      router.push({ path: '/device/types' })
    }
    // console.log(detailInfo.value)
  })

  const handleClick = (tab: TabsPaneContext, event: Event) => {
    console.log(tab, event)
  }
</script>

<style lang="scss" scoped>
  .detail-header {
    min-height: 8rem;
  }

  .detail-item-tabs > .el-tabs__content {
    padding: 32px;
    color: #6b778c;
    //   font-size: 50px;
    //   font-weight: 800;
  }
</style>
