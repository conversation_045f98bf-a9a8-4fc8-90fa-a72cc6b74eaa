<template>
  <div class="page-content">
      <el-row :gutter="20">
        <el-col :sm="24" :md="24" :lg="24">
          <my-table-bar
            :showTop="true"
            @search="search"
            @reset="resetForm(searchFormRef)"
            :layout="'search, refresh'"
          >
          <template #top>
            <el-form :model="searchForm" ref="searchFormRef" label-width="82px">
              <el-row :gutter="20">
                <art-form-input label="设备名称" prop="name" v-model="searchForm.name" />
                <art-form-select label="设备类型" prop="device_type_id" v-model="searchForm.device_type_id" :options="deviceTypeList" />  
              </el-row>
            </el-form>
          </template>
          <template #bottom>
            <el-button @click="showImportDevice" size="default" v-ripple>批量导入设备</el-button>
            <el-divider direction="vertical" />
            <el-button @click="showExportDevice" size="default" v-ripple>批量导出设备</el-button>
            <el-divider direction="vertical" />
            <el-button @click="newDevice" size="default" v-ripple>添加设备</el-button>
          </template>
        </my-table-bar>
      </el-col>
    </el-row>
    <el-divider />
      <div class="card-container">
        <el-space wrap>
        <!-- <div class="card-grid"> -->
          <el-card v-for="(item, index) in deviceList" 
                 :key="index" 
                 class="box-card"
                @click="handleCardClick($event, item)"   
              >
                <template #header>
                  <div class="card-header">
                      <!-- <span>{{ getTypeName(item.type) }}</span> -->
                      {{ item.name }}
                      <div  class="enable-class">
                          <div  v-if="item.active_online === 1">
                              <div class="online-class"></div>
                              <el-link type="primary" :underline="false" class="link-btn" >在线</el-link>
                          </div>
                          <div v-else>
                              <div class="offline-class"></div>
                              <el-link type="primary" :underline="false" class="link-btn" >离线</el-link>
                          </div>
                          
                      </div>
                  </div>
              </template>
              <!-- 卡片内容 -->
              <div class="card-content" style="overflow: auto; padding-bottom: 30px; max-height: 250px; padding: 0 20px;">
                  <!-- 这里可以放置卡片的具体内容 -->
                  <p style="margin-bottom: 10px;">设备ID：{{ item.device_id }}</p>
                  <p class="meta-item">
                    <span class="meta-label">设备类型:</span> 
                    <el-link type="primary" class="meta-link" :underline="true" @click="clickToDeviceType(item)">
                      {{ item.device_type_info?.name }}
                    </el-link>
                  </p>
                  <p v-if="item.tags.length  > 0 " class="card-tag-class">
                    <span v-for="tag in item.tags">
                       <el-tag type="info" v-if="tag.value !== ''">{{ tag.value }}</el-tag>
                       <el-tag type="info" v-if="tag.value === ''">无标签</el-tag>
                    </span>
                  </p>
                  <p v-else class="card-tag-class"><span><el-tag type="info">无标签</el-tag></span></p>
                  <!-- <p>设备描述: {{ item.desc }}</p> -->
              </div>

              <template #footer>
                  <div class="footer-div">
                      <el-link type="primary" size="small" class="footer-btn" :underline="false" @click="handleEdit(item)">编辑</el-link>
                      <el-divider direction="vertical" />
                      <el-link type="danger" size="small" class="footer-btn" :underline="false" @click="handleDelete(item)">删除</el-link>
                      <el-divider direction="vertical" />
                      <el-link type="success" size="small" class="footer-btn" :underline="false" @click="handleDetail(item)">详情</el-link>
                  </div>
            </template>
          </el-card>
        </el-space>
        <!-- </div> -->
        <div class="pagination-container" v-if="total > pageSize">
          <div class="pagination-class">
            <el-pagination
                v-model:current-page="currentPage"
                v-model:page-size="pageSize"
                :page-sizes="[10, 20, 50, 100]"
                :background="true"
                layout="total, sizes, prev, pager, next"
                :total="total"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              />
            </div>
        </div>
      </div>
    <UpdateDeviceInfo ref="updateDeviceInfoRef" @success="initDeviceList" />
    <New ref="newRef" @success="initDeviceList" />
  </div>
</template>

<script lang="ts" setup>
import { deviceService } from '@/api/device/device';
import { router } from '@/router';
import { useDeviceStore } from '@/store/modules/device';
import { Device, DeviceType, ExtendInfo, KeyValue } from '@/types/device';
import { ApiStatus } from '@/utils/http/status';
import { Status } from "@/utils/device";
import { FormInstance } from 'element-plus';
import UpdateDeviceInfo from './detail/widget/updateDeviceInfo.vue';
import DeviceDetail from './detail/index.vue';
import New from "./new.vue";
import { typesService } from '@/api/device/typesApi';


const deviceStore = useDeviceStore()
const deviceInfo = computed(() => deviceStore.deviceInfo)

const updateDeviceInfoRef = ref<InstanceType<typeof UpdateDeviceInfo>>();
const newRef = ref<InstanceType<typeof New>>()


// 分页相关变量
const currentPage = ref(1)
const pageSize = ref(8)
const total = ref(0)
const deviceList = ref<Device[]>([])

const searchForm = ref({
  name: "",
  device_type_id: "",
  page: currentPage.value,
  page_size: pageSize.value,
})

const search = async () => {
  let params = { ...searchForm.value }
  let resp = await deviceService.findDeviceList(params)
  if (resp.code === ApiStatus.success) {
    deviceList.value = resp.payload.list
    total.value = resp.payload.total
  }
}


const searchFormRef = ref<FormInstance>();
const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.resetFields()
}

const handleSizeChange = async (val: number) => {
  pageSize.value = val
  searchForm.value.page_size = val
  await search()
}

const handleCurrentChange = async (val: number) => {
  currentPage.value = val
  searchForm.value.page = val
  await search()
} 



const handleDisadabled = async (card: Device) => {
    card.status = Status.Disabled;
    let resp = await deviceService.updateDevice(card)
    if (resp.code === ApiStatus.success) {
      ElMessage.success('禁用成功')
      initDeviceList()
    } else {  
      ElMessage.error("禁用失败:" + resp.message)
    }
  }

  const handleEndabled =async (card: Device) => {
    card.status = Status.Enabled;
    let resp = await deviceService.updateDevice(card)
    if (resp.code === ApiStatus.success) {
      ElMessage.success('启用成功')
      initDeviceList()
    } else {  
      ElMessage.error("启用失败:" + resp.message)
    }
  }

const handleEdit = (item: Device) => {
  console.log(item)
  // deviceStore.setDeviceInfo(item)
  nextTick(() => {
    updateDeviceInfoRef.value?.show(item)
  })  
}

const handleDelete =async (item: Device) => {
  ElMessageBox.confirm(`确定要删除 ${item.name} 吗?`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    let resp = await deviceService.deletDevice({device_id: item.device_id});
    if(resp.code === ApiStatus.success) {
      ElMessage.success('删除成功')
      initDeviceList()
    } else {
      ElMessage.error("删除失败:" + resp.message)
    }
  })
}


const showImportDevice = () => {
  console.log("show import device")
}

const showExportDevice  = () => {
  console.log("show export device")
}

const clickToDeviceType = (item: Device) => {
  deviceStore.setDeviceTypeInfo(item.device_type_info)
  router.push({ path: '/device/types/detail' })
}

const handleCardClick = (event: MouseEvent, card: Device) => {
    // 检查点击是否来自卡片内部的操作按钮
    const target = event.target as HTMLElement;
    if (
      target.closest('.footer-div') || 
      target.closest('.enable-class') ||
      target.closest('.link-btn')
    ) {
      return; // 如果是操作按钮区域，不处理点击
    }
  handleDetail(card);
}

const handleDetail = (item: Device) => {
  deviceStore.setDeviceInfo(item)
  router.push({ path: '/device/detail' })
}

const newDevice = () => {
  // router.push({ path: '/device/new' })
  nextTick(() => {
    newRef.value?.show()
  })
}


const initDeviceList = async () => {
  let params = { ...searchForm.value }
  const res = await deviceService.findDeviceList(params)
  if(res.code === ApiStatus.success) {
    deviceList.value = res.payload.list
    total.value = res.payload.total
  }
}

const deviceTypeList = ref<any[]>([])



const initDeviceTypeList  = async () => {
  const params = {page:1, page_size: 1000}
  const res = await typesService.findDeviceTypeList(params)
  if(res.code === ApiStatus.success) {
    res.payload.list.forEach((item: DeviceType) => {
      deviceTypeList.value.push({label: item.name, value: item.device_type_id})
    })
  } 
}

onMounted( async () => {
  await initDeviceList()
  await initDeviceTypeList()
})


watch(
  () => deviceStore.deviceIndexRefresh,
  (newVal:boolean) => {
    if (newVal) {
      initDeviceList()
      deviceStore.setDeviceIndexRefresh(false)
    }
  }
)

</script>


<style scoped>
/* .page-content { */
  /* padding-bottom: 10px; */
/* } */


.card-container {
  width: 100%;
  margin-bottom: 20px;
}
  
.box-card {
  width: 400px;
  height: 240px;
  position: relative;
}



.box-card:hover {
  cursor: pointer;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
  transform: translateY(-1px);
}


.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}


.footer-div {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: absolute;
    bottom: 0;
    left: 0;
    padding: 12px 20px;
    box-sizing: border-box;
    border-top: 1px solid #EBEEF5;
    background-color: #fff;
}

.footer-btn {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  width: 100px;   /*设置一个固定宽度*/
  text-align: center;
}


.enable-class {
    float: right;
}



.online-class {
  background: #67C23A; 
  border-radius: 50%;
  width: 10px;
  height: 10px;
  float:left;
  margin-top: 3px;
}

.offline-class {
  background: #F56C6C;
  border-radius: 50%;
  width: 10px;
  height: 10px;
  float: left;
  margin-top: 3px;
}

.link-btn {
  float: right;
  width: 30px;
  height: 10px;
  margin-top: 1px;
  margin-left: 10px;
}

.no-border-card {
  border: none !important;
  box-shadow: none !important; /* 如果有阴影的话也一并去除 */
}


.card-tag-class {
  margin-top: 15px;
}


.meta-item {
    /* display: flex; */
    /* align-items: flex-start; */
    gap: 8px;
    /* color: var(--el-text-color-secondary); */
    font-size: 14px;
    line-height: 1.5;
  }
  
  .meta-label {
    font-weight: 500;
    flex-shrink: 0;
    width: 80px;
    text-align: right;
  }
  
  .meta-value {
    color: var(--el-text-color-regular);
    
    &.code {
      font-family: monospace;
      word-break: break-all;
    }
  }
  
  .meta-link {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    font-weight: 500;
    transition: color 0.2s;
    margin-left: 5px;
    &:hover {
      color: var(--el-color-primary-light-3);
    }
    
    .el-icon {
      font-size: 12px;
      margin-left: 2px;
    }
  }

</style>