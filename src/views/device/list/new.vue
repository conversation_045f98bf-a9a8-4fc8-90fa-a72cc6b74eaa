<template>
    <el-dialog
        v-model="dialogVisible"
        title="添加设备"
        width="750px"
        top="5vh"
        @close="handleClose()"
    >
    <div class="page-content">
      <el-form ref="formRef" :model="formData" :rules="rules" label-width="auto">
        <!-- 1. 自定义类型或模板类型 -->
        <ElRow :gutter="20" style="margin-top: 25px">
          <ElCol :xs="24" :sm="24" :md="22" :lg="22" :xl="20">
            <el-form-item label="设备名称" prop="name">
              <el-input v-model="formData.name">
                <template #prepend>
                  <i class="iconfont-sys prepend-icon" v-html="formData.icon" @click="changeIcon"></i>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item label="唯一标识" prop="device_id">
              <el-input v-model="formData.device_id"></el-input>
            </el-form-item>
            
            <el-form-item label="设备类型" prop="device_type_id">
              <el-select v-model="formData.device_type_id" placeholder="请选择">
                <el-option
                  v-for="item in deviceTypeList"
                  :key="item.device_type_id"
                  :label="item.name"
                  :value="item.device_type_id"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="设备描述" prop="desc">
              <el-input
                :autosize="{ minRows: 4, maxRows: 4 }"
                type="textarea"
                maxlength="1000"
                show-word-limit
                v-model="formData.desc"
              />
            </el-form-item>
            <el-form-item label="设备标签" prop="tags">
              <div class="tags-container">
                <el-row :gutter="20" v-for="(tag, index) in formData.tags" :key="index">
                  <el-col :span="11">
                    <el-input v-model="tag.key" placeholder="标签key" />
                  </el-col>
                  <el-col :span="11">
                    <el-input v-model="tag.value" placeholder="标签值" />
                  </el-col>
                  <el-col :span="2">
                    <el-button
                      size="default"
                      :icon="Delete"
                      circle
                      @click="deleteTag(index)"
                    ></el-button>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="24">
                        <el-link type="primary" @click="addTag" :underline="false">+ 添加标签</el-link>
                    </el-col>
                </el-row>
              </div>
            </el-form-item>
          </ElCol>
        </ElRow>
        <el-form-item>
          <div class="form-footer">
            <el-button @click="handleCancel">取消</el-button>
            <el-button type="primary" @click="handleSubmit">提交</el-button>
          </div>
        </el-form-item>
      </el-form>
    </div>
  </el-dialog>
</template>
  
<script setup lang="ts">
import { FormInstance, FormRules } from 'element-plus'
import { router } from '@/router'
import { Delete } from '@element-plus/icons-vue'
import { typesService } from '@/api/device/typesApi'
import { ApiStatus } from '@/utils/http/status'
import { useDeviceStore } from '@/store/modules/device'
import { useWorktabStore } from '@/store/modules/worktab'
import { Device, DeviceType, KeyValue } from '@/types/device'
import { deviceService } from '@/api/device/device'


const emit = defineEmits(['success'])


const devStore  = useDeviceStore()
const dialogVisible = ref(false)
const formRef = ref<FormInstance>()
const formData = ref({
  name: '',
  device_id: '',
  icon: '&#xe784;',
  device_type_id: "",
  desc: '',
  tags: [] as KeyValue[],
})

const rules = reactive<FormRules>({
  name: [
    { required: true, message: '请输入类型名称', trigger: 'blur' },
    { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  device_type_id: [{ required: true, message: '请选择设备类型', trigger: 'blur' }],
})

const changeIcon = () => {
  console.log('change icon')
}

const deviceTypeList = ref<DeviceType[]>([])



const deleteTag = (index: number) => {
  formData.value.tags.splice(index, 1)
}

const addTag = () => {
  formData.value.tags.push({ key: '', value: '' })
}

const resetFormData = ()  => {
  formData.value = {
    name: '',
    device_id: '',
    icon: '&#xe784;',
    device_type_id: "",
    desc: '',
    tags: [] as KeyValue[]
  }
}

const gotoList = () => {
  dialogVisible.value = false
  devStore.setDeviceIndexRefresh(true) // 设置true，返回设备列表页后执行刷新
  devStore.setDeviceInfo({} as Device) // 清空设备详情信息 
  resetFormData()
  useWorktabStore().removeTab('/device/new');
  router.push({ path: '/device/list' })
}

const handleCancel = () => {
  gotoList()
}

const handleSubmit = async () => {
  if (!formRef.value) return
  await formRef.value.validate(async (valid) => {
    if (valid) {
      let res = await deviceService.addDevice(formData.value)
      if (res.code === ApiStatus.success) {
        ElMessage.success('添加成功!')
        // gotoList()
        emit("success")
        handleClose()
      } else {
        ElMessage.error(res.message)
      }
    }
  })
}

const handleClose = () => {
  dialogVisible.value = false
  resetFormData()
}

onMounted(async () => {
  let params = { page: 1, page_size: 100, status: 1 }
  let resp = await typesService.findDeviceTypeList(params)
  if (resp.code === ApiStatus.success) {
    deviceTypeList.value = resp.payload.list;
  }
})

const show = () => {
  dialogVisible.value = true
}

defineExpose({
  show,
})

</script>

<style lang="scss" scoped>
  .prepend-icon {
    font-size: 25px;
    color: rgb(var(--art-primary));
}

    .prepend-icon:hover {
      cursor: pointer;
    }
  
    .tags-container {
      gap: 12px; // 添加行间距
      // display: flex;
      // flex-direction: column;
      width: 100vw;
    }
  
    .form-footer {
      display: flex;
      justify-content: center; // 添加这行使内容水平居中
      width: 100%; // 确保占据全部宽度
      margin-top: 20px; // 可选：添加一些顶部间距
    }
  </style>
  