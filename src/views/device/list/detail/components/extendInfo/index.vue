<template>
    <div>
        <div class="text-h4">更多扩展信息</div>
        <p class="description">
            在设备类型的扩展信息中可添加或编辑扩展信息定义,并且一定要启用。
            <el-link type="primary" href="#" :underline="false">了解详情</el-link>
        </p>
        <el-form v-model="formData" label-width="auto" style="margin-top: 15px; padding:15px" >
            <div v-for="(extendInfo, index) in getEnableExtendInfo()" :key="index">
                <!-- 字符串类型 -->
                <StringFormItem 
                    v-model="formData[extendInfo.identifier]" 
                    v-if="extendInfo.data_type === 'string'" 
                    :extendInfo="extendInfo" 
                    />
                <!-- 数字类型 -->
                <NumberFormItem 
                    v-if="extendInfo.data_type === 'number'"
                    v-model="formData[extendInfo.identifier]" 
                    :extendInfo="extendInfo" 
                    />

                <!-- 布尔类型 -->
                <SwitchFormItem 
                    v-if="extendInfo.data_type === 'switch'"
                    v-model="formData[extendInfo.identifier]" 
                    :extendInfo="extendInfo" 
                    />
                
                <!-- 枚举类型 -->
                <EnumFormItem 
                    v-if="extendInfo.data_type === 'enum'"
                    v-model="formData[extendInfo.identifier]"
                    :extendInfo="extendInfo"
                    />
                <!-- 日期类型 -->
                <DateFormItem
                    v-if="extendInfo.data_type === 'date'"
                    v-model="formData[extendInfo.identifier]" 
                    :extendInfo="extendInfo" 
                    />

            </div>
            <div>
                <el-form-item v-if="Object.keys(formData).length > 0 ">
                    <el-button type="primary" @click="saveForm()">
                        保存
                    </el-button>
                    <el-button >取消</el-button>
                </el-form-item>
            </div>
        </el-form>
    </div>
</template>

<script setup lang="ts">
import { useDeviceStore } from '@/store/modules/device';
import { Device, EnumDataOption, ExtendInfo, NumberDataOption, SwitchDataOption, TextDataOption } from '@/types/device';
import StringFormItem from './widget/StringFormItem.vue';
import NumberFormItem from './widget/NumberFormItem.vue';
import SwitchFormItem from './widget/SwitchFormItem.vue';
import EnumFormItem from './widget/EnumFormItem.vue';
import DateFormItem from './widget/DateFormItem.vue';
import { deviceService } from '@/api/device/device';
import { ApiStatus } from '@/utils/http/status';

const deviceStore = useDeviceStore()
// const { deviceInfo } = storeToRefs(deviceStore); // 保持响应式
const deviceInfo = computed(() => deviceStore.deviceInfo);

const formData = ref<any>(deviceInfo.value.extend_info === null ? {} : deviceInfo.value.extend_info );

const getEnableExtendInfo = (): any[] => {
  return deviceInfo.value.device_type_info?.extend_info?.filter(item => item.enabled).map((item :ExtendInfo) => {
    switch(item.data_type) {
        case 'string': 
            let dataOption  =  item.data_options as TextDataOption;
            if(formData.value[item.identifier] === undefined || formData.value[item.identifier] === null) {
                formData.value[item.identifier] = dataOption.default;
            }
            return { ...item, data_options:  dataOption}
        case 'number': 
            let numDataOption  =  item.data_options as NumberDataOption;
            if(formData.value[item.identifier] === undefined || formData.value[item.identifier] === null) {
                formData.value[item.identifier] = Number(numDataOption.default);
            } 
            return { ...item, data_options: numDataOption}
        case "switch": 
            let switchDataOption  =  item.data_options as SwitchDataOption;
            if(formData.value[item.identifier] === undefined || formData.value[item.identifier] === null) {
                formData.value[item.identifier] = offValue(switchDataOption);
            }
            return { ...item, data_options: switchDataOption}
        case "enum":
            let enumDataOption  =  item.data_options as Object;
            if(formData.value[item.identifier] === undefined || formData.value[item.identifier] === null) {
                formData.value[item.identifier] = "";
            }
            return { ...item, data_options: enumDataOption}
        case "date":
            let dateOption  =  item.data_options as TextDataOption;
            if(formData.value[item.identifier] === undefined || formData.value[item.identifier] === null) {
                formData.value[item.identifier] = dateOption.default;
            }
            return { ...item, data_options:  dateOption}
        default:
            return item;
    }
  })
}

const offValue = (item: SwitchDataOption) : string|number|boolean => {
  if (item.value_type === 'boolean') {
      return false
    } else if (item.value_type === 'number') {
      return 0
    } else {
      return "off"
    }
}



const saveForm = async () => {
    let devInfo =  deviceStore.getDeviceInfo();
    devInfo.extend_info = formData.value;  
    // console.log("formData:", formData.value)
    let resp = await deviceService.updateDevice(devInfo)
    if (resp.code === ApiStatus.success ) {
        ElMessage.success("保存成功")
    } else {
        ElMessage.error(resp.message)
    }
}


</script>