<template>
    <el-form-item :label="extendInfo.name">
      <el-input-number
        v-model="defaultNum"
        :step="Number(extendInfo.data_options.step)"
        :min="Number(extendInfo.data_options.min)"
        :max="Number(extendInfo.data_options.max)"
        :placeholder="extendInfo.desc || ''"
        @onChage="handleInput"
      />
    </el-form-item>
  </template>
  
<script setup lang="ts">

import { ExtendInfo, NumberDataOption } from '@/types/device';

interface Props {
    extendInfo: Omit<ExtendInfo, 'data_options'> & {
        data_options: NumberDataOption; 
    };
    modelValue: number;
}
const props = defineProps<Props>();

const defaultNum = ref(props.modelValue);

const emit = defineEmits(['update:modelValue'])

const handleInput = () => {
    emit('update:modelValue', defaultNum.value)
};

</script>