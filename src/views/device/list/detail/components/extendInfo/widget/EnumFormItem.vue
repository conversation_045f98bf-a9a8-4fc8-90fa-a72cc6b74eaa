<template>
    <el-form-item :label="extendInfo.name" :prop="extendInfo.identifier">
        <el-select
            v-model="defaultVale"
            @change="handleInput"
            :placeholder="extendInfo.desc || ''"
        >
        <el-option
            v-for="(item, index) in selectOptions"
            :key="index"
            :label="item.value"
            :value="item.key"
        />
        </el-select>
    </el-form-item>
</template>

<script setup lang="ts">
import { ExtendInfo } from '@/types/device';

interface Props {
    extendInfo: Omit<ExtendInfo, 'data_options'> & {
        data_options: Object; // 强制要求 data_options 是 TextDataOption
    };
    modelValue: string // v-model 绑定的值
}
const props = defineProps<Props>();

console.log(props);

const emit = defineEmits(['update:modelValue'])

const defaultVale = ref(props.modelValue); 
const selectOptions = Object.entries(props.extendInfo.data_options || {} ).map(([key, value]) => ({
        key, value,
}));;
const handleInput = () => {
    emit('update:modelValue', defaultVale.value);
};
</script>