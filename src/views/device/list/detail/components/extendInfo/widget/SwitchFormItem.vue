<template>
    <el-form-item :label="extendInfo.name" >
      <el-switch
        v-model="defaultValue"
        @change="handleChange"
        :active-value="onValue()" 
        :inactive-value="offValue()" 
        :active-text="extendInfo.data_options.on_text"
        :inactive-text="extendInfo.data_options.off_text"

      />
    </el-form-item>
  </template>
  
  <script setup lang="ts">
  import type { ExtendInfo, SwitchDataOption } from '@/types/device';
  
  interface Props {
    extendInfo: Omit<ExtendInfo, 'data_options'> & {
      data_options: SwitchDataOption; // 强制为布尔类型配置
    };
    modelValue: string | boolean | number; // v-model 绑定的值 
  }
  
  
  const props = defineProps<Props>();
  const emit = defineEmits(['update:modelValue']);

  const offValue = () => {
  if (props.extendInfo.data_options.value_type === 'boolean') {
      return false
    } else if (props.extendInfo.data_options.value_type === 'number') {
      return 0
    } else {
      return "off"
    }
  }

  const onValue = () => {
    if (props.extendInfo.data_options.value_type === 'boolean') {
      return true
    } else if (props.extendInfo.data_options.value_type === 'number') {
      return 1
    } else {
      return "on"
    }
  }

  const defaultValue = ref(props.modelValue === undefined ? String(offValue()): String(props.modelValue));
  // console.log("switch:", defaultValue.value)

  const handleChange = () => {
    if(props.extendInfo.data_options.value_type === 'boolean') {
      emit('update:modelValue', defaultValue.value === 'true');
    } else if(props.extendInfo.data_options.value_type === 'number') {
      emit('update:modelValue', Number(defaultValue.value));
    } else {
      emit('update:modelValue', defaultValue.value);
    }
  };




  </script>