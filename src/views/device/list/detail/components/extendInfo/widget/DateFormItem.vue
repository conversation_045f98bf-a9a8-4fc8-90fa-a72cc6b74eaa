<template>
    <el-form-item :label="extendInfo.name">
        <!-- <el-input 
            v-model="defaultVale"
            @update:model-value="handleInput"
            :placeholder="extendInfo.desc || ''"
        ></el-input> -->
        <el-date-picker
            v-model="defaultVale"
            type="date"
            placeholder="选择日期"
            format="YYYY/MM/DD"
            value-format="YYYY-MM-DD"
            @change="handleInput"
        />
    </el-form-item>
</template>

<script setup lang="ts">
import { ExtendInfo, TextDataOption } from '@/types/device';

interface Props {
    extendInfo: Omit<ExtendInfo, 'data_options'> & {
        data_options: TextDataOption; // 强制要求 data_options 是 TextDataOption
    };
    modelValue: string // v-model 绑定的值
}
const props = defineProps<Props>();
// console.log("date :", props)

const emit = defineEmits(['update:modelValue'])

const defaultVale = ref(props.modelValue);

const handleInput = () => {
    emit('update:modelValue', defaultVale.value);
};
</script>