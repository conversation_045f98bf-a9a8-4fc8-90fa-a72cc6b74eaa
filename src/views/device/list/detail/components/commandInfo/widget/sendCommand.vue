<template>
    <el-dialog 
        v-model="visible" 
        title="下发命令" 
        width="800px"
        class="json-dialog"
        :close-on-click-modal="false"
        @close="closeDialog">
        <el-form label-width="auto" >
            <el-form-item label="命令">
                <el-select v-model="formData.identifier" @change="selectCommand">
                    <el-option v-for="item in commandOptions" 
                        :key="item.command_id" 
                        :label="item.name" 
                        :value="item.identifier" />
                </el-select>
            </el-form-item>
            <el-form-item label="命令参数JSON">
                <JsonEditor v-model="jsonDataObj"   :key="jsonDataKey" />
            </el-form-item>
        </el-form>
        <template #footer>
            <el-button @click="closeDialog">取消</el-button>
            <el-button 
                type="primary" 
                @click="handleSubmit"
                :loading="submitting">
                提交
            </el-button>
        </template>
    </el-dialog>
</template>


<script setup lang="ts">
import { Command, Device, NumberDataOption, SwitchDataOption, TextDataOption } from '@/types/device'
import JsonEditor from "@/components/custom/jsonEditor/index.vue"
import { getDefaultDevice } from '@/utils/device'
import { deviceService } from '@/api/device/device'
import { ApiStatus } from '@/utils/http/status'
import { commandService } from '@/api/device/command'

const formData = reactive({
    identifier: "",
    jsonData: "{}",
    command: "",
})

const jsonDataObj = ref({})
const jsonDataKey = ref(0)


const visible = ref(false)
const commandOptions = ref<Command[]>([])
const submitting = ref(false)

const deviceInfo = ref<Device>(getDefaultDevice())

const handleSubmit = async () => {
    try {
        submitting.value = true
        // 验证 JSON
        
        formData.jsonData = JSON.stringify(jsonDataObj.value, null, 2)
        const sendData = {
            device_id: deviceInfo.value?.device_id,
            command: formData.command,
            data: formData,
        }
        let resp = await deviceService.downCommand(sendData)
        if(resp.code === ApiStatus.success) {
            ElMessage.success("提交成功")
            closeDialog()       
        } else {
            ElMessage.success(resp.message)
        }
    } catch (e) {
        console.log(e)
    } finally {
        submitting.value = false
    }
}

const selectCommand = () => {
    let command = commandOptions.value.find(item => item.identifier === formData.identifier)
    if(command) {
        let params = {} as any;
        command.send_params.forEach(item => {
            if (item.data_type === "number") {
                let dataoption = item.data_options as NumberDataOption;
                params[item.identifier] = dataoption.default;
            } else if (item.data_type === "string") {
                let dataoption = item.data_options as TextDataOption;
                params[item.identifier] = dataoption.default;
            } else if (item.data_type === "switch") {
                let dataoption = item.data_options as SwitchDataOption;
                params[item.identifier] = dataoption.off_text === "on" ? true : false;
            } else if (item.data_type === "enum") {
                params[item.identifier] = "";
            } else if (item.data_type === "object") {
                params[item.identifier] = {};
            } else {
                params[item.identifier] = "";
            }
        })
        formData.command = command.identifier;
        jsonDataObj.value = params;
        jsonDataKey.value += 1;
    } else {
        console.log("not found command");
    }
}

const closeDialog = () => {
    visible.value = false
}

const params = ref({
    device_type_id: ""
})

const initCommandData = async () => {
    let resp = await commandService.findCommandList(params.value)
    if (resp.code === ApiStatus.success) {
        commandOptions.value = resp.payload.list
    }
}

const show =async (item: Device) => {
    deviceInfo.value = item
    params.value.device_type_id = item.device_type_info.device_type_id
    await initCommandData()
    visible.value = true
}


defineExpose({
    show,
})
</script>