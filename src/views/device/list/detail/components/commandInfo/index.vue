<template>
    <div class="attribute-div-class">
        <div class="title-and-button-container">
            <div class="header-container">
                <!-- <el-button size="default" plain @click="showAttrJson">
                    <i class="iconfont-sys" v-html="'&#xe654;'"></i>
                    查看属性json
                </el-button> -->
                <el-button @click="showDownCommandInfo()" class="refresh-btn" plain>
                    <i class="iconfont-sys" v-html="'&#xe79c;'"></i>
                    下发命令
                </el-button>
            </div>
            <div class="extra-container">
                <div class="time-range-container">   
                    <el-date-picker
                        v-model="timeRange"
                        type="datetimerange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        value-format="YYYY-MM-DD HH:mm:ss"
                        class="shorter-date-picker"
                        @change="initCommandData()"
                    />
                    <el-button class="refresh-btn" @click="initCommandData()"  plain>
                        <template #icon><el-icon><Refresh /></el-icon></template>
                        刷新
                    </el-button>      
                </div>
            </div>
        </div>
        <el-table
            :data="commandList"
            style="width: 100%"
            stripe
            v-loading="loading"
        >
            <el-table-column prop="identifier" label="命令标识符"  />
            <el-table-column prop="name" label="命令名称"  />
            <el-table-column prop="send_ts" label="命令下发时间"  >
                <template #default="scope">
                    {{ moment(scope.row.send_ts).format('YYYY-MM-DD HH:mm:ss') }}
                </template>
            </el-table-column>
            <el-table-column prop="reply_ts" label="命令回复时间" >
                <template #default="scope">
                    {{ 
                        scope.row.reply_ts !== undefined && scope.row.reply_ts !== 0 ? 
                            moment(scope.row.reply_ts).format('YYYY-MM-DD HH:mm:ss') : "" 
                    }}
                </template>
            </el-table-column>
            <el-table-column prop="action" label="查看命令"  >
                <template #default="scope">
                    <el-link @click="viewCommand(scope.row)">查看</el-link>
                </template>
            </el-table-column>
        </el-table>

        <div class="pagination-container">
            <el-pagination
                v-model:current-page="currentPage"
                v-model:page-size="pageSize"
                :page-sizes="[10, 20, 50, 100]"
                :background="true"
                layout="total, sizes, prev, pager, next"
                :total="total"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                />
        </div>
        <SendCommand  ref="sendCommandRef"/>
    </div>
</template>

<script lang="ts" setup>
import { useDeviceStore } from '@/store/modules/device';
import { Command } from '@/types/device';
import { ApiStatus } from '@/utils/http/status';

import { commandService } from '@/api/device/command';
import SendCommand from './widget/sendCommand.vue';
import moment from 'moment';


const loading = ref(false)
const sendCommandRef = ref<InstanceType<typeof SendCommand>>()

const commandList = ref<Command[]>([])


const deviceStore = useDeviceStore()
const deviceInfo = computed(() => deviceStore.deviceInfo)

// 分页相关变量
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)


const showDownCommandInfo = () => {
    nextTick(() => {
        sendCommandRef.value?.show(deviceInfo.value)
    })
}




const viewCommand = (row: Command) => {

}

const timeRange = ref([
  moment().startOf('day').format('YYYY-MM-DD HH:mm:ss'), 
  moment().endOf('day').format('YYYY-MM-DD HH:mm:ss')
])

const dayStrToNanoUnix = (dateStr: string) => {
    let curDate = new Date(dateStr)
    return curDate.getTime()
}


const queryParams = ref({
    page: currentPage.value,
    page_size: pageSize.value,
    device_id: deviceInfo.value.device_id ,
    command_id: "",
    start: dayStrToNanoUnix(timeRange.value[0]),
    end: dayStrToNanoUnix(timeRange.value[1]),
})

const initCommandData = async () => {
    queryParams.value.page = currentPage.value
    queryParams.value.page_size = pageSize.value
    queryParams.value.start =  dayStrToNanoUnix(timeRange.value[0])
    queryParams.value.end = dayStrToNanoUnix(timeRange.value[1])
    let params = queryParams.value;
    loading.value = true
    let resp = await commandService.history(params)
    if (resp.code === ApiStatus.success) {
        commandList.value = resp.payload.list
        total.value = resp.payload.total
        loading.value = false
    } else {
        loading.value = false
    }
}


const handleSizeChange = async (val: number) => {
    pageSize.value = val
    await initCommandData()
}

const handleCurrentChange = async (val: number) => {
    currentPage.value = val
    await initCommandData()   
}


onMounted( async () => {
    await initCommandData()
})

</script>

<style lang="scss" scoped>
.down-class {
    cursor: pointer;
    font-size: 18px;
    &:hover {
        color: #66b1ff;
    }
}

.attribute-div-class {
    width: 100%;
    display: flex;
    flex-direction: column;
}

.title-and-button-container {
    display: flex;
    justify-content: space-between;
    align-items: center; /* 垂直居中 */
    // margin-top: 10px;
}


.header-container {
    display: flex;
    justify-content: space-between;
    align-items: center; /* 垂直居中 */
    margin-top: 10px;
}


.extra-container {
    display: flex;
    justify-content: flex-end;
}



.time-range-container {
    display: flex;
    align-items: center;
    gap: 10px; /* 添加间距 */
}



.shorter-date-picker {
    width: 380px;
}

.refresh-btn {
    height: 38px;
    margin-left: 0;
    /* 确保图标和文本对齐 */
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

</style>