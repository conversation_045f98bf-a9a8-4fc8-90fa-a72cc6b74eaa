<template>
    <el-dialog 
        v-model="visible" 
        title="下发属性" 
        width="800px"
        class="json-dialog"
        :close-on-click-modal="false"
        @close="closeDialog">
        
        <div class="dialog-content">
            <div class="json-toolbar">
                <!-- <el-tooltip content="格式化 JSON" placement="top">
                    <el-button 
                        @click="formatJson"
                        :icon="MagicStick">
                    </el-button>
                </el-tooltip> -->
                <el-tooltip content="复制 JSON" placement="top">
                    <el-button  
                        @click="copyJson"
                        :icon="DocumentCopy">
                    </el-button>
                </el-tooltip>
                <el-tooltip content="清空内容" placement="top">
                    <el-button  
                        @click="clearJson"
                        :icon="Delete">
                    </el-button>
                </el-tooltip>
            </div>
            
            <div class="json-container">
                <JsonEditor v-model="jsonData"   :key="jsonDataKey" />
                <!-- <el-input 
                    v-model="jsonData" 
                    type="textarea" 
                    :rows="18" 
                    class="json-editor"
                    placeholder="请输入 JSON 数据..."
                    spellcheck="false"
                    resize="none">
                </el-input> -->
            </div>
            
            <div v-if="errorMessage" class="json-error">
                <el-alert 
                    :title="errorMessage" 
                    type="error" 
                    :closable="false"
                    show-icon>
                </el-alert>
            </div>
        </div>
        
        <template #footer>
            <el-button @click="closeDialog">取消</el-button>
            <el-button 
                type="primary" 
                @click="handleSubmit"
                :loading="submitting">
                提交
            </el-button>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { DocumentCopy, Delete } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { deviceService } from '@/api/device/device'
import { ApiStatus } from '@/utils/http/status'

const visible = ref(false)
const jsonData = ref({})
const jsonDataKey = ref(0)
const errorMessage = ref("")
const submitting = ref(false)


const deviceID = ref("");



const closeDialog = () => {
    jsonData.value = {}
    errorMessage.value = ""
    clearJson()
    visible.value = false
}



const copyJson = () => {
    navigator.clipboard.writeText(JSON.stringify(jsonData.value))
    ElMessage.success("已复制到剪贴板")
}

const clearJson = () => {
    jsonData.value = {}
    jsonDataKey.value = jsonDataKey.value + 1
    errorMessage.value = ""
}

const handleSubmit = async () => {
    try {
        submitting.value = true
        // 验证 JSON
        // let pjdata  = JSON.parse(jsonData.value)
        errorMessage.value = ""
        // 
        const sendData = {
            device_id: deviceID.value,
            data: jsonData.value,
        }
        let resp = await deviceService.downAttribute(sendData)
        if(resp.code === ApiStatus.success) {
            ElMessage.success("提交成功")
            closeDialog()       
        } else {
            ElMessage.success(resp.message)
        }
    } catch (e) {
        errorMessage.value = "JSON 格式错误: " 
    } finally {
        submitting.value = false
    }
}

const show = (dev_id: string ,initialData = {}) => {
    clearJson()
    jsonData.value  = {}
    deviceID.value = dev_id
    jsonData.value = initialData
    visible.value = true
}

defineExpose({
    show,
})
</script>

<style lang="scss" scoped>
.json-dialog {
    .dialog-content {
        padding: 0 10px;
    }
    
    .json-toolbar {
        margin-bottom: 10px;
        display: flex;
        gap: 8px;
        
        .el-button {
            background-color: var(--el-fill-color-light);
            border: none;
            
            &:hover {
                background-color: var(--el-fill-color);
            }
        }
    }
    
    .json-container {
        border-radius: 4px;
        overflow: hidden;
        border: 1px solid var(--el-border-color);
        
        .json-editor {
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 14px;
            line-height: 1.5;
            
            :deep(.el-textarea__inner) {
                background-color: #f8f8f8;
                color: #333;
                padding: 12px;
                
                &:focus {
                    background-color: #fff;
                }
            }
        }
    }
    
    .json-error {
        margin-top: 12px;
    }
    
    :deep(.el-dialog__body) {
        padding-top: 10px;
    }
    
    :deep(.el-dialog__footer) {
        padding-top: 0;
        border-top: 1px solid var(--el-border-color-lighter);
    }
}
</style>