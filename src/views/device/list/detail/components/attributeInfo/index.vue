<template>
    <div class="attribute-div-class">
        <div class="title-and-button-container">
            <div class="header-container">
                <el-button size="default" plain @click="showAttrJson">
                    <i class="iconfont-sys" v-html="'&#xe654;'"></i>
                    查看属性json
                </el-button>
                <el-button @click="showDownInfo()" size="default" plain>
                    <i class="iconfont-sys" v-html="'&#xe79c;'"></i>
                    下发属性
                </el-button>
            </div>
            <div class="extra-container">
                <el-button @click="initAttributeData()" size="default" plain>
                    <template #icon><el-icon><Refresh /></el-icon></template>
                    刷新
                </el-button>
            </div>
        </div>
        <el-table
            :data="properties"
            style="width: 100%"
            stripe
            v-loading="loading"
        >
            <el-table-column prop="attr_type" label="属性类型" >
                <template #default="scope">
                    <el-text class="mx-1" :type="getAttrTypeCode(scope.row)" size="small">
                    {{ AttrTypeToLabel(scope.row.attr_type) }}
                    </el-text>
                </template>
            </el-table-column>
            <el-table-column prop="identifier" label="属性标识符"  />
            <el-table-column prop="name" label="属性名称"  />
            <el-table-column prop="value" label="属性值"  >
                <template #default="scope">
                    {{ getIdentifyValue(scope.row.identifier) }}
                </template>
            </el-table-column>
            <el-table-column prop="value_updated_at" label="更新时间" >
                <template #default="scope">
                    {{ getIdentifyUpdateTime(scope.row.identifier)  }} {{ getAttrUnit(scope.row) }}
                </template>
            </el-table-column>
            <el-table-column prop="history" label="历史数据">
                <template #default="scope">
                    <span title="查看历史数据" class="down-class" @click="viewHistory(scope.row)">
                        <i class="iconfont-sys" v-html="'&#xe833;'"></i>
                    </span>
                </template>
            </el-table-column>
            <el-table-column label="操作" width="120">
              <template #default="scope">
                <span title="下发属性" class="down-class"  @click="downAttribute(scope.row)">
                    <i class="iconfont-sys" v-html="'&#xe79c;'"></i>
                </span>
              </template>
            </el-table-column>
        </el-table>
        <DownAttr ref="downAttrRef" />
        <AttrHistory ref="historyRef" />

    </div>
</template>

<script lang="ts" setup>
import { attributeService } from '@/api/device/attribute';
import { useDeviceStore } from '@/store/modules/device';
import { Attribute, Device, EnumDataOption, KeyValue, NumberDataOption, Param, SwitchDataOption, TextDataOption } from '@/types/device';
import { AttrTypeToLabel, DataTypeToLabel, AttrTypeColorType } from '@/utils/device'
import { ApiStatus } from '@/utils/http/status';
import DownAttr from './widget/DownAttr.vue'
import AttrHistory from '@/components/custom/Dialog/AttrHistory.vue';
import moment from 'moment';

const downAttrRef = ref<InstanceType<typeof DownAttr>>()
const historyRef = ref<InstanceType<typeof AttrHistory>>()


const loading = ref(false)
const properties = ref<Attribute[]>([])
    const attrHistoryMap = ref<Record<string, any>>({});

type ElTextType = 'success' | 'warning' | 'danger' | 'info' | 'primary' | '';
const getAttrTypeCode = (row: Attribute) : ElTextType => {
    return AttrTypeColorType(row.attr_type) as ElTextType; 
}
  
const deviceStore = useDeviceStore()
const deviceInfo = computed(() => deviceStore.deviceInfo)

const showDownInfo = () => {
    nextTick(() => {
        downAttrRef.value?.show(deviceInfo.value.device_id)
    })
}

const showAttrJson = () => {
    ElMessage.warning("暂时未实现")
}


const downAttribute = async  (row: Attribute) => {
    let data :any = {};
    if (row.data_type === 'number') {
        const dataOptions = row.data_options as NumberDataOption;
        data[row.identifier] = Number(dataOptions?.default);
    } else if (row.data_type === 'string') {
        const dataOptions = row.data_options as TextDataOption;
        data[row.identifier] = dataOptions?.default;
    } else if (row.data_type === 'switch') {
        const dataOptions = row.data_options as SwitchDataOption;
        if(dataOptions.value_type === 'bool') {
            data[row.identifier] = true;
        } else if (dataOptions.value_type === 'number') {
            data[row.identifier] = 1;
        } else if (dataOptions.value_type === 'string') {
            data[row.identifier] = 'on';
        }
    } else if (row.data_type === 'enum') {
        data[row.identifier] = "";
    } else if (row.data_type === 'object') {
        let tmpMap: any = {};
        const paramList = row.data_options as Param[];
        if(paramList?.length > 0) {
            paramList?.forEach((item: Param) => {
                if(item.data_type === 'number') {
                    const itemDataOptions = item.data_options as NumberDataOption;
                    tmpMap[item.identifier] = Number(itemDataOptions.default);
                } else {
                    tmpMap[item.identifier] = "";
                }
            })
        }
        data[row.identifier] = tmpMap;
    } else if (row.data_type === 'list') {
        let tmpList: any = [];
        const paramList = row.data_options as Param[];
        if(paramList?.length > 0) {
            paramList?.forEach((item: Param) => {
                if(item.data_type === 'number') {
                    const itemDataOptions = item.data_options as NumberDataOption;
                    tmpList.push(Number(itemDataOptions.default))
                } else {
                    tmpList.push("")
                }
            })
        }
        data[row.identifier] = tmpList;
    } else {
        ElMessage.warning("不支持的数据类型")
        return 
    }
    nextTick(() => {
        console.log("data:", data)
        downAttrRef.value?.show(deviceInfo.value.device_id, data)
    })
}

const viewHistory = (item: Attribute) => {
    nextTick(() => {
        historyRef.value?.show(item, deviceInfo.value.device_id)
    })
}

const selectTime = ref<[Date, Date] | null>(null);
const inittoday = () => {
    const start = new Date();
    start.setHours(0, 0, 0, 0);
    const end = new Date();
    end.setHours(23, 59, 59, 999);
    selectTime.value = [start, end];
}

const getOneHistory = async () => {
    inittoday();
    properties.value?.forEach(async (item: Attribute) => {  
        let params = {
            device_id: deviceInfo.value.device_id,
            attr_id: item.attr_id,
            start:  selectTime.value![0].getTime() , // moment().startOf('day').valueOf()  * 1000000,
            end:   selectTime.value![1].getTime() ,//moment().endOf('day').valueOf() * 1000000,
            page: 1,
            page_size: 1,
        }
        let resp = await attributeService.history(params);
        if (resp.code === ApiStatus.success) {
            if(resp.payload?.list.length > 0) {
                attrHistoryMap.value[item.identifier] = resp.payload.list[0];
            } 
        } else {
            console.log(resp.message)
        }
    })
}

const getAttrUnit = (item: Attribute) => {
    if(item.data_type === 'number' ) {
        const numberOption = item.data_options as NumberDataOption;
        if (numberOption?.unit) {
            return numberOption.unit;
        } else {
            return ""
        }
    } else {
        return ""
    }
}

const getIdentifyUpdateTime = (identifier: string ) => {
    if (attrHistoryMap.value[identifier] === undefined) {
        return "--";
    }
    if (attrHistoryMap.value[identifier]["ts"] === undefined) {
        return "--";
    }
    return moment.unix(attrHistoryMap.value[identifier]["ts"]).format("YYYY-MM-DD HH:mm:ss");
}

const getIdentifyValue = (identifier: string ) => {
    if (attrHistoryMap.value[identifier] === undefined) {
        return "";
    }
    return attrHistoryMap.value[identifier][identifier]
}

const queryParams = ref({
    page: 1,
    page_size: 100,
    device_type_id: deviceInfo.value.device_type_info.device_type_id ,
    attr_id: "",
})
const initAttributeData = async () => {
    let params = queryParams.value;
    let resp = await attributeService.findAttributeList(params)
    if (resp.code === ApiStatus.success) {
      properties.value = resp.payload.list
      await getOneHistory();
    }
}

onMounted( async () => {
    await initAttributeData()
})

</script>

<style lang="scss" scoped>
.down-class {
    cursor: pointer;
    font-size: 18px;
    &:hover {
        color: #66b1ff;
    }
}

.attribute-div-class {
    width: 100%;
    display: flex;
    flex-direction: column;
}

.title-and-button-container {
    display: flex;
    justify-content: space-between;
    align-items: center; /* 垂直居中 */
    // margin-top: 10px;
}


.header-container {
    display: flex;
    justify-content: space-between;
    align-items: center; /* 垂直居中 */
    margin-top: 10px;
}

/* 可以移除原来的 .header 和 .extra 的 margin-top */
.extra-container {
    display: flex;
    justify-content: flex-end;
}
</style>