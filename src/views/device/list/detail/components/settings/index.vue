<template>
    <div class="setting-class">
        <div class="text-item-class">
            <div class="text-h3">设备描述</div>
            <div class="text-desc">
                {{ deviceInfo.desc }}
            </div>
            <div class="text-btn">
                <el-button plain size="default"  @click="showUpdateDeviceInfo">编辑</el-button>
            </div>
        </div>

        <div class="text-item-class">
            <div class="text-h3">
                设备类型
            </div>
            <div class="text-desc">
                <el-tag type="primary">
                    {{ deviceInfo.device_type_info?.name }}
                </el-tag>
            </div>
            <div class="text-btn">
                <el-button plain size="default" @click="changeDeviceType">
                    修改设备类型
                </el-button>
            </div>
        </div>

        <!-- 设备分组 -->
        <div class="text-item-class">
            <div class="text-h3">
                设备分组
            </div>
            <div class="text-desc">
                <el-tag type="primary" v-for="(item, index) in deviceInfo.group_info" :key="index" style="margin-right: 10px">
                    {{ item.name }}
                </el-tag>
            </div>
            <div class="text-btn">
                <el-button plain size="default" @click="changeDeviceGroup">
                    修改设备分组
                </el-button>
            </div>
        </div>

        <div class="text-item-class">
            <div class="text-h3">
                设备标签
            </div>
            <div class="text-desc">
                <el-table :data="deviceInfo.tags">
                    <el-table-column prop="key" label="标签名称"></el-table-column>
                    <el-table-column prop="value" label="标签值"></el-table-column>
                </el-table>
            </div>
            <div class="text-btn">
                <el-button plain size="default"  @click="showUpdateDeviceInfo">添加/编辑标签</el-button>
            </div>
        </div>

        <div class="text-item-class">
            <div class="text-h3">
                设备码
            </div>
            <div class="text-desc">
                将设备码贴在外包装或产品手册中，用户可在 App 中通过设备码主动添加设备。
            </div>
            <div class="text-btn">
                <el-button plain size="default" >查看</el-button>
            </div>
        </div>


        <UpdateDeviceInfo ref="updateDeviceInfoRef" />
        <ChangeDeviceType ref="changeDeviceTypeRef"  />
        <ChangeDeviceGroup ref="changeDeviceGroupRef"  />
   
    </div>
</template>

<script setup lang="ts">
import { useDeviceStore } from '@/store/modules/device';
import UpdateDeviceInfo from '../../widget/updateDeviceInfo.vue';
import ChangeDeviceType from './widget/ChangeDeviceType.vue';
import ChangeDeviceGroup from './widget/ChangeDeviceGroup.vue';

const updateDeviceInfoRef = ref<InstanceType<typeof UpdateDeviceInfo>>()
const changeDeviceTypeRef = ref<InstanceType<typeof ChangeDeviceType>>()
const changeDeviceGroupRef = ref<InstanceType<typeof ChangeDeviceGroup>>()

const deviceStore = useDeviceStore()
const deviceInfo  = computed(() => deviceStore.deviceInfo)

const showUpdateDeviceInfo = () => {
    nextTick(() => {
        updateDeviceInfoRef.value?.show(deviceInfo.value)
    })
}


const changeDeviceType = () => {
    nextTick(() => {
        changeDeviceTypeRef.value?.show(deviceInfo.value)
        // changeDeviceTypeRef.value?.show(deviceInfo.value)
    })
}

const changeDeviceGroup = () => {
    nextTick(() => {
        changeDeviceGroupRef.value?.show(deviceInfo.value)
    })
}

</script>

<style lang="css" scoped>
.setting-class { 
    width: 100%;
    margin: 0 auto;
    background-color: var(--art-background-color);
    padding-left: 20px;
    padding-right: 20px;
    /* padding-top: 20px; */
    padding-bottom: 50px;
}


.text-item-class {
    margin-top: 25px;
}

.text-btn {
    margin-top: 10px;

}

</style>