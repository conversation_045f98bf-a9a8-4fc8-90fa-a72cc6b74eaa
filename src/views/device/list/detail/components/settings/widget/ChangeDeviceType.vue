<template>
    <el-dialog
        v-model="dialogVisible"
        title="编辑设备类型"
        width="750px"
        top="5vh" 
        @close="handleClose()"
    >
        <el-form ref="formRef" :model="formData" :rules="rules" label-width="auto">
            <el-form-item label="设备类型" prop="device_type_id">
                <!-- <el-input v-model="formData.device_type_id"></el-input> -->
                <el-select v-model="formData.device_type_id" placeholder="请选择设备类型">
                    <el-option
                        v-for="item in deviceTypeList"
                        :key="item.id"
                        :label="item.name"
                        :value="item.device_type_id"
                    ></el-option>
                </el-select>
            </el-form-item>
        </el-form>
        <template #footer>
            <span class="dialog-footer">
            <el-button @click="handleClose">取消</el-button>
            <el-button type="primary" @click="handleUpdate">确定</el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
import { deviceService } from '@/api/device/device';
import { typesService } from '@/api/device/typesApi';
import { useDeviceStore } from '@/store/modules/device';
import { Device, DeviceType } from '@/types/device';
import { ApiStatus } from '@/utils/http/status';
import { FormInstance, FormRules } from 'element-plus';


const emit = defineEmits(['success'])

const deviceStore = useDeviceStore()
const deviceInfo  = computed(() => deviceStore.deviceInfo)

const deviceTypeList = ref<DeviceType[]>([])

const dialogVisible = ref(false)
const formRef = ref<FormInstance>()
const formData = ref({
    device_type_id: "",
})
const rules = reactive<FormRules>({
    device_type_id: [{ required: true, message: '请选择设备类型', trigger: 'blur' }],
})



const handleClose = () => {
    formRef.value?.resetFields()
    formData.value = {
        device_type_id: ""
    }
    dialogVisible.value = false
}

const setCurDevInfo = () => {
    let selectDeviceType = deviceTypeList.value.find((item) => item.device_type_id === formData.value.device_type_id)
    let tmpDev = deviceStore.getDeviceInfo()
    tmpDev.device_type_id = formData.value.device_type_id;
    if (selectDeviceType !== undefined) {
        tmpDev.device_type_info = selectDeviceType;
    }
    deviceStore.setDeviceInfo(tmpDev)
}

const handleUpdate = async () => {
    if (!formRef.value) return
    await formRef.value.validate(async (valid) => {
        if (valid) {
            let res = await deviceService.updateDevice(deviceInfo.value)
            if (res.code === ApiStatus.success) {
                ElMessage.success('修改成功!')
                setCurDevInfo()
                emit("success")
                handleClose()
            } else {
                ElMessage.error(res.message)
            }
        }
    })
}


const initDeviceTypeList = async () => {
    let res = await typesService.findDeviceTypeList({})
    if (res.code === ApiStatus.success) {
        deviceTypeList.value = res.payload.list;
    } else {
        ElMessage.error(res.message)
    }
}

const show = async (item: Device) => {
    await initDeviceTypeList()
    formData.value.device_type_id = item.device_type_id;
    dialogVisible.value = true
}


defineExpose({
    show,
})

</script>

<style scoped>
.tags-container {
    gap: 12px; 
    width: 100vw;
}
</style>