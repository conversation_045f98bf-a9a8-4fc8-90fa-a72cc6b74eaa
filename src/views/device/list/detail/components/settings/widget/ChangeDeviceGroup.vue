<template>
    <el-dialog
        v-model="dialogVisible"
        title="编辑设备分组"
        width="750px"
        top="5vh"  
        @close="handleClose()"
    >
        <el-form ref="formRef" :model="formData" :rules="rules" label-width="auto">
            <el-form-item label="设备分组" prop="device_type_id">
                <el-select multiple v-model="formData.groups" placeholder="请选择设备分组">
                    <el-option
                        v-for="(item, index) in groupInfoList"
                        :key="index"
                        :label="item.name"
                        :value="item.group_id"
                    ></el-option>
                </el-select>
            </el-form-item>
        </el-form>
        <template #footer>
            <span class="dialog-footer">
            <el-button @click="handleClose">取消</el-button>
            <el-button type="primary" @click="handleUpdate">确定</el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
import { deviceService } from '@/api/device/device';
import { groupService } from '@/api/device/group';
import { typesService } from '@/api/device/typesApi';
import { useDeviceStore } from '@/store/modules/device';
import { Device, DeviceType, GroupInfo } from '@/types/device';
import { ApiStatus } from '@/utils/http/status';
import { FormInstance, FormRules } from 'element-plus';


const emit = defineEmits(['success'])

const deviceStore = useDeviceStore()
const deviceInfo  = computed(() => deviceStore.deviceInfo)

const groupInfoList = ref<GroupInfo[]>([])

const dialogVisible = ref(false)
const formRef = ref<FormInstance>()
const formData = ref({
    groups: [] as string[],
})
const rules = reactive<FormRules>({
    device_type_id: [{ required: true, message: '请选择设备类型', trigger: 'blur' }],
})



const handleClose = () => {
    formRef.value?.resetFields()
    formData.value = {groups: []}
    dialogVisible.value = false
}

const setCurDevInfo = () => {
    let selectGroupInfo = [] as GroupInfo[];
    groupInfoList.value.forEach((item) => {
        if (formData.value.groups.includes(item.group_id)) {
            selectGroupInfo.push(item)
        }
    })
    let tmpDev = deviceStore.getDeviceInfo()
    tmpDev.group_info = selectGroupInfo;
    deviceStore.setDeviceInfo(tmpDev);
}

const handleUpdate = async () => {
    if (!formRef.value) return
    await formRef.value.validate(async (valid) => {
        if (valid) {
            let res = await deviceService.updateDevice(deviceInfo.value)
            if (res.code === ApiStatus.success) {
                ElMessage.success('修改成功!')
                setCurDevInfo()
                emit("success")
                handleClose()
            } else {
                ElMessage.error(res.message)
            }
        }
    })
}


const initDeviceTypeList = async () => {
    let res = await groupService.findGroupList({})
    if (res.code === ApiStatus.success) {
        groupInfoList.value = res.payload.list;
    } else {
        ElMessage.error(res.message)
    }
}

const show = async (item: Device) => {
    await initDeviceTypeList()
    formData.value.groups = item.groups;
    dialogVisible.value = true
}


defineExpose({
    show,
})

</script>

<style scoped>
.tags-container {
    gap: 12px; 
    width: 100vw;
}
</style>