<template>
    <div class="attribute-div-class">
        <div class="title-and-button-container">
            <div class="header-container">
                <!-- <el-button size="default" plain @click="showAttrJson">
                    <i class="iconfont-sys" v-html="'&#xe654;'"></i>
                    查看属性json
                </el-button>
                <el-button @click="showDownInfo()" size="default" plain>
                    <i class="iconfont-sys" v-html="'&#xe79c;'"></i>
                    下发属性
                </el-button> -->
            </div>
            <div class="extra-container">
                <div class="time-range-container">   
                    <el-date-picker
                        v-model="timeRange"
                        type="datetimerange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        value-format="YYYY-MM-DD HH:mm:ss"
                        class="shorter-date-picker"
                        @change="initEventData()"
                    />
                    <el-button class="refresh-btn" @click="initEventData()"  plain>
                        <template #icon><el-icon><Refresh /></el-icon></template>
                        刷新
                    </el-button>      
                </div>
            </div>
        </div>
        <el-table
            :data="eventList"
            style="width: 100%"
            stripe
            v-loading="loading"
        >
            <el-table-column prop="identifier" label="事件标识符"  />
            <el-table-column prop="name" label="事件名称"  />
            <el-table-column prop="ts" label="事件上报时间"  >
                <template #default="scope">
                    {{ moment.unix(scope.row.ts).format('YYYY-MM-DD HH:mm:ss')}}
                </template>
            </el-table-column>
            <el-table-column prop="conn_type" label="通信类型" >
                <template #default="scope"> 
                    {{  ConnTypeOptionsLabel(deviceInfo.device_type_info?.conn_type) }}
                </template>
            </el-table-column>
            <el-table-column prop="action" label="查看事件"  width="150" >
                <template #default="scope">
                    <span title="查看历史数据" class="down-class" @click="viewHistory(scope.row)">
                        <el-icon><View /></el-icon>
                    </span>
                </template>
            </el-table-column>
        </el-table>

        <div class="pagination-container">
            <el-pagination
                v-model:current-page="currentPage"
                v-model:page-size="pageSize"
                :page-sizes="[10, 20, 50, 100]"
                :background="true"
                layout="total, sizes, prev, pager, next"
                :total="total"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                />
        </div>
    </div>
</template>

<script lang="ts" setup>
import { useDeviceStore } from '@/store/modules/device';
import { Event } from '@/types/device';
import { ApiStatus } from '@/utils/http/status';
import moment from 'moment';
import { eventService } from '@/api/device/event';
import { View } from '@element-plus/icons-vue';


import { onMounted } from 'vue';
import { ConnTypeOptionsLabel } from '@/utils/device';

const loading = ref(false)
const eventList = ref<Event[]>([])


const deviceStore = useDeviceStore()
const deviceInfo = computed(() => deviceStore.deviceInfo)




const viewHistory = (item: Event) => {

}


const timeRange = ref([
  moment().startOf('day').format('YYYY-MM-DD HH:mm:ss'), 
  moment().endOf('day').format('YYYY-MM-DD HH:mm:ss')
])

const dayStrToNanoUnix = (dateStr: string) => {
    let curDate = new Date(dateStr)
    return curDate.getTime()
}


// 分页相关变量
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

const handleSizeChange = async (val: number) => {
    pageSize.value = val
    await initEventData()
}

const handleCurrentChange = async (val: number) => {
    currentPage.value = val
    await initEventData()   
}

const queryParams = ref({
    page: currentPage.value,
    page_size: pageSize.value,
    device_id: deviceInfo.value.device_id,
    event_id: "",
    start: dayStrToNanoUnix(timeRange.value[0]),
    end: dayStrToNanoUnix(timeRange.value[1]),
})

const initEventData = async () => {
    queryParams.value.page = currentPage.value
    queryParams.value.page_size = pageSize.value
    queryParams.value.start = dayStrToNanoUnix(timeRange.value[0]);
    queryParams.value.end  = dayStrToNanoUnix(timeRange.value[1]);
    let params = queryParams.value;
    loading.value = true
    let resp = await eventService.history(params)
    if (resp.code === ApiStatus.success) {
        eventList.value = resp.payload.list
        total.value = resp.payload.total
        loading.value = false
    } else {
        loading.value = false
    }
}



onMounted( async () => {
    await initEventData()
})

</script>

<style lang="scss" scoped>
.down-class {
    text-align: center;
    cursor: pointer;
    font-size: 18px;
    &:hover {
        color: #66b1ff;
    }
}

.attribute-div-class {
    width: 100%;
    display: flex;
    flex-direction: column;
}

.title-and-button-container {
    display: flex;
    justify-content: space-between;
    align-items: center; /* 垂直居中 */
    // margin-top: 10px;
}


.header-container {
    display: flex;
    justify-content: space-between;
    align-items: center; /* 垂直居中 */
    margin-top: 10px;
}


.extra-container {
    display: flex;
    justify-content: flex-end;
    width: 100%;
}





.time-range-container {
    display: flex;
    align-items: center;
    gap: 10px; /* 添加间距 */
}



.shorter-date-picker {
    width: 380px;
}

.refresh-btn {
    height: 38px;
    margin-left: 0;
    /* 确保图标和文本对齐 */
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

</style>