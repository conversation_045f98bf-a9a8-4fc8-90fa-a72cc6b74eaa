
<template>
    <div class="summary">
        <div class="title-and-button-container">
            <div class="header-container">
                <div class="text-h4">当前设备属性</div>
            </div>
            <div class="extra-container">
                <el-button @click="refreshDatas()" size="default" plain>
                    <template #icon><el-icon><Refresh /></el-icon></template>
                    刷新
                </el-button>
            </div>
        </div>
        <div  class="properties-div-class">
            <el-card :title="item?.name"  v-for="(item, index)  in properties" :key="index"  class="property-class" >
                <template #header>
                    <div class="header-class">
                        <div class="card-title-class">
                            <div class="text-h5">{{ item?.name }}<span class="text-desc">({{ item?.identifier }})</span></div>
                        </div>
                        <div class="all-extra-class">
                            <span class="extra-class" @click="viewHistory(item)">
                                <i class="iconfont-sys" v-html="'&#xe833;'"></i>
                            </span>
                            <span class="extra-class" @click="viewChart(item)"  v-if="isShowChart(item)">
                                <i class="iconfont-sys" v-html="'&#xe742;'"></i>
                            </span>
                            <span class="extra-class" @click="viewAction(item)">
                                <el-dropdown placement="bottom">
                                    <span class="ant-dropdown-link">
                                        <i class="iconfont-sys" v-html="'&#xe6dd;'"></i>
                                    </span>
                                    <template #dropdown>
                                    <el-dropdown-menu>
                                        <el-dropdown-item >
                                            <i class="iconfont-sys" v-html="'&#xe79c;'"></i>
                                            下发属性
                                        </el-dropdown-item>
                                        <el-dropdown-item>
                                            <i class="iconfont-sys" v-html="'&#xe873;'"></i>
                                            加入看板
                                        </el-dropdown-item>
                                        <el-dropdown-item divided>
                                            <i class="iconfont-sys" v-html="'&#xe7dc;'"></i>
                                            移除该属性
                                        </el-dropdown-item>
                                    </el-dropdown-menu>
                                    </template>
                                </el-dropdown>
                            </span>
                        </div>
                    </div>
                </template>
                <div class="item-content-class">
                    <div class="text-h4 item-value-class">
                        <span v-if="item.data_type === 'object' || item.data_type === 'list'" >
                            <el-link  plain @click="viewHistory(item)">查看详情</el-link>
                        </span>
                        <span v-else>{{ getIdentifyValue(item.identifier) }}</span>
                    </div>
                    <span class="text-desc">{{ getAttrUnit(item) }}</span>
                    <div class="item-update-class">
                        {{ getIdentifyUpdateTime(item.identifier) }}
                    </div>
                </div>
            </el-card>
        </div>
        <AttrHistory ref="historyRef" />
        <PropertyChart ref="propertyChartRef" /> 
    </div>
</template>
  
<script setup lang="ts">
import { Attribute, NumberDataOption } from '@/types/device';
import { ref } from 'vue'
import { Refresh } from "@element-plus/icons-vue";
import { attributeService } from '@/api/device/attribute';
import { ApiStatus } from '@/utils/http/status';
import PropertyChart from './widget/propertyChart.vue';
import { useDeviceStore } from '@/store/modules/device';
import AttrHistory from '@/components/custom/Dialog/AttrHistory.vue';
import moment from 'moment';
  
const deviceStore = useDeviceStore()

const deviceInfo  = computed(() => deviceStore.deviceInfo)

const historyRef = ref<InstanceType<typeof AttrHistory>>();
const propertyChartRef = ref<InstanceType<typeof PropertyChart>>();

const loading = ref(false);
const properties = ref<Attribute[]>([]);
const attrHistoryMap = ref<Record<string, any>>({});

const refreshDatas = async () =>{
    properties.value = [];
    await initAttribute();
    await getOneHistory();
}

const viewHistory = (item: Attribute) => {
    nextTick(() => {
        historyRef.value?.show(item, deviceInfo.value.device_id);
    })
}

const viewChart = (item: Attribute) => {
    nextTick(() => {
        propertyChartRef.value?.show(item);
    })
}
const isShowChart = (item: Attribute) => {
    if (item.data_type === 'number' ) {
        return true;
    }
    return false;
}
const viewAction = (item: Attribute) => {

}

// 在初始化时修改 moment 的相对时间规则
moment.updateLocale('en', {
  relativeTime: {
    future: '%s',
    past: '%s',
    s: '几秒',
    ss: '%d 秒',
    m: '1 分钟',
    mm: '%d 分钟',
    h: '1 小时',
    hh: '%d 小时',
    d: '1 天',
    dd: '%d 天',
    M: '1 月',
    MM: '%d 月',
    y: '1 年',
    yy: '%d 年'
  }
});
const getIdentifyUpdateTime = (identifier: string ) => {
    if (attrHistoryMap.value[identifier] === undefined) {
        return "";
    }
    if (attrHistoryMap.value[identifier]["ts"] === undefined) {
        return "";
    }
    return moment.unix(attrHistoryMap.value[identifier]["ts"]).fromNow() + "前更新";
}

const getIdentifyValue = (identifier: string ) => {
    if (attrHistoryMap.value[identifier] === undefined) {
        return "";
    }
    return attrHistoryMap.value[identifier][identifier]
}

const getAttrUnit = (item: Attribute) => {
    if(item.data_type === 'number' ) {
        const numberOption = item.data_options as NumberDataOption;
        if (numberOption?.unit) {
            return numberOption.unit;
        } else {
            return ""
        }
    } else {
        return ""
    }
}

const selectTime = ref<[Date, Date] | null>(null);
const inittoday = () => {
    const start = new Date();
    start.setHours(0, 0, 0, 0);
    const end = new Date();
    end.setHours(23, 59, 59, 999);
    selectTime.value = [start, end];
}

const getOneHistory = async () => {
    inittoday();
    properties.value?.forEach(async (item: Attribute) => {  
        let params = {
            device_id: deviceInfo.value.device_id,
            attr_id: item.attr_id,
            start:  selectTime.value![0].getTime() , // moment().startOf('day').valueOf()  * 1000000,
            end:   selectTime.value![1].getTime() ,//moment().endOf('day').valueOf() * 1000000,
            page: 1,
            page_size: 1,
        }
        let resp = await attributeService.history(params);
        if (resp.code === ApiStatus.success) {
            if(resp.payload?.list.length > 0) {
                attrHistoryMap.value[item.identifier] = resp.payload.list[0];
            } 
        } else {
            console.log(resp.message)
        }
    })
}




const initAttribute = async () => {
    loading.value = true;
    let params = {
        device_type_id: deviceInfo.value?.device_type_id,
        page: 1,
        page_size: 100,
    }
    let resp = await attributeService.findAttributeList(params);
    if (resp.code === ApiStatus.success) {
        properties.value = resp.payload.list;
    } else {
        ElMessage.error(resp.message);
    }
    loading.value = false;
}



onMounted(async () => {
    await initAttribute();
    await getOneHistory();
})


watch(
  () => deviceInfo.value.device_id,
  async (newVal: string) => {
    if (newVal) {
        await initAttribute();
        await getOneHistory();
    }
  }
)

const refresh = async () => {
    await initAttribute();
    await getOneHistory();
}

defineExpose({
    refresh,
})

</script>
  
<style scoped>
.summary {
    width: 100%;
    display: flex;
    flex-direction: column;
}

.title-and-button-container {
    display: flex;
    justify-content: space-between;
    align-items: center; /* 垂直居中 */
    margin-top: 10px;
}


.header-container {
    display: flex;
    justify-content: space-between;
    align-items: center; /* 垂直居中 */
    margin-top: 10px;
}

/* 可以移除原来的 .header 和 .extra 的 margin-top */
.extra-container {
    display: flex;
    justify-content: flex-end;
}

 .properties-div-class {
    display: flex; /* 启用Flex布局 */
    flex-wrap: wrap; /* 允许项目换行以适应容器 */
    gap: 15px; /* 设置项目间的间距 */
}

.property-class {
    width: 300px;
    margin-top: 10px;
    position: relative;
    height: 180px; /* 设置一个固定高度，确保有足够的空间 */
}
.property-class:hover{  
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: box-shadow 0.3s ease;
    transform: translateY(-0.5px); /* 可选：轻微上移以增强立体感 */
}


.header-class {
    display: flex;
    justify-content: space-between;
    align-items: center; /* 垂直居中 */
}
.card-title-class {
}



.extra-class {
    margin-left: 15px;
}
.extra-class:hover {
    cursor: pointer;
}


.item-content-class {
    display: flex;
    justify-content: center;
    align-items: center; /* 垂直居中 */ 
    position: relative;
    height: 100%;
}
.item-value-class {
    margin-right: 10px;       
}
   
/* 更新时间显示在右下角 */
.item-update-class {
    position: absolute;
    right: 5px; /* 调整右边距 */
    top: 70px; /* 调整底部边距 */
    color: #999; /* 可选：让文字颜色淡一点 */
    font-size: 14px; /* 可选：调整字体大小 */
}

</style>