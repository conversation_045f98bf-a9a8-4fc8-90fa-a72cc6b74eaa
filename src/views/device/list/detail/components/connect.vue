<template>
    <div class="mqtt-access-container">
      <div v-if="mqttForm.haveAndShow">    
        <div class="text-h4">设备端 MQTT 接入点</div>
        <p class="description">
          MQTT 接入方式为设备和云平台提供双向连接，设备既可上报属性数据，也可接收云端的消息下发。
          <el-link type="primary" href="#" :underline="false">了解详情</el-link>
        </p>
        
        <el-input class="url-input" v-model="mqttForm.broker" readonly>
          <template #prepend>mqtt://</template>
        </el-input>
        
        <div class="form-container">
          <el-form label-width="auto">
            <!-- <el-form-item label="MQTT 主机:">
              <el-input v-model="mqttForm.broker" readonly>
                <template #append>
                  <el-button @click="copyToClipboard(mqttForm.broker)">复制</el-button>
                </template>
              </el-input>
            </el-form-item> -->
            
            <!-- <el-form-item label="MQTT 端口:">
              <el-input v-model="mqttPort" readonly />
            </el-form-item> -->
            
            <el-form-item label="ClientID:">
              <el-input v-model="mqttForm.clientID" readonly>
                <template #append>
                  <el-button @click="copyToClipboard(mqttForm.clientID)">复制</el-button>
                </template>
              </el-input>
            </el-form-item>

            <el-form-item label="Username:">
              <el-input v-model="mqttForm.username" readonly>
                <template #append>
                  <el-button @click="copyToClipboard(mqttForm.username)">复制</el-button>
                </template>
              </el-input>
            </el-form-item>
            
            <el-form-item label="Password:">
              <el-input v-model="mqttForm.password" type="password" readonly>
                <template #append>
                  <el-button @click="copyToClipboard(mqttForm.password)">复制</el-button>
                </template>
              </el-input>
            </el-form-item>
          </el-form>
        </div>
      </div>    
      
      <div v-if="tcpForm.haveAndShow">
        <div class="text-h3">设备端 TCP 接入点</div>
        <p class="description">
          TCP 接入方式为设备提供了最原始的 TCP/IP 传输方式，支持 ASCII、HEX、JSON 等有效负载，适合一些自定义协议的通信场景，也适用于 TCP 透传方式
          <el-link type="primary" href="#" :underline="false">了解详情</el-link>
        </p>
        
        <el-input class="url-input" v-model="tcpForm.domain" readonly>
          <template #prepend>tcp://</template>
        </el-input>
        <div class="form-container">
          <el-form label-width="auto">
            <el-form-item label="Host:">
                <el-input v-model="tcpForm.host" readonly>
                  <template #append>
                    <el-button @click="copyToClipboard(tcpForm.host)">复制</el-button>
                  </template>
                </el-input>
            </el-form-item>
            <el-form-item label="端口:">
                <el-input v-model="tcpForm.port" readonly>
                  <template #append>
                    <el-button @click="copyToClipboard(String(tcpForm.port))">复制</el-button>
                  </template>
                </el-input>
            </el-form-item>
          </el-form>
        </div>
      </div>      
    </div>
  </template>
  
  <script lang="ts" setup>
  import { ref } from 'vue'
  import { ElMessage } from 'element-plus'
import { deviceService } from '@/api/device/device'
import { ApiStatus } from '@/utils/http/status'
import { AccessPoint, ConnectInfo, ConnectMqttInfo } from '@/types/device'
import { useDeviceStore } from '@/store/modules/device'
  
const deviceStore = useDeviceStore()

const deviceInfo  = computed(() => deviceStore.deviceInfo)


  // 数据定义
  const mqttForm = reactive({
    haveAndShow: false ,
    broker: '',
    clientID: "",
    username: "",
    password: ""
  })
  const tcpForm = reactive({
    haveAndShow: false ,
    domain: "",
    host: "",
    port: NaN,
  })
 
  // 复制到剪贴板函数
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
      .then(() => {
        ElMessage.success('复制成功')
      })
      .catch(() => {
        ElMessage.error('复制失败')
      })
  }


  const initConnListInfo = async () => {
    if (deviceInfo.value === undefined || deviceInfo.value?.device_id === undefined) return 
    let params = {device_id: deviceInfo.value?.device_id}
    let resp  = await deviceService.connectList(params)
    if (resp.code === ApiStatus.success) {
      resp.payload.list.forEach((item: ConnectInfo) => {
        if(item.conn_protocol === "mqtt" ){
          mqttForm.haveAndShow = true;
          mqttForm.broker = item.host + ":" + item.port;
          let mqttInfo = item.extend_info as ConnectMqttInfo;
          mqttForm.clientID = mqttInfo.client_id;
          mqttForm.username = mqttInfo.username;
          mqttForm.password = mqttInfo.password;
        } else if (item.conn_protocol === "tcp" ){
          tcpForm.haveAndShow = true;
          tcpForm.domain = item.host + ":" + item.port;
          tcpForm.host = item.host;
          tcpForm.port = item.port;
        }
      })
    }
  }

  onMounted(async () => {
    // 获取设备信息
    await initConnListInfo()
  })

  watch(
    () => deviceInfo.value.device_id,
    async (newVal: string) => {
      if (newVal) {
          await initConnListInfo();
      }
    }
  )


  </script>
  
  <style scoped>
  .mqtt-access-container {
    width: 100%;
    margin: 0 auto;
    /* padding: 20px; */
    background-color: var(--art-background-color);
    /* color: #333; */
  }
  
  /* h2 {
    color: #333;
    margin-top: 30px;
    margin-bottom: 15px;
  } */
  
  .description {
    color: #666;
    margin-bottom: 20px;
    line-height: 1.6;
  }
  
  .url-input {
    margin-bottom: 20px;
  }
  
  .form-container {
    margin-bottom: 30px;
  }
  
  .el-form-item {
    margin-bottom: 15px;
  }
  
  .el-input {
    width: 100%;
  }
  
  .el-link {
    margin-left: 5px;
  }
  </style>