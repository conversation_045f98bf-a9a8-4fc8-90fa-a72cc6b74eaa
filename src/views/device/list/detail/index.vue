<template>
    <div >
      <div class="detail-header">
        <Bannel></Bannel>
      </div>
  
      <div class="detail-content">
        <el-tabs v-model="activeName" class="detail-item-tabs" @tab-click="handleClick">
          <el-tab-pane label="概览" name="summary">
            <Summary  ref="summaryRef" />
          </el-tab-pane>
          <el-tab-pane label="连接" name="conn">
            <Connect />
          </el-tab-pane>
          <el-tab-pane label="信息" name="extend_info">
            <ExtendInfo />
          </el-tab-pane>
          <el-tab-pane label="属性" name="attrs">
            <AttributeInfo />
          </el-tab-pane>
          <el-tab-pane label="事件" name="event">
            <EventInfo />
          </el-tab-pane>
          <el-tab-pane label="命令" name="command">
            <CommandInfo />
          </el-tab-pane>
          <!-- <el-tab-pane label="任务" name="tasks">
          </el-tab-pane>
          <el-tab-pane label="告警" name="alarm">
          </el-tab-pane> -->
          <!-- <el-tab-pane label="调试" name="debug">
          </el-tab-pane> -->
          <el-tab-pane label="设置" name="settings">
            <Settings />
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </template>
  
<script setup lang="ts">
import { ref } from 'vue'
import type { TabsPaneContext } from 'element-plus'
import { useDeviceStore } from '@/store/modules/device'
import { useWorktabStore } from '@/store/modules/worktab'
import { router } from '@/router'
import Bannel from './widget/bannel.vue'
import Summary from './components/summary/index.vue'
import Connect from './components/connect.vue'
import ExtendInfo from './components/extendInfo/index.vue'
import AttributeInfo from './components/attributeInfo/index.vue'
import EventInfo from './components/eventInfo/index.vue'
import CommandInfo from './components/commandInfo/index.vue'
import Settings from './components/settings/index.vue'
  

const activeName = ref('summary')
const deviceStore = useDeviceStore()
const deviceInfo = computed(() => deviceStore.deviceInfo)

const summaryRef = ref<InstanceType<typeof Summary>>()


onMounted(() => {
  // 检查是否有设备信息，如果没有则返回列表页
  if (deviceInfo.value.device_id === undefined || deviceInfo.value.device_id === '') {
    useWorktabStore().removeTab('/device/detail')
    router.push({ path: '/device/list' })
  }
})

const handleClick = (tab: TabsPaneContext, event: Event) => {
  console.log(tab, event)
  if(tab.props.name === 'summary') {
    nextTick(() => {
      summaryRef.value?.refresh()
    })
  }

}
</script>

<style lang="scss" scoped>
  .detail-header {
    min-height: 8rem;
  }

  .detail-item-tabs > .el-tabs__content {
    padding: 32px;
    color: #6b778c;
    //   font-size: 50px;
    //   font-weight: 800;
  }
</style>
  