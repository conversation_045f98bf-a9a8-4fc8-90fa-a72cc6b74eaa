<template>
    <div class="device-header">
      <div class="back-section">
        <el-button 
          class="back-btn" 
          type="primary" 
          link 
          @click="goBack"
        >
          <el-icon><ArrowLeft /></el-icon>
          <span>返回设备列表</span>
        </el-button>
      </div>
      <div class="section-line"></div>

      <!-- 主标题区域 -->
      <div class="header-main">
        <div class="title-section">
          <i
            class="iconfont-sys device-icon"
            v-html="deviceInfo?.device_type_info?.icon"
            :style="{ color: 'rgb(var(--art-primary))', fontSize: 60 + 'px'}"
          ></i>
          <span class="text-h3">{{ deviceInfo?.name }}</span>
          <el-tag :type="statusTagType" effect="light" class="status-tag">
            {{ statusText }}
          </el-tag>
          <el-button class="edit-btn" size="small"  plain @click="handleEdit">
            <el-icon><Edit /></el-icon>
            <span>编辑</span>
          </el-button>
        </div>
    
        <!-- 元信息区域 -->
        <div class="meta-section">
          <div class="meta-grid">
            <!-- 第一列 -->
            <div class="meta-column">
              <div class="meta-item">
                <span class="meta-label">设备ID:</span>
                <span class="meta-value code">{{ deviceInfo?.device_id }}</span>
              </div>
              
              <div class="meta-item">
                <span class="meta-label">设备Code:</span>
                <span class="meta-value code">{{ deviceInfo?.device_code || 'N/A' }}</span>
              </div>
              
              
            </div>
            
            <!-- 第二列 -->
            <div class="meta-column">
              <div class="meta-item">
                <span class="meta-label">设备类型:</span>
                <el-link type="primary" :underline="false" class="meta-link" @click="clickToDeviceType">
                  {{ deviceInfo?.device_type_info?.name }}
                  <el-icon><ArrowRight /></el-icon>
                </el-link>
              </div>
              
              <div class="meta-item">
                <span class="meta-label">设备类型ID:</span>
                <span class="meta-value code">{{ deviceInfo?.device_type_info?.device_type_id }}</span>
              </div>
              <!-- <div class="meta-item">
                <span class="meta-label">激活状态:</span>
                <el-tag :type="deviceInfo?.active_status === 1   ? 'success' : 'info'" size="small">
                  {{ deviceInfo?.active_status === 1 ? '已激活' : '未激活' }}
                </el-tag>
              </div> -->
              
             
            </div>
            
            <!-- 第三列 -->
            <div class="meta-column">
              <div class="meta-item">
                <span class="meta-label">在线状态:</span>
                <el-tag :type="deviceInfo?.active_online ===1 ? 'success' : 'danger'" size="small">
                  {{ deviceInfo?.active_online === 1 ? '在线' : '离线' }}
                </el-tag>
              </div>
              
              <div class="meta-item">
                <span class="meta-label">调试模式:</span>
                <el-tag :type="deviceInfo?.debug ? 'warning' : 'info'" size="small">
                  {{ deviceInfo?.debug ? '开启' : '关闭' }}
                </el-tag>
              </div>
            </div>

            <!-- 第四列 -->
            <div class="meta-column">
              <div class="meta-item">
                <span class="meta-label">创建时间:</span>
                <span class="meta-value">{{ formatDate(deviceInfo?.created_at) }}</span>
              </div>
              
              <div class="meta-item">
                <span class="meta-label">更新时间:</span>
                <span class="meta-value">{{ formatDate(deviceInfo?.updated_at) }}</span>
              </div>
              
            </div>
          </div>
          
          <!-- 描述信息 -->
          <div class="description" v-if="deviceInfo?.desc">
            <el-text class="desc-label">设备描述:</el-text>
            <el-text class="desc-content">{{ deviceInfo.desc }}</el-text>
          </div>
          
          <!-- 标签信息 -->
          <div class="tags-section" v-if="deviceInfo?.tags?.length">
            <el-text class="tags-label">设备标签:</el-text>
            <div class="tags-container">
              <el-tag 
                v-for="tag in deviceInfo.tags" 
                :key="tag.key" 
                type="info" 
                size="small"
                class="custom-tag"
              >
                {{ tag.key }}: {{ tag.value }}
              </el-tag>
            </div>
          </div>
        </div>
      </div>
      
      <UpdateDeviceInfo ref="updateDeviceInfoRef" />
    </div>
  </template>
  
  <script lang="ts" setup>
  import { computed, ref, nextTick } from 'vue'
  import { useRouter } from 'vue-router'
  import { Edit, ArrowRight } from '@element-plus/icons-vue'
  import { useDeviceStore } from '@/store/modules/device'
  import UpdateDeviceInfo from './updateDeviceInfo.vue'
import { Device } from '@/types/device'
import { getDefaultDevice } from '@/utils/device'
  

  // 组件逻辑
  const deviceStore = useDeviceStore()
  const router = useRouter()
  const updateDeviceInfoRef = ref<InstanceType<typeof UpdateDeviceInfo>>()
  
  const deviceInfo = computed(() => deviceStore.deviceInfo)
  
  // 状态显示转换
  const statusText = computed(() => {
    const statusMap: Record<number, string> = {
      "-1": '禁用',
      1: '启用',
      2: '维护中'
    }
    return deviceInfo.value?.status !== undefined 
      ? statusMap[deviceInfo.value.status] || '未知'
      : '未知'
  })
  
  const statusTagType = computed(() => {
    const typeMap: Record<number, 'success' | 'danger' | 'warning' | 'info'> = {
      "-1": 'danger',
      1: 'success',
      2: 'warning'
    }
    return deviceInfo.value?.status !== undefined 
      ? typeMap[deviceInfo.value.status] || 'info'
      : 'info'
  })
  
  const handleEdit = (): void => {
    nextTick(() => {
      if (deviceInfo.value) {
        updateDeviceInfoRef.value?.show(deviceInfo.value)
      }
    })
  }
  
  const clickToDeviceType = (): void => {
    if (deviceInfo.value?.device_type_info) {
      deviceStore.setDeviceTypeInfo(deviceInfo.value.device_type_info)
      router.push({ path: '/device/types/detail' })
    }
  }
  
  const formatDate = (dateString: string | undefined): string => {
    if (!dateString) return 'N/A'
    try {
      return new Date(dateString).toLocaleString()
    } catch {
      return 'N/A'
    }
  }

  const goBack = () => {
    const defaultDev = getDefaultDevice()
    deviceStore.setDeviceInfo(defaultDev)
  }

</script>
  
<style lang="scss" scoped>
.device-header {
    // padding: 16px 0;
    padding-bottom: 16px;
    margin-bottom: 24px;
    border-bottom: 1px solid var(--el-border-color-light);
}

.section-line {
  margin-bottom: 5px;
  border-bottom: 1px solid var(--el-border-color-light);
}

/* 新增的返回按钮样式 */
.back-section {
  margin-bottom: 5px;
  margin-top: -10px;
  
  .back-btn {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 8px 0;
    font-size: 14px;
    color: var(--el-color-primary);
    transition: color 0.2s;
    
    &:hover {
      color: var(--el-color-primary-light-3);
    }
    
    .el-icon {
      font-size: 16px;
    }
  }
}

  .header-main {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }
  
  .title-section {
    display: flex;
    align-items: center;
    gap: 12px;
    flex-wrap: wrap;
  }
  
  .device-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 60px;
  }
  
  .title-section .text-h3 {
    display: flex;
    align-items: center;
    margin: 0;
    line-height: 1;
  }
  
  .device-title {
    margin: 0;
    font-size: 24px;
    font-weight: 600;
    color: var(--el-text-color-primary);
    line-height: 1.3;
  }
  
  .status-tag {
    font-weight: 500;
    margin-left: 8px;
  }
  
  .edit-btn {
    padding: 5px 11px;
    
    span {
      margin-left: 4px;
    }
  }
  
  .meta-section {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }
  
  .meta-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 16px 24px;
  }
  
  .meta-column {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }
  
  .meta-item {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    color: var(--el-text-color-secondary);
    font-size: 14px;
    line-height: 1.5;
  }
  
  .meta-label {
    font-weight: 500;
    flex-shrink: 0;
    width: 80px;
    text-align: right;
  }
  
  .meta-value {
    color: var(--el-text-color-regular);
    
    &.code {
      font-family: monospace;
      word-break: break-all;
    }
  }
  
  .meta-link {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    font-weight: 500;
    transition: color 0.2s;
    
    &:hover {
      color: var(--el-color-primary-light-3);
    }
    
    .el-icon {
      font-size: 12px;
      margin-left: 2px;
    }
  }
  
  .description {
    display: flex;
    gap: 8px;
    margin-top: 8px;
    
    .desc-label {
      font-weight: 500;
      color: var(--el-text-color-secondary);
      flex-shrink: 0;
    }
    
    .desc-content {
      color: var(--el-text-color-regular);
      line-height: 1.5;
    }
  }
  
  .tags-section {
    display: flex;
    gap: 8px;
    margin-top: 8px;
    
    .tags-label {
      font-weight: 500;
      color: var(--el-text-color-secondary);
      flex-shrink: 0;
    }
    
    .tags-container {
      display: flex;
      flex-wrap: wrap;
      gap: 6px;
    }
    
    .custom-tag {
      :deep(.el-tag__content) {
        display: flex;
        align-items: center;
        gap: 4px;
      }
    }
  }
  
  .group-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
  }
  
  @media (max-width: 768px) {
    .meta-grid {
      grid-template-columns: 1fr;
    }
    
    .meta-item {
      align-items: center;
    }
    
    .device-title {
      font-size: 20px;
    }
    
    .description, .tags-section {
      flex-direction: column;
      align-items: flex-start;
    }
  }
  </style>