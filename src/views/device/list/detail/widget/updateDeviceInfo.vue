<template>
    <el-dialog
        v-model="dialogVisible"
        title="编辑设备信息"
        width="750px"
        top="5vh" 
        @close="handleClose()"
    >
        <el-form ref="formRef" :model="formData" :rules="rules" label-width="auto">
            <el-form-item label="设备id" prop="device_id">
                <el-input v-model="formData.device_id" :disabled="true"></el-input>
            </el-form-item>
            <el-form-item label="设备名称" prop="name">
              <el-input v-model="formData.name">
                <template #prepend>
                  <i class="iconfont-sys prepend-icon" v-html="formData.icon" @click="changeIcon"></i>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item label="调试模式" prop="debug">
                <el-switch v-model="formData.debug"></el-switch>
            </el-form-item>
            <el-form-item label="是否启用" prop="status">
                <el-switch v-model="formData.status" :active-value="1" :inactive-value="-1" ></el-switch>
                <!-- <el-select v-model="formData.status" placeholder="请选择"> -->
                  <!-- <el-option label="启用" :value="1" /> -->
                  <!-- <el-option label="禁用" :value="-1" /> -->
                <!-- </el-select> -->
            </el-form-item>
            <el-form-item label="设备描述" prop="desc">
                <el-input
                :autosize="{ minRows: 4, maxRows: 4 }"
                type="textarea"
                maxlength="1000"
                show-word-limit
                v-model="formData.desc"
              />
            </el-form-item>
            <el-form-item label="设备标签" prop="tags">
              <div class="tags-container">
                <el-row :gutter="20" v-for="(tag, index) in formData.tags" :key="index">
                  <el-col :span="11">
                    <el-input v-model="tag.key" placeholder="标签key" />
                  </el-col>
                  <el-col :span="11">
                    <el-input v-model="tag.value" placeholder="标签值" />
                  </el-col>
                  <el-col :span="2">
                    <el-button
                      size="default"
                      :icon="Delete"
                      circle
                      @click="deleteTag(index)"
                    ></el-button>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="24">
                        <el-link type="primary" @click="addTag" :underline="false">+ 添加标签</el-link>
                    </el-col>
                </el-row>
              </div>
            </el-form-item>
        </el-form>
        <template #footer>
            <span class="dialog-footer">
            <el-button @click="handleClose">取消</el-button>
            <el-button type="primary" @click="handleUpdate">确定</el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
import { deviceService } from '@/api/device/device';
import { Device, KeyValue } from '@/types/device';
import { ApiStatus } from '@/utils/http/status';
import { FormInstance, FormRules } from 'element-plus';
import { Delete } from '@element-plus/icons-vue'

const emit = defineEmits(['success'])

const curDevInfo = ref<Device>()

const dialogVisible = ref(false)
const formRef = ref<FormInstance>()
const formData = ref({
    name: '',
    device_id: "",
    icon: '&#xe784;',
    desc: "",
    debug: false,
    status: 1,
    tags: [] as KeyValue[],
})
const rules = reactive<FormRules>({
    name: [
        { required: true, message: '请输入类型名称', trigger: 'blur' },
        { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
    ],
    device_id: [{ required: true, message: '请输入设备id', trigger: 'blur' }],
})

const changeIcon = () => {
    console.log("change icon")
}

const deleteTag = (index: number) => {
    formData.value.tags.splice(index, 1)
}
  
const addTag = () => {
    formData.value.tags.push({ key: '', value: '' })
}
  

const handleClose = () => {
    formRef.value?.resetFields()
    formData.value = {
        name: '',
        icon: '&#xe784;',
        device_id: "",
        desc: "",
        status: 1,
        debug: false,
        tags: [] as KeyValue[],
    }
    dialogVisible.value = false
}

const setCurDevInfo = () => {
    curDevInfo.value!.name = formData.value.name;
    curDevInfo.value!.debug = formData.value.debug;
    curDevInfo.value!.status = formData.value.status;
    curDevInfo.value!.tags = formData.value.tags;
    curDevInfo.value!.desc = formData.value.desc;
}

const handleUpdate = async () => {
    if (!formRef.value) return
    await formRef.value.validate(async (valid) => {
        if (valid) {
            setCurDevInfo()
            let res = await deviceService.updateDevice(curDevInfo.value)
            if (res.code === ApiStatus.success) {
                ElMessage.success('修改成功!')
                emit("success")
                handleClose()
            } else {
                ElMessage.error(res.message)
            }
        }
    })
}

const show = (item: Device) => {
    formData.value = {
        name: item.name,
        icon: "&#xe784;",
        device_id: item.device_id,
        desc: item.desc,
        status: item.status,
        debug: item.debug,
        tags: item.tags,
    }
    curDevInfo.value = item
    dialogVisible.value = true
}


defineExpose({
    show,
})

</script>

<style scoped>
.tags-container {
    gap: 12px; 
    width: 100vw;
}
</style>