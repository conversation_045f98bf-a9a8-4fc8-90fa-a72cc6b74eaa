<template>
    <div class="page-content">
      <el-row :gutter="20">
        <el-col :sm="24" :md="24" :lg="24">
          <my-table-bar
            :showTop="true"
            @search="search"
            @reset="resetForm(searchFormRef)"
            :layout="'search, refresh'"
          >
            <template #top>
              <el-form :model="searchForm" ref="searchFormRef" label-width="auto">
                <el-row :gutter="20">
                  <art-form-input label="接入点名称" prop="name" v-model="searchForm.name" />
                </el-row>
              </el-form>
            </template>
            <template #bottom>
              <el-button @click="newAccessPoint" size="default" v-ripple>添加接入点</el-button>
            </template>
          </my-table-bar>
        </el-col>
      </el-row>
      <el-divider />
        <div class="card-container">
          <el-space wrap>
          <!-- <div class="card-grid"> -->
            <el-card v-for="(item, index) in accessPointList" :key="index" class="box-card">
                  <template #header>
                    <div class="card-header">
                        <!-- <span>{{ getTypeName(item.type) }}</span> -->
                        {{ item.name }}
                        <div  class="enable-class">
                            <div  v-if="item.status === 1">
                                <div class="online-class"></div>
                                <el-link type="primary" :underline="false" class="link-btn"  @click="handleDisadabled(item)">启用</el-link>
                            </div>
                            <div v-else>
                                <div class="offline-class"></div>
                                <el-link type="primary" :underline="false" class="link-btn"  @click="handleEndabled(item)">禁用</el-link>
                            </div>
                            
                        </div>
                    </div>
                </template>
                <!-- 卡片内容 -->
                <div class="card-content">
                    <el-row>
                      <div>
                        <p style="margin-bottom: 10px;">接入点ID: {{ item?.access_point_id }}</p>
                        <p style="margin-bottom: 10px;">接入点Code: {{ item?.access_point_code }}</p>
                        <p style="margin-bottom: 10px;">主机地址: {{ item?.host }}</p>
                        <p style="margin-bottom: 10px;">主机端口: {{ item?.port }}</p>
                        <p class="card-tag-class"><span><el-tag type="info">无标签</el-tag></span></p>
                      </div>
                    </el-row>
                </div>
  
                <template #footer>
                    <div class="footer-div">
                        <el-link type="primary" size="small" class="footer-btn" :underline="false" @click="handleEdit(item)">编辑</el-link>
                        <el-divider direction="vertical" />
                        <el-link type="danger" size="small" class="footer-btn" :underline="false" @click="handleDelete(item)">删除</el-link>
                        <el-divider direction="vertical" />
                        <el-link type="success" size="small" class="footer-btn" :underline="false" @click="handleDetail(item)">详情</el-link>
                    </div>
              </template>
            </el-card>
          </el-space>
          <!-- </div> -->
        </div>
      <AddUpdate ref="addUpdateRef" @success="initData" />
    </div>
  </template>
  
<script lang="ts" setup>
import { AccessPoint } from '@/types/device'
import { ApiStatus } from '@/utils/http/status';
import AddUpdate from './widget/addUpdate.vue';
import { FormInstance } from 'element-plus'
import { router } from '@/router'
import { AccessPointService } from '@/api/device/accessPoint';
import { Status } from '@/utils/device';
  
  

const addUpdateRef = ref<InstanceType<typeof AddUpdate>>()
const searchForm = ref({
      name: ''
})
const search = async () => {
    let params = { ...searchForm.value }
    let resp = await AccessPointService.findAccessPointList(params)
    if (resp.code === ApiStatus.success) {
    accessPointList.value = resp.payload.list
    total.value = resp.payload.total
    }
}


const newAccessPoint = () => {
    nextTick(() => {
        addUpdateRef.value?.add()
    })
}


const searchFormRef = ref<FormInstance>()
const resetForm = (formEl: FormInstance | undefined) => {
    if (!formEl) return
    formEl.resetFields()
}

const handleDisadabled = async (card: AccessPoint) => {
    card.status = Status.Disabled;
    let resp = await AccessPointService.updateAccessPoint(card)
    if (resp.code === ApiStatus.success) {
    ElMessage.success('禁用成功')
    initData()
    } else {  
    ElMessage.error("禁用失败:" + resp.message)
    }
}

const handleEndabled =async (card: AccessPoint) => {
    card.status = Status.Enabled;
    let resp = await AccessPointService.updateAccessPoint(card)
    if (resp.code === ApiStatus.success) {
    ElMessage.success('启用成功 ')
    initData()
    } else {  
    ElMessage.error("启用失败:" + resp.message)
    }
}

const handleDetail = (card: AccessPoint) => {
}

const handleEdit = (card: AccessPoint) => {
    nextTick(() => {
    addUpdateRef.value?.showDialog(card)
    })
}

const handleDelete = async (card: AccessPoint) => {
    let resp   = await  AccessPointService.deleteAccessPoint({ access_point_id: card.access_point_id})
    if (resp.code === ApiStatus.success) {
    ElMessage.success('删除成功')
    initData()
    } else {
    ElMessage.error("删除失败:" + resp.message)
    }
} 

onMounted(async () => {
    await initData()
})

const total = ref(0)
const accessPointList = ref<AccessPoint[]>([])

const pageParams = ref({ page_size: 20, page: 1 })
const initData = async () => {
    let params = { ...pageParams.value }
    let resp = await AccessPointService.findAccessPointList(params)
    if (resp.code === ApiStatus.success) {
    accessPointList.value = resp.payload.list
    total.value = resp.payload.total
    }
}


</script>


<style scoped>


.card-container {
/* display: flex; */
/* justify-content: center; */
width: 100%;
}

.card-grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 20px;
  max-width: 100vw; /* 根据你的需求调整 */
  margin: 0 auto;
}

.box-card {
  width: 500px;
  height:  300px;
}

.box-card:hover {
  cursor: pointer;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
  transform: translateY(-1px);
}


.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}


.footer-div {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.footer-btn {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  width: 100px;   /*设置一个固定宽度*/
  text-align: center;
}


.enable-class {
    float: right;
}



.online-class {
  background: #67C23A; 
  border-radius: 50%;
  width: 10px;
  height: 10px;
  float:left;
  margin-top: 3px;
}

.offline-class {
  background: #F56C6C;
  border-radius: 50%;
  width: 10px;
  height: 10px;
  float: left;
  margin-top: 3px;
}

.link-btn {
  float: right;
  width: 30px;
  height: 10px;
  margin-top: 1px;
  margin-left: 10px;
}

.no-border-card {
  border: none !important;
  box-shadow: none !important; /* 如果有阴影的话也一并去除 */
}


.card-tag-class {
    margin-top: 10px;
}
</style>

<style lang="scss" scoped>
.page-title {
    font-size: 30px;
}
</style>
  