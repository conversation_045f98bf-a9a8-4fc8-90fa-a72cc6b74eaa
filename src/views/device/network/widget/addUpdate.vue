<template>
    <el-dialog
        v-model="dialogVisible"
        :title="title"
        :width="dialogWidth"
        @close="closeDialog"
    >
        <el-form ref="formRef" :model="formData" :rules="rules" label-width="auto">
            <!-- <el-form-item label="logo" prop="icon"> -->
                <!-- <UserAvatar   @update="update" :avatar="formData.icon" /> -->
            <!-- </el-form-item> -->
            <el-form-item label="协议名称" prop="name">
                <el-input v-model="formData.name" />
            </el-form-item>
            <el-form-item label="状态" prop="status">
                <el-switch v-model="formData.status"  :active-value="1" :inactive-value="-1"> </el-switch>
            </el-form-item>
            <el-form-item label="接入协议" prop="conn_protocol">
                <el-input v-model="formData.conn_protocol" />
            </el-form-item>
            <el-form-item label="消息编码" prop="msg_protocol">
                <el-input v-model="formData.msg_protocol" />
            </el-form-item>
            <el-form-item label="主机地址" prop="host">
                <el-input v-model="formData.host" />
            </el-form-item>
            <el-form-item label="端口" prop="port">
                <el-input-number v-model="formData.port"  />
            </el-form-item>
            <el-form-item label="扩展信息" prop="tags">
              <div class="tags-container">
                <el-row :gutter="20" v-for="(tag, index) in extendInfoList" :key="index">
                  <el-col :span="11">
                    <el-input v-model="tag.key" placeholder="例如: username/password" />
                  </el-col>
                  <el-col :span="11">
                    <el-input v-model="tag.value" placeholder="值" />
                  </el-col>
                  <el-col :span="2">
                    <el-button
                      size="default"
                      :icon="Delete"
                      circle
                      @click="deleteTag(index)"
                    ></el-button>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="24">
                        <el-link type="primary" @click="addTag" :underline="false">+ 添加扩展信息</el-link>
                    </el-col>
                </el-row>
              </div>
            </el-form-item>
            <!-- <el-form-item label="描述" prop="desc">
                <el-input
                    :autosize="{ minRows: 4, maxRows: 4 }"
                    type="textarea"
                    maxlength="200"
                    show-word-limit
                    v-model="formData.desc"
                />
            </el-form-item> -->
        </el-form>
        <template #footer>
        <div class="dialog-footer">
            <el-button @click="closeDialog">取消</el-button>
            <el-button type="primary" @click="handleSubmit">提交</el-button>
        </div>
        </template>
    </el-dialog>
</template>


<script lang="ts" setup>
import { typesService } from '@/api/device/typesApi';
import { AccessPoint , KeyValue } from '@/types/device';
import { ApiStatus } from '@/utils/http/status';
import { FormInstance, FormRules } from 'element-plus';
import { Delete } from '@element-plus/icons-vue'
import { objectEntries } from '@vueuse/core';
import { AccessPointService } from '@/api/device/accessPoint';

const emit = defineEmits(['success'])
const formRef = ref<FormInstance>();

const dialogVisible  = ref(false);
const title = ref('编辑/添加接入点');
const isAdd   = ref(false);

const formData = ref({
    name: '',
    conn_protocol: "",
    msg_protocol: "",
    host: "",
    port: NaN,
    status: 1,
    // extend_info: { username: "", password: ""} as object,
    // icon: "",
    // tags: [] as KeyValue[],
})

const rules = reactive<FormRules>({
    name: [
        { required: true, message: '请输入类型名称', trigger: 'blur' },
        { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
    ],
    conn_protocol: [
        { required: true, message: '请输入接入协议', trigger: 'blur' },
    ],
    msg_protocol: [
        { required: true, message: '请输入协议编码', trigger: 'blur' },
    ],
    host: [
        { required: true, message: '请输入主机地址', trigger: 'blur' },
    ],
    port: [
        { required: true, message: '请输入端口', trigger: 'blur' },
    ],
})


const extendInfoList = ref<KeyValue[]>([]);
const deleteTag = (index: number) => {
    extendInfoList.value.splice(index, 1)
}
const addTag = () => {
    extendInfoList.value.push({ key:"", value: ""})
}
    

const showDialog = (item: AccessPoint) => {
    formData.value.name = item.name;
    formData.value.conn_protocol = item.conn_protocol;
    formData.value.msg_protocol = item.msg_protocol;
    formData.value.host = item.host;
    formData.value.port = item.port;
    formData.value.status = item.status;
    extendInfoList.value = objectEntries(item.extend_info).map(([key, value]) => ({key, value}));
    curItem.value = item;
    isAdd.value = false;
    dialogVisible.value = true;
}


const closeDialog = () => {
    resetForm()
    dialogVisible.value = false;
}

const setCurInfo = () => {
    curItem.value!.name = formData.value.name;
    curItem.value!.conn_protocol = formData.value.conn_protocol;
    curItem.value!.msg_protocol = formData.value.msg_protocol;
    curItem.value!.host = formData.value.host;
    curItem.value!.port = formData.value.port;
    curItem.value!.status = formData.value.status;
    curItem.value!.extend_info = extendInfoList.value.reduce((acc, {key, value}) => ({...acc, [key]: value}), {});
}

const curItem = ref<AccessPoint>();
const handleSubmit = async () => {
    if (!formRef.value) return
    await formRef.value.validate(async (valid) => {
        if (valid) {
            setCurInfo()
            let apiCall;
            if(isAdd.value){
                apiCall = AccessPointService.addAccessPoint;
            } else {
                apiCall  =  AccessPointService.updateAccessPoint
            }
            let res = await apiCall(curItem.value)
            if (res.code === ApiStatus.success) {
                ElMessage.success('修改成功!')
                emit("success")
                closeDialog()
            } else {
                ElMessage.error(res.message)
            }
        }
    })
}


const resetForm = () => {
    formData.value.name = '';
    formData.value.conn_protocol = "";
    formData.value.msg_protocol = "";
    formData.value.host = "";
    formData.value.port = NaN;
    formData.value.status = 1;
    // formData.value.extend_info = { username: "", password: ""} as object;
}


const dialogWidth = ref("50%")
    const handleResize = () => {
        // 根据屏幕宽度调整对话框宽度
    const windowWidth = window.innerWidth;
    if (windowWidth < 768) {
        dialogWidth.value = "90%"
    } else if(windowWidth > 768 && windowWidth < 1000) {
        dialogWidth.value = "70%"
    } else if(windowWidth > 1000 && windowWidth < 1440) {
        dialogWidth.value = "50%"
    } else {
        dialogWidth.value = "50%"
    }
}

onMounted(() => {
    window.addEventListener('resize', handleResize)
    handleResize() // 初始化响应式布局
})



const add = () => {
    isAdd.value  = true;
    dialogVisible.value = true;
    title.value = '添加接入点';
    resetForm();
    curItem.value = {} as AccessPoint;
}

defineExpose({
    showDialog,
    add, 
})
</script>