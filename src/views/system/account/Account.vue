<template>
  <div class="page-content">
    <art-table-bar
      :showTop="false"
      @search="search"
      @reset="resetForm(searchFormRef)"
      @changeColumn="changeColumn"
      :columns="columns"
    >
      <template #top>
        <el-form :model="searchForm" ref="searchFormRef" label-width="82px">
          <el-row :gutter="20">
            <art-form-input label="用户ID" prop="id" v-model="searchForm.id" />
            <art-form-input label="用户名" prop="username" v-model="searchForm.username" />
            <art-form-input label="手机号" prop="phone" v-model="searchForm.phone" />
            <art-form-select label="状态" prop="status" v-model="searchForm.status" :options="statusOptions" />
            <!-- <form-input label="账号" prop="account" v-model="searchForm.account" /> -->
          </el-row>
          <el-row :gutter="20">
            <art-form-select label="性别" prop="sex" v-model="searchForm.sex" :options="sexOptions" />
          </el-row>
        </el-form>
      </template>
      <template #bottom>
        <el-button @click="showDialog('add')" v-ripple>添加用户</el-button>
      </template>
    </art-table-bar>

    <art-table 
        :data="tableData" 
        :currentPage="pageParams.page" 
        :pageSize="pageParams.page_size" 
        :total="total"
        @current-change="currentPageChange"
        @size-change="pageSizeChange"
        >
      <template #default>
        <el-table-column
          label="用户名"
          prop="avatar"
          #default="scope"
          width="300px"
          v-if="columns[0].show"
        >
          <div class="user" style="display: flex; align-items: center">
            <img class="avatar" :src="scope.row.avatar" />
            <div>
              <p class="user-name">{{ scope.row.username }}</p>
              <p class="email">{{ scope.row.email }}</p>
            </div>
          </div>
        </el-table-column>
        <el-table-column label="手机号" prop="phone" v-if="columns[1].show" />
        <el-table-column label="性别" prop="sex" #default="scope" sortable v-if="columns[2].show">
          {{ scope.row.sex === 1 ? '男' : '女' }}
        </el-table-column>
        <el-table-column label="所属角色" prop="roles" v-if="columns[3].show">
          <template #default="scope">
            <div class="flex">
              <el-tag type="primary" 
                :key="item.id" 
                v-for="item in scope.row.roles"  class="mr-2 mb-2">
                  {{  item?.title }}
              </el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="状态"
          prop="status"
          :filters="[
            { text: '在线', value: '1' },
            { text: '离线', value: '2' },
            { text: '异常', value: '3' },
            { text: '注销', value: '4' }
          ]"
          :filter-method="filterTag"
          filter-placement="bottom-end"
          v-if="columns[4].show"
        >
          <template #default="scope">
            <el-tag :type="getTagType(scope.row.status)">
              {{ buildTagText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="创建日期" prop="created_at" sortable v-if="columns[5].show" >
          <template #default="scope">
            {{ moment(scope.row.created_at).format('YYYY-MM-DD HH:mm:ss')  }}
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="180px">
          <template #default="scope">
            <ArtButtonTable type="edit"  v-auth="'edit'"     @click="showDialog('edit', scope.row)" />
            <ArtButtonTable type="delete" v-auth="'delete'"  @click="deleteUser(scope.row)" />
            <ArtButtonTable type="passwd"  v-auth="'change_passwd'" @click="changeUserPasswd(scope.row)" />
          </template>
        </el-table-column>
      </template>
    </art-table>

    <el-dialog
      v-model="dialogVisible"
      :title="dialogType === 'add' ? '添加用户' : '编辑用户'"
      :width="dialogWidth"
      @close="closeDialog"
    >
      <el-form ref="formRef" :model="formData" :rules="rules" label-width="80px">
        <el-form-item label="logo" prop="logo">
          <UserAvatar   @update="update" :avatar="formData.avatar" />
        </el-form-item>
        <el-form-item label="用户名" prop="username">
          <el-input v-model="formData.username" />
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="formData.phone" />
        </el-form-item>
        <el-form-item label="性别" prop="sex">
          <el-select v-model="formData.sex">
            <el-option label="男" :value="1" />
            <el-option label="女" :value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="角色" prop="roles">
          <el-select v-model="formData.roles"  multiple  >
            <el-option :label="item.title" v-for="item in roleList" :key="item.id" :value="item.id"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="区域" prop="address">
            <el-cascader
              :props="addressProps"
              clearable
              v-model="formData.address"
              style="width: 100%"
            />
        </el-form-item>
        <el-form-item label="描述" prop="desc">
          <el-input
              :autosize="{ minRows: 4, maxRows: 4 }"
              type="textarea"
              maxlength="200"
              show-word-limit
              v-model="formData.desc"
            />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeDialog">取消</el-button>
          <el-button type="primary" @click="handleSubmit">提交</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 更新密码 -->
     <UpdatePasswd ref="cuPasswdVisibleRef"></UpdatePasswd>
  </div>
</template>

<script setup lang="ts">
  import { RoleService } from '@/api/roleApi'
  import { SystemService } from "@/api/systemApi"
  import { useUserStore } from '@/store/modules/user'

  import { PaginationResult } from '@/types/axios'
  import { RoleType } from '@/types/role'
  import { ApiStatus } from '@/utils/http/status'


  import { FormInstance } from 'element-plus'
  import { ElMessageBox, ElMessage } from 'element-plus'
  import type { FormRules } from 'element-plus'
  import { UserService } from '@/api/usersApi'
  import { UserInfo } from '@/types/store'
  import moment from 'moment'
  import UserAvatar from './components/userAvatar.vue'
  import UpdatePasswd from './components/updatePasswd.vue'

  const userStore = useUserStore()
  const userInfo = computed(() => userStore.getUserInfo)

  const dialogType = ref('add')
  const dialogVisible = ref(false)


  const formData = reactive({
    id: 0,
    username: '',
    avatar: '',
    phone: '',
    sex: 1,
    dep: '',
    roles: [] as number[],
    address: [] as string[],
    desc: '',
  })

  const sexOptions = [
    {
      value: "1",
      label: '男'
    },
    {
      value: "2",
      label: '女'
    }
  ]
  const statusOptions =  [
    { value: "1", label: '在线'  },
    { value: "2", label: '离线'  },
    { value: "3", label: '异常'  },
    { value: "4", label: '注销'  }
  ]

  const columns = reactive([
    { name: '用户名', show: true },
    { name: '手机号', show: true },
    { name: '性别', show: true },
    { name: '所属角色', show: true },
    { name: '状态', show: true },
    { name: '创建日期', show: true }
  ])



  const searchFormRef = ref<FormInstance>()
  const searchForm = reactive({
    username: '',
    phone: '',
    id: '',
    sex: '',
    status: '',
    roles: [] as number[],
    address: [] as string [],
  })

  const resetForm = (formEl: FormInstance | undefined) => {
    if (!formEl) return
    formEl.resetFields()
  }

  const cuPasswdVisibleRef = ref();
  const changeUserPasswd = (row?: any) => {
    nextTick(() => {
      cuPasswdVisibleRef.value.show(row)
    })
  }

  const tableData = ref<UserInfo[]>([]); // ACCOUNT_TABLE_DATA
  const pageParams = ref({page_size: 20, page: 1})
  const total = ref(0);
  const showDialog = async (type: string, row?: any) => {
    await getRoleList();
    dialogVisible.value = true
    dialogType.value = type
    if (type === 'edit' && row) {
      formData.id = row.id
      formData.avatar = row.avatar 
      formData.username = row.username
      formData.phone = row.phone
      formData.sex = row.sex 
      formData.dep = row.dep
      formData.roles = row.roles?.map((item: RoleType) => item.id)
      formData.desc = row.desc
      let address = [];
      if(row.province_id !== 0 || row.city !== 0 || row.district_id !== 0) {
        if (row.province_id) {
          address.push(`${row.province_id}-province`);
        }
        if (row.city_id) {
          address.push(`${row.city_id}-city`);
        }
        if (row.district_id) {
          address.push(`${row.district_id}-district`);
        }
      } else {
        if (userInfo.value.company?.district_id) {
          address.unshift(`${userInfo.value.company?.district_id}-district`);
        } else if (userInfo.value.company?.city_id) {
          address.unshift(`${userInfo.value.company?.city_id}-city`);
        } else if (userInfo.value.company?.province_id) {
          address.unshift(`${userInfo.value.company?.province_id}-province`);
        }
      }
      formData.address = address;
      // console.log("formData:", formData)
    } else {
      formData.username = ''
      formData.phone = ''
      formData.avatar = ""
      formData.sex = 1
      formData.dep = ''
      formData.desc = ''
      formData.roles = [] as number[]
      formData.address = [] as string[]
    }
  }

  const deleteUser = (row: any) => {
    ElMessageBox.confirm('确定要注销该用户吗？', '注销用户', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'error'
    }).then(async () => {
      if(row.id === userInfo.value.id) {
        ElMessage.error('不能注销自己')
        return 
      }
      const resp =  await UserService.deleteSubUser({uid: row.id})
      if (resp.code === ApiStatus.success) {
        await getUserList()
        ElMessage.success('删除成功')
      } else {
        ElMessage.error(resp.message);
      }
    })
  }

  const search =async () => {
    const resp = await UserService.listSubUser(searchForm)
    if (resp.code === ApiStatus.success) {
      tableData.value = resp.payload.list
      pageParams.value.page = resp.payload.page
      pageParams.value.page_size = resp.payload.page_size
      total.value = resp.payload.total
    } else {
      ElMessage.error(resp.message)
    }
  }

  const changeColumn = (list: any) => {
    columns.values = list
  }

  const filterTag = (value: string, row: any) => {
    // console.log("status:", row.status, ", value:", value)
    return row.status === value
  }

  const getTagType = (status: number) => {
    switch (status) {
      case 1:
        return 'success'
      case 2:
        return 'info'
      case 3:
        return 'warning'
      case 4:
        return 'danger'
      default:
        return 'info'
    }
  }

  const buildTagText = (status: number) => {
    let text = ''
    if (status === 1) {
      text = '在线'
    } else if (status === 2) {
      text = '离线'
    } else if (status === 3) {
      text = '异常'
    } else if (status === 4) {
      text = '注销'
    }
    return text
  }

  const rules = reactive<FormRules>({
    username: [
      { required: true, message: '请输入用户名', trigger: 'blur' },
      { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
    ],
    // phone: [
    //   { required: true, message: '请输入手机号', trigger: 'blur' },
    //   { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号格式', trigger: 'blur' }
    // ],
    sex: [{ required: true, message: '请选择性别', trigger: 'change' }],
    roles: [{ required: true, message: '请选择角色', trigger: 'change' }],
    address: [{ required: true, message: '请选择区域', trigger: 'change' }]
  })

  const formRef = ref<FormInstance>()
  const closeDialog = () => {
    // console.log("close dialog")
    dialogVisible.value = false ;
    resetForm(formRef.value)
    formData.avatar = "";
  }
  const handleSubmit = async () => {
    if (!formRef.value) return
    await formRef.value.validate(async (valid) => {
      if (valid) {
        const { address, ...withoutAddr } = formData;
        let params = {...withoutAddr, province_id: 0, city_id: 0, district_id: 0 };
        formData.address.forEach((cur) => {
          if (cur.split("-").pop() === "province") {
            params.province_id = Number(cur.split("-")[0]);
          }
          if (cur.split("-").pop() === "city") {
            params.city_id = Number(cur.split("-")[0]);
          }
          if (cur.split("-").pop() === "district") {
            params.district_id = Number(cur.split("-")[0]);
          }
        });
        let resp ;
        if ( dialogType.value === 'add') {
          resp = await UserService.addSubUser(params)
        } else {
          resp = await UserService.updateSubUser(params)
        } 
        if(resp?.code === ApiStatus.success) {
          ElMessage.success(dialogType.value === 'add' ? '添加成功' : '更新成功')
          await getUserList();
          dialogVisible.value = false
        } else {
          ElMessage.error(resp?.message)
        } 
      }
    })
  }


  const roleList = ref<RoleType[]>([]);
  // 从后端获取角色列表
  const getRoleList =async () => {
    const params = {page: 1, page_size: 100}
    const resp : PaginationResult<RoleType[]> = await RoleService.getRoleList(params)
    if(resp.code === ApiStatus.success) {
      roleList.value = resp.payload.list;
    } else{
      ElMessage.error(resp.message)
    }
  }

  const update = (avatar: string) => {
    formData.avatar = avatar;
  };
  const getUserList = async () => {
    const resp = await UserService.listSubUser({page: pageParams.value.page, page_size: pageParams.value.page_size})
    if (resp.code === ApiStatus.success) {
      tableData.value = resp.payload.list
      total.value = resp.payload.total
    } else{
      ElMessage.error(resp.message)
    }
  }
const  currentPageChange = async(page: number) => {
  pageParams.value.page = page 
  await getUserList()
}
const  pageSizeChange = async(size: number) => {
  pageParams.value.page_size = size 
  await getUserList()
}
  
const addressProps = {
    lazy: true,
    checkStrictly: true,
    lazyLoad(node:any, resolve:any) {
      if (!node.value && !userInfo.value.is_super) {
        // console.log("userInfo:", userInfo.value)
        SystemService.areaInfo({
          id:
            userInfo.value?.district_id ||
            userInfo.value?.city_id ||
            userInfo.value?.province_id,
        }).then((res) => {
          let nodes = [
            {
              value: `${res.payload.id}-${res.payload.area_level}`,
              label: res.payload.name,
              leaf: ["province", "city"].includes(res.payload.area_level)
                ? false
                : true,
            },
          ];
          resolve(nodes);
        });
      } else {
        SystemService.areaChildrenList({ parent_id: node.value?.split("-")[0] }).then(
          (res) => {
            const nodes = res.payload.list.map((cur:any) => ({
              value: `${cur.id}-${cur.area_level}`,
              label: cur.name,
              leaf: ["province", "city"].includes(cur.area_level) ? false : true,
            }));
            resolve(nodes);
          }
        );
      }
    },
  };

  onMounted(()=>{
    window.addEventListener('resize', handleResize)
    handleResize() // 初始化响应式布局
  })
  onBeforeMount(() => {
    window.removeEventListener('resize', handleResize)
  })
  const dialogWidth = ref("30%")
  const handleResize = () => {
      // 根据屏幕宽度调整对话框宽度
    const windowWidth = window.innerWidth;
    if (windowWidth < 768) {
      dialogWidth.value = "90%"
    } else if(windowWidth > 768 && windowWidth < 1000) {
      dialogWidth.value = "70%"
    } else if(windowWidth > 1000 && windowWidth < 1440) {
      dialogWidth.value = "50%"
    } else {
      dialogWidth.value = "30%"
    }
  }

  onMounted(async() => {
    await getUserList()
    await getRoleList()
  })


</script>

<style lang="scss" scoped>
  .page-content {
    width: 100%;
    height: 100%;

    .user {
      .avatar {
        width: 40px;
        height: 40px;
        border-radius: 6px;
      }

      > div {
        margin-left: 10px;

        .user-name {
          font-weight: 500;
          color: var(--art-text-gray-800);
        }
      }
    }
  }
</style>
