<template>
    <el-dialog
      v-model="dialogVisible"
      title="重置用户密码"
      :width="dialogWidth"
      @close="closeDialog"
    >   
        <el-form ref="formRef" :model="formData" :rules="rules" label-width="auto">
            <el-form-item label="用户名" prop="username">
                <el-input v-model="formData.username" disabled />
            </el-form-item>
            <el-form-item label="密码" prop="password">
                <el-input v-model="formData.password" />
            </el-form-item>
            <el-form-item label="确认密码" prop="password1">
                <el-input v-model="formData.password1" />
            </el-form-item>
        </el-form>
        <template #footer>
            <div class="dialog-footer">
            <el-button @click="closeDialog">取消</el-button>
            <el-button type="primary" @click="handleSubmit">提交</el-button>
            </div>
      </template>
    </el-dialog>

</template>


<script setup lang="ts">
import { UserService } from '@/api/usersApi';
import { ApiStatus } from '@/utils/http/status';
import { FormRules } from 'element-plus';



const formData = ref({
    uid: 0,
    username: "",
    password: "",
    password1: "",
})
const dialogVisible = ref(false);
const rules = reactive<FormRules>({
    password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
    password1: [{ required: true, message: '请再次输入密码', trigger: 'blur' }],
})

const closeDialog = () => {
    formData.value.uid = 0
    formData.value.username = ""
    formData.value.password = ""
    formData.value.password1 = ""
    dialogVisible.value = false;
}

const handleSubmit = async () => {
    if(formData.value.password=== "") {
        ElMessage.error("密码不能为空")
        return 
    }
    if(formData.value.password !== formData.value.password1) {
        ElMessage.error("两次输入密码不一致")
        return 
    }
    const resp = await UserService.updateUserPasswd(formData.value)
    if (resp.code === ApiStatus.success) {
        ElMessage.success("更新成功")
        closeDialog()
    } else {
        ElMessage.error(resp.message)
    }
}

onMounted(()=>{
    window.addEventListener('resize', handleResize)
    handleResize() // 初始化响应式布局
})
onBeforeMount(() => {
    window.removeEventListener('resize', handleResize)
})
const dialogWidth = ref("30%")
const handleResize = () => {
        // 根据屏幕宽度调整对话框宽度
    const windowWidth = window.innerWidth;
    if (windowWidth < 768) {
        dialogWidth.value = "90%"
    } else if(windowWidth > 768 && windowWidth < 1000) {
        dialogWidth.value = "70%"
    } else if(windowWidth > 1000 && windowWidth < 1440) {
        dialogWidth.value = "50%"
    } else {
        dialogWidth.value = "30%"
    }
}

const show = (data: any) => {
    formData.value.uid = data.id 
    formData.value.username = data.username
    dialogVisible.value = true 
}

defineExpose({
    show,
})

</script>