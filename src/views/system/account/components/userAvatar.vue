<template>
  <div class="userAvatar-page">
    <div class="user-info-head" @click="visible = true">
      <img :src="userAvatarImg"  title=""  v-if="userAvatarImg" />
      <span v-else>点击上传头像</span>
    </div>
    <el-dialog
      title="修改头像"
      :model-value="visible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :width="dialogWidth"
      @close="visible = false"
    >
      <el-row>
        <el-col  :xs="24" :sm="24" :md="12"  :style="{ height: '350px' }">
          <VueCropper
            ref="cropperRef"
            :img="options.img"
            :info="true"
            :autoCrop="options.autoCrop"
            :autoCropWidth="options.autoCropWidth"
            :autoCropHeight="options.autoCropHeight"
            :fixedBox="options.fixedBox"
            :outputType="options.outputType"
            @realTime="realTime"
            v-if="visible"
          />
        </el-col>
        <el-col :xs="24" :sm="24" :md="12"  :style="{ height: '350px' }">
          <div class="previews-box">
            <img :src="previews.url" :style="previews.img" />
          </div>
        </el-col>
      </el-row>
      <br />
      <el-row :gutter="10">
        <el-col :xs="8" :sm="4">
          <el-upload
            action="#"
            :http-request="() => {}"
            :show-file-list="false"
            :before-upload="beforeUpload"
          >
            <el-button :icon="UploadFilled">选择</el-button>
          </el-upload>
        </el-col>
        <el-col  :xs="8" :sm="2" >
          <el-button :icon="Plus" @click="changeScale(1)"></el-button>
        </el-col>
        <el-col  :xs="8" :sm="2" >
          <el-button :icon="Minus" @click="changeScale(-1)"></el-button>
        </el-col>
        <el-col  :xs="8" :sm="2" >
          <el-button :icon="RefreshLeft" @click="rotateLeft()"></el-button>
        </el-col>
        <el-col  :xs="8" :sm="2" >
          <el-button :icon="RefreshRight" @click="rotateRight()"></el-button>
        </el-col>
      </el-row>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="close()">关 闭</el-button>
          <el-button type="primary" @click="ok()">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script setup>
import {  ref } from "vue";
import {
  Plus,
  Minus,
  RefreshLeft,
  RefreshRight,
  UploadFilled,
} from "@element-plus/icons-vue";
import VueCropper from "vue-cropper/src/vue-cropper.vue";
import { ApiStatus } from '@/utils/http/status';
import { SystemService } from '@/api/systemApi';
import { useSettingStore } from "@/store/modules/setting";
const props = defineProps({
  avatar: {
    type: String,
    default: "",
  },
});

const minioApi = configStore.getMinioApi() 
const defaultUrl = props.avatar.startsWith("http")?  props.avatar: (minioApi + props.avatar).replace(/([^:]\/)\/+/g, "$1");
const userAvatarImg = ref(defaultUrl);



const emit = defineEmits(["update"]);
const options = ref({
  img: userAvatarImg.value, // img: props.user.avatar, //裁剪图片的地址
  autoCrop: true, // 是否默认生成截图框
  autoCropWidth: 200, // 默认生成截图框宽度
  autoCropHeight: 200, // 默认生成截图框高度
  fixedBox: true, // 固定截图框大小 不允许改变
  outputType: "png", // 默认生成截图为PNG格式
});
const visible = ref(false);
// 上传预处理
const beforeUpload = (file) => {
  if (file.type.indexOf("image/") == -1) {
    this.$modal.msgError(
      "文件格式错误，请上传图片类型,如：JPG，PNG后缀的文件。"
    );
  } else {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => {
      options.value.img = reader.result;
    };
  }
};

// 实时预览
const previews = ref({});
const realTime = (data) => {
  previews.value = data;
};
// 向左旋转
const cropperRef = ref(null);
const rotateLeft = () => {
  cropperRef.value.rotateLeft();
};
// 向右旋转
const rotateRight = () => {
  cropperRef.value.rotateRight();
};
// 图片缩放
const changeScale = (num) => {
  num = num || 1;
  cropperRef.value.changeScale(num);
};

const close = () => {
  userAvatarImg.value =  "";
  options.value.img = "";
  visible.value = false
}
const ok = () => {
  cropperRef.value.getCropBlob(async (data) => {
    const timestampMs = Date.now();
    let imgFileData = new FormData();
    imgFileData.append("file", data);
    imgFileData.append("filename", timestampMs + ".png");
    const imgResp =  await SystemService.uploadFile(imgFileData)
    if (imgResp.code !== ApiStatus.success) {
      ElMessage.error("上传图片错误:" + imgResp.message)
      return 
    }
    userAvatarImg.value =  imgResp.payload.url;
    options.value.img = imgResp.payload.url;
    emit("update", imgResp.payload.url);
    close()
    // ElMessage.success("修改成功");
  });
};


onMounted(()=>{
  window.addEventListener('resize', handleResize)
  handleResize() // 初始化响应式布局
})
onBeforeMount(() => {
  window.removeEventListener('resize', handleResize)
})
const dialogWidth = ref("30%")
const handleResize = () => {
    // 根据屏幕宽度调整对话框宽度
  const windowWidth = window.innerWidth;
  if (windowWidth < 768) {
    dialogWidth.value = "90%"
  } else if(windowWidth > 768 && windowWidth < 1000) {
    dialogWidth.value = "75%"
  } else if(windowWidth > 1000 && windowWidth < 1440) {
    dialogWidth.value = "55%"
  } else if(windowWidth > 1440 && windowWidth < 1920){
    dialogWidth.value = "45%"
  } else {
    dialogWidth.value = "30%"
  }
}


// 监听图片URL变化
watch(
  () => props.avatar,
  (newVal) => {
    userAvatarImg.value = newVal;
    options.value.img = newVal;
  }
)

</script>
<style lang="scss" scoped>
.userAvatar-page {
  // text-align: center;
  .previews-box {
    position: relative;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    width: 200px;
    height: 200px;
    border-radius: 50%;
    -webkit-box-shadow: 0 0 4px #ccc;
    box-shadow: 0 0 4px #ccc;
    overflow: hidden;
  }
}
.user-info-head {
  position: relative;
  display: inline-block;
  width: 120px;
  height: 120px;
  text-align: center;
  line-height: 120px;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.5);
  color: #fff;
  font-size: 12px;
  overflow: hidden;
  img {
    width: 100%;
  }
}

.user-info-head:hover:after {
  content: "+";
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  color: #eee;
  background: rgba(0, 0, 0, 0.5);
  font-size: 24px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  cursor: pointer;
  line-height: 120px;
  text-align: center;
  border-radius: 50%;
}
</style>
