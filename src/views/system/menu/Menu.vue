<template>
  <div class="page-content">
    <el-row :gutter="20" style="margin-left: 15px">
      <el-button v-auth="'add'" @click="showModel('menu', null, true)" v-ripple>添加菜单</el-button>
      <el-button v-auth="'export'" @click="handleExport()" v-ripple>菜单导出</el-button>
      <el-upload
        :auto-upload="false"
        accept=".json"
        :show-file-list="false"
        @change="handleImport"
        style="margin-left: 15px"
        v-auth="'import'"
      >
        <el-button v-ripple>菜单导入</el-button>
      </el-upload>
    </el-row>

    <art-table :data="tableData" rowKey="menu_id">
      <template #default>
        <el-table-column label="菜单名称">
          <template #default="scope">
            {{ formatMenuTitle(scope.row.meta?.lang_tag, scope.row.meta?.title) }}
          </template>
        </el-table-column>
        <el-table-column prop="path" label="路由" />
        <el-table-column prop="sort" label="排序">
          <template #default="scope">
            {{ scope.row.meta?.sort }}
          </template>
        </el-table-column>
        <el-table-column prop="meta.auth_list" label="可操作权限">
          <template #default="scope">
            <el-popover
              placement="top-start"
              title="操作"
              :width="200"
              trigger="click"
              v-for="(item, index) in scope.row.meta.auth_list"
              :key="index"
            >
              <div style="margin: 0; text-align: right">
                <el-button size="small" type="primary" @click="editButton('button', item)"
                  >编辑</el-button
                >
                <el-button size="small" type="danger" @click="deleteAuth(item)">删除</el-button>
              </div>
              <template #reference>
                <el-button class="small-btn">{{ item.title }}</el-button>
              </template>
            </el-popover>
          </template>
        </el-table-column>

        <el-table-column label="编辑时间" prop="updated_at">
          <template #default="scope">
             {{ moment(scope.row.updated_at).format('YYYY-MM-DD HH:mm:ss') }}
          </template>
        </el-table-column>

        <el-table-column fixed="right" label="操作" width="180">
          <template #default="scope">
            <ArtButtonTable type="add" v-auth="'add'" @click="showModel('menu', scope.row)" />
            <ArtButtonTable type="edit" v-auth="'edit'" @click="showDialog('edit', scope.row)" />
            <ArtButtonTable type="delete" v-auth="'delete'" @click="deleteMenu(scope.row)" />
          </template>
        </el-table-column>
      </template>
    </art-table>

    <el-dialog :title="dialogTitle" v-model="dialogVisible" width="700px" align-center>
      <el-form ref="formRef" :model="form" :rules="rules" label-width="85px">
        <el-form-item label="菜单类型">
          <el-radio-group v-model="labelPosition" :disabled="disableMenuType">
            <el-radio-button value="menu" label="menu">菜单</el-radio-button>
            <el-radio-button value="button" label="button">权限</el-radio-button>
          </el-radio-group>
        </el-form-item>

        <template v-if="labelPosition === 'menu'">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="菜单标识" prop="lang_tag">
                <el-input v-model="form.lang_tag" placeholder="中英文json的key,常以menus.开头"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="菜单名称" prop="title">
                <el-input v-model="form.title" placeholder="中文名" ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="英文名" prop="name">
                <el-input v-model="form.name" placeholder="英文名"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="路由地址" prop="path">
                <el-input v-model="form.path" placeholder="路由地址"  ></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="菜单排序" prop="sort" style="width: 100%">
                <el-input-number
                  v-model="form.sort"
                  style="width: 100%"
                  @change="handleChange"
                  :min="1"
                  controls-position="right"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="图标" prop="icon">
                <ArtIconSelector :iconType="iconType" :defaultIcon="form.icon" width="229px"  @getIcon="getIcon" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="组件路径" prop="component">
                <el-input
                  v-model="form.component"
                  placeholder="请输入组件路径"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="外部链接" prop="link">
                <el-input
                  v-model="form.link"
                  placeholder="外部链接/内嵌地址(https://www.baidu.com)"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="5">
              <el-form-item label="是否启用" prop="is_enable">
                <el-switch v-model="form.is_enable"></el-switch>
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="页面缓存" prop="keep_alive">
                <el-switch v-model="form.keep_alive"></el-switch>
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="是否隐藏" prop="is_hide">
                <el-switch v-model="form.is_hide"></el-switch>
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="是否内嵌" prop="is_iframe">
                <el-switch v-model="form.is_iframe"></el-switch>
              </el-form-item>
            </el-col>
          </el-row>
        </template>

        <template v-if="labelPosition === 'button'">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="权限名称" prop="authName">
                <el-input v-model="form.authName" placeholder="权限名称"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="权限标识" prop="authLabel">
                <el-input v-model="form.authLabel" placeholder="权限标识"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="权限排序" prop="authSort" style="width: 100%">
                <el-input-number
                  v-model="form.authSort"
                  style="width: 100%"
                  @change="handleChange"
                  :min="1"
                  controls-position="right"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </template>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancel()">取 消</el-button>
          <el-button type="primary" @click="submitForm()"> 确 定 </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { useMenuStore } from '@/store/modules/menu'
  import type { FormInstance, FormRules } from 'element-plus'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { IconTypeEnum } from '@/enums/appEnum'
  import { formatMenuTitle } from '@/utils/menu'
  import moment from "moment";
  import { ApiStatus } from '@/utils/http/status';
  import { menuService } from '@/api/menuApi'
  import type { UploadFile } from 'element-plus'
import { useSettingStore } from '@/store/modules/setting'
import { MenuListType, MenuListResp } from '@/types/menu'
  // const menuList = computed(() => useMenuStore().getMenuList)

  const dialogVisible = ref(false)
  const form = reactive({
    menu_id: 0,
    // 菜单
    parentID: 0,
    name: '',
    path: '',
    title: '',
    lang_tag: "",
    icon: '',
    is_enable: true,
    sort: 1,
    is_menu: true,
    keep_alive: true,
    is_hide: false,
    link: '',
    component: '',
    is_iframe: false,
    // 权限 (修改这部分)
    authName: '',
    authLabel: '',
    authIcon: '',
    authSort: 1
  })
  const iconType = ref(IconTypeEnum.UNICODE)

  const labelPosition = ref('menu')
  const rules = reactive<FormRules>({
    name: [
      { required: true, message: '请输入菜单名称', trigger: 'blur' },
      { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
    ],
    component: [{ required: true, message: '请输入组件地址', trigger: 'blur' }],
    path: [{ required: true, message: '请输入路由地址', trigger: 'blur' }],
    title: [{ required: true, message: '输入标题', trigger: 'blur' }],
    lang_tag: [{ required: true, message: '输入权限标识', trigger: 'blur' }],
    // 修改这部分
    authName: [{ required: true, message: '请输入权限名称', trigger: 'blur' }],
    authLabel: [{ required: true, message: '请输入权限权限标识', trigger: 'blur' }]
  })

  const tableData = ref<MenuListType[]>([]);
  // console.log("tableData:", tableData.value)
  const menuStore = useMenuStore()
  const isEdit = ref(false)
  const formRef = ref<FormInstance>()
  const dialogTitle = computed(() => {
    const type = labelPosition.value === 'menu' ? '菜单' : '权限'
    return isEdit.value ? `编辑${type}` : `新建${type}`
  })




  const showDialog = (type: string, row: any) => {
    isEdit.value = true 
    showModel('menu', row, true)
  }

  const editButton = (type: string , row: any) => {
    isEdit.value = true
    showModel("button", row, true)
  }

  const cancel = () => {
    resetForm();
    isEdit.value = false ;
    dialogVisible.value = false;
  }

  const handleChange = () => {}



  const getIcon = (iconValue: string) => {
    form.icon = iconValue;
  }

  const submitForm = async () => {
    if (!formRef.value) return

    await formRef.value.validate(async (valid) => {
      if (valid) {
        try {
          // console.log("form icon:", form);
          let params =
            labelPosition.value === 'menu'
              ? {
                  menu_id: form.menu_id,
                  title: form.title,
                  path: form.path,
                  lang_tag: form.lang_tag,
                  name: form.name,
                  icon: form.icon,
                  sort: form.sort,
                  is_enable: form.is_enable,
                  is_menu: form.is_menu,
                  keep_alive: form.keep_alive,
                  is_hide: form.is_hide,
                  component: form.component,
                  link: form.link
                }
              : {
                  is_menu: false,
                  path: "",
                  component: "",
                  is_enable: form.is_enable,
                  menu_id: form.menu_id,
                  title: form.authName,
                  name: form.authLabel,
                  icon: form.authIcon,
                  sort: form.authSort
                }
            // console.log("params:", params)
          let resp ;
          if (isEdit.value) {
            resp = await menuStore.updateMenu(params)
          } else {
            resp = await menuStore.addMenu(params)
          }
          if(resp.code === ApiStatus.success) {
            ElMessage.success(`${isEdit.value ? '编辑' : '新增'}成功`)
            // 刷新列表
            await getCurrentMenuList()
            resetForm()
            dialogVisible.value = false
          } else {
            ElMessage.error(`${isEdit.value ? '编辑' : '新增'}失败`)
          }
        } catch {
          ElMessage.error(`${isEdit.value ? '编辑' : '新增'}失败`)
        }
      }
    })
  }

  const showModel = (type: string, row?: any, lock: boolean = false) => {
    // console.log("rolw:", row , ", type:", type, " isEdit:", isEdit.value)
    dialogVisible.value = true
    labelPosition.value = type
    lockMenuType.value = lock
    form.menu_id = row?.menu_id
    if (isEdit.value) {
      nextTick(() => {
        // 回显数据
        if (type === 'menu') {
          // 菜单数据回显
          form.menu_id = row.menu_id
          form.name = row.name
          form.path = row.path  // 这里，name是一个 英文名字，对应一个当前路由
          form.lang_tag = row.meta.lang_tag 
          form.title = row.meta.title 
          form.icon = row.meta.icon
          form.sort = row.meta.sort || 1
          form.is_menu = row.meta.is_menu
          form.keep_alive = row.meta.keep_alive
          form.is_hide = row.meta.is_hide || false
          form.is_enable = row.meta.is_enable || true
          form.link = row.meta.link
          form.component = row.component
          form.is_iframe = row.meta.is_iframe || false
        } else {
          // 权限按钮数据回显
          form.menu_id = row.menu_id
          form.authName = row.title
          form.authLabel = row.auth_mark
          form.authIcon = row.icon || ''
          form.authSort = row.sort || 1
        }
      })
    }
  }

  const resetForm = () => {
    isEdit.value = false 
    formRef.value?.resetFields()
    Object.assign(form, {
      // 菜单
      menu_id: 0,
      parentID: 0,
      name: '',
      path: '',
      lang_tag: "",
      label: '',
      icon: '',
      sort: 1,
      is_menu: true,
      keep_alive: true,
      is_hide: undefined,
      link: '',
      is_iframe: false,
      // 权限
      authName: '',
      authLabel: '',
      authIcon: '',
      authSort: 1
    })
  }
  const settingStore = useSettingStore()
  const handleExport = async () => {
    const resp = await menuService.exportMenu();
    if(resp.code === ApiStatus.success) {
      const api = configStore.getApiUrl() 
      console.log("base url:", api)
      const fileurl = api + "/" + resp.payload.export;
      let link = document.createElement("a");
      fetch(fileurl)
          .then((res) => res.blob())
          .then((blob) => {
          link.href = URL.createObjectURL(blob);
          link.download = resp.payload.filename;
          document.body.appendChild(link);
          link.click();
      });
    } else {
      ElMessage.error(resp.message)
    }
  }

  const handleImport = async (uploadFile: UploadFile) => {
    try {
      if (!uploadFile.raw) return
      let fdata = new FormData();
      fdata.append("filename", uploadFile.name)
      fdata.append("file", uploadFile.raw)
      const resp = await menuService.importMenu(fdata)
      if(resp.code === ApiStatus.success) {
        ElMessage.success("导入成功")
        await menuStore.refreshMenuList()
      } else {
        ElMessage.error("导入失败:" + resp.message)
      }
    } catch (error) {
      console.log('import-error', error as Error)
    }
  }


  const deleteMenu = async (row:any) => {
    try {
      await ElMessageBox.confirm('确定要删除该菜单吗？删除后无法恢复', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      const result =  await menuStore.deleteMenu(row.menu_id)
      if (result.code === ApiStatus.success) {
        await getCurrentMenuList()
        ElMessage.success('删除成功')
      } else {
        ElMessage.error(result.message)
      }
    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error('删除失败')
      }
    }
  }

  const deleteAuth = async (row:any) => {
    try {
      await ElMessageBox.confirm('确定要删除该权限吗？删除后无法恢复', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      const result =  await menuStore.deleteMenu(row.menu_id)
      if (result.code === ApiStatus.success) {
        await getCurrentMenuList()
        ElMessage.success('删除成功')
      } else {
        ElMessage.error(result.message)
      }
    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error('删除失败')
      }
    }
  }

  // 修改计算属性，增加锁定控制参数
  const disableMenuType = computed(() => {
    // 编辑权限时锁定为权限类型
    if (isEdit.value && labelPosition.value === 'button') return true
    // 编辑菜单时锁定为菜单类型
    if (isEdit.value && labelPosition.value === 'menu') return true
    // 顶部添加菜单按钮时锁定为菜单类型
    if (!isEdit.value && labelPosition.value === 'menu' && lockMenuType.value) return true
    return false
  })

  // 添加一个控制变量
  const lockMenuType = ref(false)


  // 

// 监听 lang_tag 的变化，自动更新 title
watch(
  () => form.lang_tag, // 监听 form.lang_tag
  (newlang_tag) => {
    form.title = formatMenuTitle(newlang_tag, form.title);
  }
);



const getCurrentMenuList = async () => {
    const { menuList, closeLoading } = await menuService.getMenuList()
    closeLoading()
    tableData.value = menuList;
}
onMounted(async() => {
  await getCurrentMenuList()
})

</script>

<style lang="scss" scoped>
  .page-content {
    .svg-icon {
      width: 1.8em;
      height: 1.8em;
      overflow: hidden;
      vertical-align: -8px;
      fill: currentcolor;
    }

    :deep(.small-btn) {
      height: 30px !important;
      padding: 0 10px !important;
      font-size: 12px !important;
    }
  }
</style>
