<template>
    <div class="page-content">
      <el-row>
        <el-col :xs="24" :sm="12" :lg="6">
          <el-input placeholder="公司名称"></el-input>
        </el-col>
        <div style="width: 12px"></div>
        <el-col :xs="24" :sm="12" :lg="6" class="el-col2">
          <el-button v-ripple>搜索</el-button>
          <el-button @click="showDialog('add')" v-ripple>新增公司</el-button>
        </el-col>
      </el-row>
  
      <art-table :data="tableData">
        <template #default>
          <el-table-column label="id" prop="id" />
          <el-table-column label="PID" prop="parent_id" />
          <el-table-column label="公司名称" prop="name" />
          <el-table-column label="公司全称" prop="fullname" />
          <el-table-column label="uuid" prop="uuid" />
          <el-table-column label="描述" prop="desc" />
          <el-table-column label="创建时间" prop="created_at">
            <template #default="scope">
              {{ formatDate(scope.row.created_at) }}
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" width="150px">
          <template #default="scope">
            <ArtButtonTable type="edit" @click="showDialog('edit', scope.row)" />
            <ArtButtonTable type="delete" @click="deleteComapny(scope.row)" />
          </template>
        </el-table-column>
        </template>
      </art-table>
  
      <el-dialog
        v-model="dialogVisible"
        :title="dialogType === 'add' ? '新增公司' : '编辑公司'"
        width="30%"
      >
        <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
          <el-form-item label="公司名称" prop="name">
            <el-input v-model.trim="form.name" />
          </el-form-item>
          <el-form-item label="公司logo" prop="logo">
            <Upload
              upload-type="image"
              data-format="file"
              :limit="1"
              v-model:modelValue="form.logo"
              ref="uploadRef"
            />
          </el-form-item>
          <el-form-item label="公司全称" prop="fullname">
            <el-input v-model.trim="form.fullname" />
          </el-form-item>
          <el-form-item label="角色" prop="roles">
            <el-tree-select
              v-model="form.roles"
              :props="{
                label: 'title',
              }"
              :data="roleList"
              multiple
              :render-after-expand="false"
              show-checkbox
              check-on-click-node
              node-key="id"
              style="width: 100%"
            />
          </el-form-item>
          <el-form-item label="区域" prop="address">
            <el-cascader
              :props="addressProps"
              clearable
              v-model="form.address"
              style="width: 100%"
            />
          </el-form-item>
          <el-form-item label="描述" prop="desc">
            <el-input v-model="form.desc" type="textarea" :rows="3" />
          </el-form-item>
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="dialogVisible = false">取消</el-button>
            <el-button type="primary" @click="handleSubmit(formRef)">提交</el-button>
          </div>
        </template>
      </el-dialog>
  
      
    </div>
  </template>
  
  <script setup lang="ts">

  import { ElMessage, ElMessageBox } from 'element-plus'

  import type { FormInstance, FormRules } from 'element-plus'

  import { ApiStatus } from '@/utils/http/status';
  import { RoleService } from '@/api/roleApi';

  import { BaseResult,  PaginationResult } from '@/types/axios'
  import { UserService } from '@/api/usersApi';
  import { CompanyType } from '@/types/company';
  import { SystemService } from '@/api/systemApi';
  import { useUserStore } from '@/store/modules/user';
  import { RoleType } from '@/types/role';
  import Upload from "@/components/custom/Upload/index.vue";
import { useSettingStore } from '@/store/modules/setting';


    const dialogVisible = ref(false)

    const userStore = useUserStore()
    const userInfo = computed(() => userStore.getUserInfo)
  
    const formRef = ref<FormInstance>()
    const uploadRef = ref()
  
    const rules = reactive<FormRules>({
      name: [
        { required: true, message: '请输入公司名称', trigger: 'blur' },
        { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
      ],
      fullname: [{ required: true, message: "必填", trigger: "blur" }],
      roles: [{ required: true, message: "必选", trigger: "change" }],
      address: [{ required: true, message: "必选", trigger: "change" }],
    })
  
    interface LogoFile extends File {
      url: string;  // 新增字段：上传后的图片 URL
    }
    const form = ref({
      id: 0,
      name: '',
      fullname: "",
      roles: [] as number[], // 多个以,分割
      desc: '',
      address: [] as string[],
      logo: [] as LogoFile[] ,
      logo_filename: "", // 上传文件
    })
  

    const roleList = ref<RoleType[]>([]);
    // 从后端获取公司列表
    const getRoleList =async () => {
      const params = {page: 1, page_size: 100};
      const resp : PaginationResult<RoleType[]> = await RoleService.getRoleList(params)
      if(resp.code === ApiStatus.success) {
        roleList.value = resp.payload.list
      } else{
        ElMessage.error(resp.message)
      }
    }
    const tableData  = ref<CompanyType[]>([]);
    const params = ref({page: 1, page_size: 10, parent_id: userInfo.value.company_id})
    // 从后端获取公司列表
    const getCompanyList = async () => {
      const resp : PaginationResult<CompanyType[]> = await UserService.listCompany(params.value)
      if(resp.code === ApiStatus.success) {
        tableData.value = resp.payload.list
      } else{
        ElMessage.error(resp.message)
      }
    }

    onMounted(async () => {
      await getCompanyList()
      await getRoleList()
    })
  
  
  const addressProps = {
      lazy: true,
      checkStrictly: true,
      lazyLoad(node:any, resolve:any) {
        if (!node.value && !userInfo.value.is_super) {
          console.log("userInfo:", userInfo.value)
          SystemService.areaInfo({
            id:
              userInfo.value?.district_id ||
              userInfo.value?.city_id ||
              userInfo.value?.province_id,
          }).then((res) => {
            let nodes = [
              {
                value: `${res.payload.id}-${res.payload.area_level}`,
                label: res.payload.name,
                leaf: ["province", "city"].includes(res.payload.area_level)
                  ? false
                  : true,
              },
            ];
            resolve(nodes);
          });
        } else {
          SystemService.areaChildrenList({ parent_id: node.value?.split("-")[0] }).then(
            (res) => {
              const nodes = res.payload.list.map((cur:any) => ({
                value: `${cur.id}-${cur.area_level}`,
                label: cur.name,
                leaf: ["province", "city"].includes(cur.area_level) ? false : true,
              }));
              resolve(nodes);
            }
          );
        }
      },
    };
    const dialogType = ref('add')
  
    const showDialog = (type: string, row?: any) => {
      dialogVisible.value = true
      dialogType.value = type
      
      if (type === 'edit' && row) {
        form.value.id = row.id
        form.value.name = row.name
        form.value.fullname = row.fullname
        form.value.roles = row.roles?.map((item: RoleType) => item.id)
        form.value.desc = row.desc
        // console.log("row: log", row.logo)
        if (row.logo) {
          const minioApi =  configStore.getMinioApi() 
          const logoStr = row.logo
          const logoUrl = logoStr.startsWith("http")?logoStr:(minioApi + logoStr).replace(/([^:]\/)\/+/g, "$1");
          form.value.logo = [{ url: logoUrl }] as LogoFile[];
        } else {
          form.value.logo = [];
        }
        let address = [];
        if (row.province_id) {
          address.push(`${row.province_id}-province`);
        }
        if (row.city_id) {
          address.push(`${row.city_id}-city`);
        }
        if (row.district_id) {
          address.push(`${row.district_id}-district`);
        }
        form.value.address = address;
      } else {
        form.value.id = 0
        form.value.name = ''
        form.value.fullname = ''
        form.value.logo = []
        form.value.desc = ''
        form.value.roles = []
        form.value.address = []
      }
      nextTick(() => {
        uploadRef.value.uploadEcho(form.value.logo)
      })
    }
  


  
    const deleteComapny =async  (row: any) => {
      ElMessageBox.confirm('确定删除该公司吗？', '删除确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'error'
      }).then(async () => {
        let resp = await UserService.deleteCompany({id: row.id})
        if(resp.code === ApiStatus.success) {
          ElMessage.success('删除成功')
          await getCompanyList()
        } else {
          ElMessage.error(resp.message)
        }
  
      })
    }
  


    const handleSubmit = async (formEl: FormInstance | undefined) => {
      if (!formEl) return
      await formEl.validate(async (valid) => {
        if (valid) {
          let params: any = {};
          if (form.value.logo.length > 0 ) {
            if(form.value.logo[0].size > 0) {
              let imgFileData = new FormData()
              imgFileData.append("file", form.value.logo[0], form.value.logo[0].name);
              imgFileData.append("filename", form.value.logo[0].name);
              const imgResp =  await SystemService.uploadFile(imgFileData)
              if (imgResp.code !== ApiStatus.success) {
                ElMessage.error("上传图片错误:" + imgResp.message)
                return 
              }
              params.logo = imgResp.payload.url;
            } else {
              params.logo = form.value.logo[0].url;
            }
          }
          
          let result: BaseResult ;
          //编辑
          form.value.address.forEach((cur) => {
            const [id, addrType] = cur.split("-");
            if (addrType === "province") {
              params.province_id =  Number(cur.split("-")[0]);
            }
            if (addrType ===  "city") {
              params.city_id = Number(cur.split("-")[0]);
            }
            if (addrType === "district") {
              params.district_id =  Number(cur.split("-")[0]);
            }
          });
          params.name =  form.value.name;
          params.fullname = form.value.fullname;
          params.desc = form.value.desc;
          params.roles = form.value.roles.join(",");
          if (dialogType.value === 'edit') {
            params.id = form.value.id;
            result = await UserService.updateCompany(params)
          } else {
            result = await UserService.addCompany(params)
          }
          if (result.code === ApiStatus.success) {
            const message = dialogType.value === 'add' ? '新增成功' : '修改成功'
            formEl.resetFields()
            await getCompanyList()
            ElMessage.success(message)
            dialogVisible.value = false
          } else{
            ElMessage.error(result.message)
          }
        }
      })
    }
  
    const formatDate = (date: string) => {
      return new Date(date)
        .toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit'
        })
        .replace(/\//g, '-')
    }
  </script>
  
  <style lang="scss" scoped>
    .page-content {
      .svg-icon {
        width: 1.8em;
        height: 1.8em;
        overflow: hidden;
        vertical-align: -8px;
        fill: currentcolor;
      }
    }
  
  .menu-icon {
      margin-right: 5px;
      font-size: 16px;
    }
  
  .custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    padding-right: 8px;
  } 
  
  
  .auth-checkbox-group {
    display: flex;
    flex-wrap: wrap; /* 允许换行 */
    gap: 2px; /* 间距 */
    margin-top: 6px;
  }
  
  .auth-checkbox-item {
    flex: 0 0 auto; /* 禁止伸缩 */
    min-width: 10px; /* 最小宽度 */
    white-space: nowrap; /* 防止文字换行 */
  }
  
  /* 可选：添加悬停效果 */
  // .auth-checkbox-item:hover {
  //   background: #f5f7fa;
  //   border-radius: 4px;
  //   padding: 2px;
  // }
  </style>
  