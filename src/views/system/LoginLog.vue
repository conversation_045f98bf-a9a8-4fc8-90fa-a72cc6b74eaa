<template>
  <div class="page-content">
    <art-table-bar
      :showTop="false"
      @search="search"
      @reset="resetForm(searchFormRef)"
      @changeColumn="changeColumn"
      :columns="columns"
    >
      <template #top>
        <el-form :model="searchForm" ref="searchFormRef" label-width="82px">
          <el-row :gutter="20">
            <art-form-input label="操作人" prop="username" v-model="searchForm.username"  clearable/>
            <art-form-input label="操作项" prop="title" v-model="searchForm.title" clearable />
          </el-row>
        </el-form>
      </template>
      <!-- <template #bottom>
        <el-button @click="showDialog('add')" v-ripple>添加用户</el-button>
      </template> -->
    </art-table-bar>

    <art-table 
        :data="logList" 
        :currentPage="params.page" 
        :pageSize="params.page_size" 
        :total="total"
        @current-change="currentPageChange"
        @size-change="pageSizeChange"
      >
      <el-table-column label="操作人" prop="username" />
      <el-table-column label="CID" prop="company_id" width="80" />
      <el-table-column label="联系方式" prop="phone" />
      <el-table-column label="操作项" prop="title" />
      <el-table-column label="操作内容" prop="content" />
      <el-table-column label="操作时间" prop="created_at" >
        <template #default="scope">
          {{ moment(scope.row.created_at).format('YYYY-MM-DD HH:mm:ss') }}
        </template>
      </el-table-column>
    </art-table>
  </div>
</template>

<script setup lang="ts">
import { SystemService } from '@/api/systemApi';
import { SystemLogType } from '@/types/system';
import { ApiStatus } from '@/utils/http/status';
import { FormInstance } from 'element-plus';
import moment from 'moment';

const logList = ref<SystemLogType[]>([]);

interface Params {
  page: number;
  page_size: number;
  total?: number;
  username?: string; // 可选属性
  title?:string ;  
}
const total = ref(0);
const params = ref<Params>({page:1, page_size: 20})
const getSystemLog = async () => {
  if(searchForm.username !== "") {
    params.value.username = searchForm.username.trim();
  }
  if (searchForm.title !== "" ) {
    params.value.title = searchForm.title.trim();
  }
  const resp = await SystemService.listLog(params.value)
  if (resp.code === ApiStatus.success) {
    logList.value = resp.payload.list
    total.value = resp.payload.total
  } 
}
const  currentPageChange = async(currentPage: number) => {
  params.value.page = currentPage
  await getSystemLog()
}
const pageSizeChange = async (size: number) => {
  params.value.page_size = size 
  await getSystemLog()
}

const columns = reactive([
    { name: '操作人', show: true },
    { name: '操作项', show: true },
])
const resetForm = (formEl: FormInstance | undefined) => {
  searchForm.username = ""
  searchForm.title = ""
  if (!formEl) return
  formEl.resetFields()
}

const changeColumn = (list: any) => {
  columns.values = list
}
const searchFormRef = ref<FormInstance>();
const searchForm = reactive({
    username: '',
    title: '',
});
const search = async () => { 
  await getSystemLog()
}

onMounted(async () => {
  await getSystemLog()
})
</script>

<style lang="scss" scoped></style>
