<template>
  <div class="page-content">
    <el-row>
      <el-col :xs="24" :sm="12" :lg="6">
        <el-input placeholder="角色名称"></el-input>
      </el-col>
      <div style="width: 12px"></div>
      <el-col :xs="24" :sm="12" :lg="6" class="el-col2">
        <el-button v-ripple>搜索</el-button>
        <el-button @click="showDialog('add')" v-ripple>新增角色</el-button>
      </el-col>
    </el-row>

    <art-table 
        :data="tableData" 
        :currentPage="params.page" 
        :pageSize="params.page_size" 
        :total="total"
        @current-change="currentPageChange"
        @size-change="pageSizeChange"
        >
      <template #default>
        <el-table-column label="角色名称" prop="title" />
        <el-table-column label="描述" prop="desc" />
        <el-table-column label="状态" prop="status">
          <template #default="scope">
            <el-tag :type="scope.row.status === 1 ? 'primary' : 'info'">
              {{ scope.row.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" prop="created_at">
          <template #default="scope">
            {{ formatDate(scope.row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="100px">
          <template #default="scope">
            <el-row>
              <ArtButtonMore
                :list="[
                  { key: 'permission', label: '菜单权限' },
                  { key: 'edit', label: '编辑角色' },
                  { key: 'delete', label: '删除角色' }
                ]"
                @click="buttonMoreClick($event, scope.row)"
              />
            </el-row>
          </template>
        </el-table-column>
      </template>
    </art-table>

    <el-dialog
      v-model="dialogVisible"
      :title="dialogType === 'add' ? '新增角色' : '编辑角色'"
      width="30%"
    >
      <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="角色名称" prop="title">
          <el-input v-model="form.title" />
        </el-form-item>
        <el-form-item label="描述" prop="desc">
          <el-input v-model="form.desc" type="textarea" :rows="3" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-switch v-model="form.status" :active-value="1"  :inactive-value="2" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit(formRef)">提交</el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog v-model="permissionDialog" title="菜单权限" width="30%">
      <div :style="{  overflowY: 'scroll' }">
        <el-tree
            ref="treeRef"
            :data="menuList"
            :props="defaultProps"
            show-checkbox
            node-key="menu_id"
          >
            <template #default="{ node, data }">
              <div class="custom-tree-node ">
                <div>
                  <!-- 图标 -->
                  <i  class="menu-icon iconfont-sys"  v-html="data.meta.icon"></i>
                  <!-- 主名称 -->
                  <span>{{ node.label }}</span>
                </div>
                <!-- 权限标识（右侧悬浮显示） -->
                <!-- <div  class="auth-checkbox-group"   v-if="data.meta.auth_list.length">
                  <el-checkbox-group  v-model="data.meta.auth_list" >
                      <el-checkbox  v-for="auth in data.meta.auth_list" :key="auth.auth_mark" :label="auth.auth_mark"  class="auth-checkbox-item">
                        {{ auth.name }}
                      </el-checkbox>
                  </el-checkbox-group>
                </div> -->
              </div>
            </template>
          </el-tree>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="permissionDialog = false">取消</el-button>
          <el-button type="primary" @click="submitRoleMenu()">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { ButtonMoreItem } from '@/components/core/forms/ArtButtonMore.vue'
  import { useMenuStore } from '@/store/modules/menu'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import type { TreeInstance } from 'element-plus';
  import type { FormInstance, FormRules } from 'element-plus'
  import { formatMenuTitle } from '@/utils/menu'
  import { ApiStatus } from '@/utils/http/status';
  import { RoleService } from '@/api/roleApi';
  import { RoleType } from '@/types/role';
  import { BaseResult, PageInfo, PaginationResult } from '@/types/axios'

  const dialogVisible = ref(false)
  const permissionDialog = ref(false)
  const menuList = computed(() => addAuthToListItems( useMenuStore().getMenuList()))

  // console.log("menuList:", menuList.value);

  const formRef = ref<FormInstance>()
  
  const treeRef = ref<TreeInstance>()

  const rules = reactive<FormRules>({
    title: [
      { required: true, message: '请输入角色名称', trigger: 'blur' },
      { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
    ],
    status: [{ required: true, message: '请输入角色状态', trigger: 'blur' }]
  })


  const form = reactive({
    id: 0,
    title: '',
    desc: '',
    status: 1,
    menu_list: [] as number[],
  })

  const tableData  = ref<RoleType[]>([]);
  const params = ref({page: 1, page_size: 20})
  const total = ref(0)

  onMounted(async () => {
    await getRoleList()
  })

  // 将权限字符，加到 children下面，可以方便el-tree显示
  function addAuthToListItems(data:any) {
    return data.map((item:any) => {
      // 创建新节点避免修改原数据
      const newItem = { ...item };
      // 转换 auth_list 为子节点格式
      const authChildren = newItem.meta.auth_list?.map((auth:any) => ({
        menu_id: auth.menu_id, // 生成唯一ID
        name: auth.name,
        path: "", // 自定义路径
        component: "", // 默认组件
        meta: {
          title: auth.title,
          icon: auth.icon, // 默认图标
          sort: auth.sort,
          is_hide: false
        },
        children: [] // 保持树形结构
      }));
      // 合并原有子节点和权限子节点
      newItem.children = [
        ...(newItem.children || []),
        ...(authChildren || [])
      ];
      // 递归处理子节点
      newItem.children = addAuthToListItems(newItem.children);
      return newItem;
    });
  }



  const submitRoleMenu = () => {
    nextTick(async () => {
      const items: number[] = treeRef.value!.getCheckedKeys() as number[];
      const half_items : number[] = treeRef.value!.getHalfCheckedKeys() as number[];   
      form.menu_list = [...half_items, ...items];
      const resp = await RoleService.updateRole(form)
      if(resp.code === ApiStatus.success) {
        permissionDialog.value = false
        ElMessage.success("更新成功")
      } else {
        ElMessage.error(resp.message)
      }
    })
  }

  // 从后端获取角色列表
  const getRoleList =async () => {
    const resp : PaginationResult<RoleType[]> = await RoleService.getRoleList(params.value)
    if(resp.code === ApiStatus.success) {
      tableData.value = resp.payload.list
    } else{
      ElMessage.error(resp.message)
    }
  }
  const  currentPageChange = async(page: number) => {
    params.value.page = page 
    await getRoleList()
  }
  const  pageSizeChange = async(size: number) => {
    params.value.page_size = size 
    await getRoleList()
  }

  const dialogType = ref('add')

  const showDialog = (type: string, row?: any) => {
    dialogVisible.value = true
    dialogType.value = type

    if (type === 'edit' && row) {
      form.id = row.id
      form.title = row.title
      form.desc = row.desc
      form.status = row.status
    } else {
      form.id = 0
      form.title = ''
      form.desc = ''
      form.status = 1
    }
  }

  const buttonMoreClick = (item: ButtonMoreItem, row: any) => {
    if (item.key === 'permission') {
      showPermissionDialog(row)
    } else if (item.key === 'edit') {
      showDialog('edit', row)
    } else if (item.key === 'delete') {
      deleteRole(row)
    }
  }

  const showPermissionDialog = async (row:any) => {
    form.id = row.id;
    permissionDialog.value = true
    await getRoleMenu(row.id)
  }

  const getRoleMenu = async (roleId: number) => {
    const resp = await RoleService.getRoleMenuList({id: roleId})
    if(resp.code === ApiStatus.success) {
      let checkedMap  = <any>{};
      resp.payload.list.forEach((item:any) => {
        if (checkedMap[item.parent_id] !== undefined) {
          // 删除父节点(因为选中父节点，那么所有的子节点也会被选中，但有些子节点并不要选中 )
          delete checkedMap[item.parent_id]
        } 
        checkedMap[item.menu_id] = item
      })
      let checkedItems = Object.values(checkedMap)
      // 只要设置子项，那么父节点也会被选中
      treeRef.value!.setCheckedKeys(checkedItems.map((item:any) => item.menu_id))
    } else{
      ElMessage.error(resp.message)
    }
  }


  const defaultProps = {
    children: 'children',
    label: (data: any) => formatMenuTitle(data.meta?.lang_tag, data.meta?.title) || data.name
  }

  const deleteRole =async  (row: any) => {
    ElMessageBox.confirm('确定删除该角色吗？', '删除确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'error'
    }).then(async () => {
      let resp = await RoleService.deleteRole({id: row.id})
      if(resp.code === ApiStatus.success) {
        ElMessage.success('删除成功')
        await getRoleList()
      } else {
        ElMessage.error(resp.message)
      }

    })
  }

  const handleSubmit = async (formEl: FormInstance | undefined) => {
    if (!formEl) return

    await formEl.validate(async (valid) => {
      if (valid) {
        let result: BaseResult ;
        if(dialogType.value === 'add') {
          result = await RoleService.addRole(form)
        } else {
          result = await RoleService.updateRole(form)
        }
        if (result.code === ApiStatus.success) {
          const message = dialogType.value === 'add' ? '新增成功' : '修改成功'
          formEl.resetFields()
          await getRoleList();
          ElMessage.success(message)
          dialogVisible.value = false
        } else{
          ElMessage.error(result.message)
        }
      }
    })
  }

  const formatDate = (date: string) => {
    return new Date(date)
      .toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
      .replace(/\//g, '-')
  }
</script>

<style lang="scss" scoped>
  .page-content {
    .svg-icon {
      width: 1.8em;
      height: 1.8em;
      overflow: hidden;
      vertical-align: -8px;
      fill: currentcolor;
    }
  }

.menu-icon {
    margin-right: 5px;
    font-size: 16px;
  }

.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
} 


.auth-checkbox-group {
  display: flex;
  flex-wrap: wrap; /* 允许换行 */
  gap: 2px; /* 间距 */
  margin-top: 6px;
}

.auth-checkbox-item {
  flex: 0 0 auto; /* 禁止伸缩 */
  min-width: 10px; /* 最小宽度 */
  white-space: nowrap; /* 防止文字换行 */
}

/* 可选：添加悬停效果 */
// .auth-checkbox-item:hover {
//   background: #f5f7fa;
//   border-radius: 4px;
//   padding: 2px;
// }
</style>
