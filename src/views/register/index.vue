<template>
    <div class="login register">
      <div class="left-wrap">
        <LoginLeftView></LoginLeftView>
      </div>
      <div class="right-wrap">
        <div class="header">
          <svg class="icon" aria-hidden="true">
            <use xlink:href="#iconsys-zhaopian-copy"></use>
          </svg>
          <h1>{{ systemName }}</h1>
        </div>
        <div class="login-wrap">
          <div class="form">
            <h3 class="title">{{ $t('register.title') }}</h3>
            <p class="sub-title">{{ $t('register.subTitle') }}</p>
            <el-form ref="formRef" :model="formData" :rules="rules" label-position="top">
              <el-form-item prop="username">
                <el-input
                  v-model.trim="formData.username"
                  :placeholder="$t('register.placeholder[0]')"
                  size="large"
                />
              </el-form-item>
  
              <el-form-item prop="password">
                <el-input
                  v-model.trim="formData.password"
                  :placeholder="$t('register.placeholder[1]')"
                  size="large"
                  type="password"
                  autocomplete="off"
                />
              </el-form-item>
  
              <el-form-item prop="confirm_password">
                <el-input
                  v-model.trim="formData.confirm_password"
                  :placeholder="$t('register.placeholder[2]')"
                  size="large"
                  type="password"
                  autocomplete="off"
                  @keyup.enter="register"
                />
              </el-form-item>
              <el-form-item prop="code">
                <el-input
                  auto-complete="off"
                  placeholder="验证码"
                  maxlength="5"
                  v-model.trim="formData.verify_code"
                  @keyup.enter="register"
                  style="width: 63%"
                  >
                </el-input>
                <div class="login-code">
                  <img :src="codeUrl" @click="getCode" class="login-code-img" />
                </div>
              </el-form-item>
              <el-form-item prop="agreement">
                <el-checkbox v-model="formData.agreement">
                  {{ $t('register.agreeText') }}
                  <router-link
                    style="color: var(--main-color); text-decoration: none"
                    to="#"
                    >{{ $t('register.privacyPolicy') }}</router-link
                  >
                </el-checkbox>
              </el-form-item>
  
              <div style="margin-top: 15px">
                <el-button
                  class="register-btn"
                  size="large"
                  type="primary"
                  @click="register"
                  :loading="loading"
                  v-ripple
                >
                  {{ $t('register.submitBtnText') }}
                </el-button>
              </div>
  
              <div class="footer">
                <p>
                  {{ $t('register.hasAccount') }}
                  <router-link to="/login">{{ $t('register.toLogin') }}</router-link>
                </p>
              </div>
            </el-form>
          </div>
        </div>
      </div>
    </div>
  </template>
  
  <script setup lang="ts">
    import { UserService } from '@/api/usersApi'
import AppConfig from '@/config'
import { ApiStatus } from '@/utils/http/status'
    import { ElMessage } from 'element-plus'
    import type { FormInstance, FormRules } from 'element-plus'
    import { useI18n } from 'vue-i18n'
  
    const { t } = useI18n()
  
    const router = useRouter()
    const formRef = ref<FormInstance>()
  
    const systemName = AppConfig.systemInfo.name
    const loading = ref(false)
  
    const formData = reactive({
      username: '',
      password: '',
      confirm_password: '',
      captcha_id: "",
      verify_code: "",
      agreement: true
    })
  
    const validatePass = (rule: any, value: string, callback: any) => {
      if (value === '') {
        callback(new Error(t('register.placeholder[1]')))
      } else {
        if (formData.confirm_password !== '') {
          formRef.value?.validateField('confirm_password')
        }
        callback()
      }
    }
  
    const validatePass2 = (rule: any, value: string, callback: any) => {
      if (value === '') {
        callback(new Error(t('register.rule[0]')))
      } else if (value !== formData.password) {
        callback(new Error(t('register.rule[1]')))
      } else {
        callback()
      }
    }
  
    const rules = reactive<FormRules>({
      username: [
        { required: true, message: t('register.placeholder[0]'), trigger: 'blur' },
        { min: 3, max: 20, message: t('register.rule[2]'), trigger: 'blur' }
      ],
      password: [
        { required: true, validator: validatePass, trigger: 'blur' },
        { min: 6, message: t('register.rule[3]'), trigger: 'blur' }
      ],
      confirm_password: [{ required: true, validator: validatePass2, trigger: 'blur' }],
      agreement: [
        {
          validator: (rule: any, value: boolean, callback: any) => {
            if (!value) {
              callback(new Error(t('register.rule[4]')))
            } else {
              callback()
            }
          },
          trigger: 'change'
        }
      ]
    })
  
    const register = async () => {
      if (!formRef.value) return
      try {
        await formRef.value.validate()
        loading.value = true
        let resp = await UserService.register(formData)
        if(resp.code === ApiStatus.success){
          loading.value = false
          ElMessage.success("注册成功")
          toLogin();
        } else {
          loading.value  = false
          ElMessage.error("注册失败")
        }
      } catch (error) {
        loading.value = false
        console.log('验证失败', error)
      }
    }
  
    const toLogin = () => {
      setTimeout(() => {
        router.push('/login')
      }, 1000)
    }

    //获取验证码
const codeUrl = ref("");
const getCode = async () => {
  const res = await UserService.captcha();
  if (res.code === ApiStatus.success && res.payload ) {
    codeUrl.value = res.payload.img_data;
    formData.captcha_id = res.payload.captcha_id;
  }
};

onMounted( async () => {
    await getCode();
})
</script>
  
<style lang="scss" scoped>
  @use '../login/index' as login;
  @use './index' as register;
</style>
  