<template>
    <div>
        <el-row :gutter="20" class="custom-row">
            <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4" v-for="card in companyList" :key="card.id"  class="custom-col"  @click="changeCompany(card.id)">
                <ArtImageCard
                    :imageUrl="card.imageUrl"
                    :title="card.title"
                    :category="card.category"
                    :desc="card.desc"
                    :date="card.date"
                    :imgDesc="card.imgDesc"
                />
            </el-col>
        </el-row>
    </div>
</template>

<script setup lang="ts">
import cover1 from '@imgs/cover/img1.jpg'
import cover2 from '@imgs/cover/img2.jpg'
import cover3 from '@imgs/cover/img3.jpg'
import cover4 from '@imgs/cover/img4.jpg'
import cover5 from '@imgs/cover/img5.jpg'
import cover6 from '@imgs/cover/img6.jpg'
import cover7 from '@imgs/cover/img7.jpg'
import cover8 from '@imgs/cover/img8.jpg'
import cover9 from '@imgs/cover/img9.jpg'
import cover10 from '@imgs/cover/img10.jpg'
import { UserService } from '@/api/usersApi'
import { ApiStatus } from '@/utils/http/status'
import moment from 'moment'
import { useUserStore } from '@/store/modules/user'
import { useMenuStore } from '@/store/modules/menu'

const imageList = ref([
    cover1, cover2, cover3, cover4, cover5, cover6, cover7, cover8,cover9,cover10
])

const getImage = (index:number) => {
    let curImage = imageList.value[index % imageList.value.length];
    return curImage;
}

export type CompanyType = {
    id: number
    imageUrl: string
    category: string 
    title: string 
    desc: string 
    date: string  
    imgDesc: string 
}

const companyList = ref<CompanyType[]>([]);
const params = ref({page: 1, page_size: 100})
const getAuthedCompany = async () => {
    const resp = await UserService.listAuthedCompany(params.value)
    companyList.value = [];
    if (resp.code === ApiStatus.success) {
        resp.payload.list.forEach((item) => {
            companyList.value.push({
                id: item.id,
                imageUrl: getImage(item.id),
                category: item.name,
                title: item.fullname,
                desc: item.desc,
                date: moment(item.created_at).format('YYYY-MM-DD HH:mm:ss'),
                imgDesc: "点击进入"
            })
        })
    }
}


const changeCompany = (id:number) => {
    // localStorage.setItem("show_control", "show");
    useMenuStore().changeCompany(id, "") // 
};


onMounted( async () => {
    await getAuthedCompany()
})

</script>

<style lang="scss" scoped>


/* 列高度继承 */
.custom-col {

  margin-bottom: 20px;
}


</style>