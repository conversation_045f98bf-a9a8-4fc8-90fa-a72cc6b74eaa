<template>
    <div :style="{ width: '300px', height: '150px'}">
        <VueUiRings
            :config="config"
            :dataset="dataset"
        />
    </div>
</template>


<script setup>
import { ref } from "vue";
import { VueUiRings } from "vue-data-ui";
import "vue-data-ui/style.css"; // If you are using multiple components, place styles import in your main

const config = ref({
        responsive: false,
        theme: '',
        customPalette: [],
        useCssAnimation: true,
        useBlurOnHover: true,
        style: {
            fontFamily: 'inherit',
            chart: {
                backgroundColor: '#FFFFFFff',
                color: '#1A1A1Aff',
                layout: {
                    labels: {
                        dataLabels: {
                            prefix: '',
                            suffix: '',
                            formatter: null
                        }
                    },
                    rings: {
                        strokeWidth: 2,
                        stroke: '#FFFFFFff',
                        gradient: {
                            show: true,
                            intensity: 100,
                            underlayerColor: '#FFFFFF'
                        },
                        useShadow: true
                    }
                },
                legend: {
                    show: true,
                    bold: false,
                    backgroundColor: '#FFFFFFff',
                    color: '#1A1A1Aff',
                    fontSize: 14,
                    roundingValue: 0,
                    roundingPercentage: 0
                },
                title: {
                    text: 'Title',
                    color: '#1A1A1Aff',
                    fontSize: 20,
                    bold: true,
                    textAlign: 'center',
                    paddingLeft: 0,
                    paddingRight: 0,
                    subtitle: {
                        color: '#A1A1A1ff',
                        text: '',
                        fontSize: 16,
                        bold: false
                    }
                },
                tooltip: {
                    show: true,
                    color: '#1A1A1Aff',
                    backgroundColor: '#FFFFFFff',
                    fontSize: 14,
                    customFormat: null,
                    borderRadius: 4,
                    borderColor: '#e1e5e8',
                    borderWidth: 1,
                    backgroundOpacity: 30,
                    position: 'center',
                    offsetY: 24,
                    showValue: true,
                    showPercentage: true,
                    roundingValue: 0,
                    roundingPercentage: 0
                }
            }
        },
        userOptions: {
            show: true,
            showOnChartHover: false,
            keepStateOnChartLeave: true,
            position: 'right',
            buttons: {
                tooltip: true,
                pdf: true,
                csv: true,
                img: true,
                table: true,
                labels: false,
                fullscreen: true,
                sort: false,
                stack: false,
                animation: false,
                annotator: true
            },
            buttonTitles: {
                open: 'Open options',
                close: 'Close options',
                tooltip: 'Toggle tooltip',
                pdf: 'Download PDF',
                csv: 'Download CSV',
                img: 'Download PNG',
                table: 'Toggle table',
                fullscreen: 'Toggle fullscreen',
                annotator: 'Toggle annotator'
            },
            print: {
                allowTaint: false,
                backgroundColor: '#FFFFFFff',
                useCORS: false,
                onclone: null,
                scale: 2,
                logging: false
            }
        },
        table: {
            show: false,
            responsiveBreakpoint: 400,
            columnNames: {
                series: 'Series',
                value: 'Value',
                percentage: 'Percentage'
            },
            th: {
                backgroundColor: '#FFFFFFff',
                color: '#1A1A1Aff',
                outline: 'none'
            },
            td: {
                backgroundColor: '#FFFFFFff',
                color: '#1A1A1Aff',
                outline: 'none',
                roundingValue: 0,
                roundingPercentage: 0
            }
        }
    });

const dataset = ref([
    {
        name: 'Serie 1',
        values: [
            100
        ],
        color: '#6376DD'
    },
    {
        name: 'Serie 2',
        values: [
            200
        ],
        color: '#42d392'
    },
    {
        name: 'Serie 3',
        values: [
            300
        ],
        color: '#ff6400'
    }
]);

</script>
