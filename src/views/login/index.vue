<template>
  <div class="login">
    <div class="left-wrap">
      <left-view></left-view>
    </div>
    <div class="right-wrap">
      <div class="top-right-wrap">
        <!-- <div class="btn config-btn" @click="setConfig"> -->
          <!-- <i class="iconfont-sys">&#xe755;</i> -->
        <!-- </div> -->
        <div class="btn theme-btn" @click="toggleTheme">
          <i class="iconfont-sys">
            {{ isDark ? '&#xe6b5;' : '&#xe725;' }}
          </i>
        </div>
        <el-dropdown @command="changeLanguage" popper-class="langDropDownStyle">
          <div class="btn language-btn">
            <i class="iconfont-sys icon-language">&#xe611;</i>
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <div v-for="lang in languageOptions" :key="lang.value" class="lang-btn-item">
                <el-dropdown-item
                  :command="lang.value"
                  :class="{ 'is-selected': locale === lang.value }"
                >
                  <span class="menu-txt">{{ lang.label }}</span>
                  <i v-if="locale === lang.value" class="iconfont-sys icon-check">&#xe621;</i>
                </el-dropdown-item>
              </div>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
      <div class="header">
        <svg class="icon" aria-hidden="true">
          <use xlink:href="#iconsys-zhaopian-copy"></use>
        </svg>
        <h1>{{ systemName }}</h1>
      </div>
      <div class="login-wrap">
        <div class="form">
          <h3 class="title">{{ $t('login.title') }}</h3>
          <p class="sub-title">{{ $t('login.subTitle') }}</p>
          <el-form
            ref="formRef"
            :model="formData"
            :rules="rules"
            @keyup.enter="handleSubmit"
            style="margin-top: 25px"
          >
            <el-form-item prop="username">
              <el-input
                :placeholder="$t('login.placeholder[0]')"
                size="large"
                v-model.trim="formData.username"
              />
            </el-form-item>
            <el-form-item prop="password">
              <el-input
                :placeholder="$t('login.placeholder[1]')"
                size="large"
                v-model.trim="formData.password"
                type="password"
                radius="8px"
                autocomplete="off"
                @keyup.enter="handleSubmit"
              />
            </el-form-item>
            <el-form-item prop="code">
              <el-input
                auto-complete="off"
                placeholder="验证码"
                maxlength="5"
                v-model.trim="formData.code"
                @keyup.enter="handleSubmit"
                style="width: 63%"
              >
            </el-input>
            <div class="login-code">
              <img :src="codeUrl" @click="getCode" class="login-code-img" />
            </div>
          </el-form-item>
            <!-- <div class="drag-verify">
              <div class="drag-verify-content" :class="{ error: !isPassing && isClickPass }">
                <DragVerify
                  ref="dragVerify"
                  v-model:value="isPassing"
                  :width="width < 500 ? 328 : 438"
                  :text="$t('login.sliderText')"
                  textColor="var(--art-gray-800)"
                  :successText="$t('login.sliderSuccessText')"
                  :progressBarBg="getCssVariable('--el-color-primary')"
                  background="var(--art-gray-200)"
                  handlerBg="var(--art-main-bg-color)"
                  @pass="onPass"
                />
              </div>
              <p class="error-text" :class="{ 'show-error-text': !isPassing && isClickPass }">{{
                $t('login.placeholder[2]')
              }}</p>
            </div> -->

            <!--
            <div class="forget-password">
              <el-checkbox v-model="formData.rememberPassword">{{
                $t('login.rememberPwd')
              }}</el-checkbox>
              <router-link to="/forget-password">{{ $t('login.forgetPwd') }}</router-link>
            </div>
          -->

            <div style="margin-top: 30px">
              <el-button
                class="login-btn"
                size="large"
                type="primary"
                @click="handleSubmit"
                :loading="loading"
                v-ripple
              >
                {{ $t('login.btnText') }}
              </el-button>
            </div>

            <div class="footer">
              <p>
                {{ $t('login.noAccount') }}
                <router-link to="/register">{{ $t('login.register') }}</router-link>
              </p>
            </div>
          </el-form>
        </div>
      </div>
    </div>

    <!-- <SysConfig ref="sysConfigRef" @setComplate="setComplate"></SysConfig> -->
  </div>
</template>

<script setup lang="ts">
  import LeftView from '@/components/core/views/login/LoginLeftView.vue'
  // import SysConfig from './components/SysConfig.vue'
  import AppConfig from '@/config'
  import { ElMessage, ElNotification } from 'element-plus'
  import { useUserStore } from '@/store/modules/user'
  import { ApiStatus } from '@/utils/http/status'
  import { getCssVariable } from '@/utils/colors'
  import { languageOptions } from '@/language'
  import { LanguageEnum, SystemThemeEnum } from '@/enums/appEnum'
  import { useI18n } from 'vue-i18n'

  const { t } = useI18n()
  import { useSettingStore } from '@/store/modules/setting'
  import type { FormInstance, FormRules } from 'element-plus'

  const userStore = useUserStore()
  const router = useRouter()
  const isPassing = ref(false)
  const isClickPass = ref(false)

  const systemName = AppConfig.systemInfo.name
  const formRef = ref<FormInstance>()
  const formData = reactive({
    username: AppConfig.systemInfo.login.username,
    password: AppConfig.systemInfo.login.password,
    code:  "",
    captcha_id: "",
    rememberPassword: true
  })

  const rules = computed<FormRules>(() => ({
    username: [{ required: true, message: t('login.placeholder[0]'), trigger: 'blur' }],
    password: [{ required: true, message: t('login.placeholder[1]'), trigger: 'blur' }],
    code:     [{ required: true, message: "请输入验证码", trigger: 'blur' }],
  }))

  const loading = ref(false)
  const { width } = useWindowSize()

  const settingStore = useSettingStore()
  const isDark = computed(() => settingStore.isDark)

//获取验证码
const codeUrl = ref("");
const getCode = async () => {
  const res = await UserService.captcha();
  if (res.code === ApiStatus.success && res.payload ) {
    codeUrl.value = res.payload.img_data;
    formData.captcha_id = res.payload.captcha_id;
  }
};


  const onPass = () => {}

  const submitLoading = ref(false);
  const handleSubmit = async () => {
    if (!formRef.value) return
    if (submitLoading.value) return 
    await formRef.value.validate(async (valid) => {
      if (valid) {
        // if (!isPassing.value) {
        //   isClickPass.value = true
        //   return
        // }
        submitLoading.value = true;

        loading.value = true

        // 延时辅助函数
        const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms))

        try {
          const res = await UserService.login({
            body: JSON.stringify({
              username: formData.username,
              password: formData.password,
              verify_code: formData.code,
              captcha_id: formData.captcha_id,
            })
          })

          if (res.code === ApiStatus.success && res.payload) {
            // 设置 token
            userStore.setToken(res.payload.accessToken)

            // 获取用户信息
            const userRes = await UserService.getUserInfo()
            if (userRes.code === ApiStatus.success) {
              userStore.setUserInfo(userRes.payload)
            }
            await useMenuStore().refreshMenuList()
            // 设置登录状态
            userStore.setLoginStatus(true)
            // 延时辅助函数
            await delay(1000)
            // 登录成功提示
            showLoginSuccessNotice()
            // 跳转首页
            // router.push("/")
            router.replace("/");
          } else {
            ElMessage.error(res.message)
          }
        } finally {
          await delay(1000)
          loading.value = false
          submitLoading.value = false
        }
      }
    })
  }

  // 登录成功提示
  const showLoginSuccessNotice = () => {
    setTimeout(() => {
      ElNotification({
        title: t('login.success.title'),
        type: 'success',
        showClose: false,
        duration: 2500,
        zIndex: 10000,
        message: `${t('login.success.message')}, ${systemName}!`
      })
    }, 300)
  }

  // 切换语言
  const { locale } = useI18n()

  const changeLanguage = (lang: LanguageEnum) => {
    if (locale.value === lang) return
    locale.value = lang
    userStore.setLanguage(lang)
  }
  const setComplate =async () => {
     await getCode();   
  }

  // 切换主题
  import { useTheme } from '@/composables/useTheme'
  import { UserService } from '@/api/usersApi'



  const toggleTheme = () => {
    let { LIGHT, DARK } = SystemThemeEnum
    useTheme().switchThemeStyles(useSettingStore().systemThemeType === LIGHT ? DARK : LIGHT)
  }

  const baseUrl =  configStore.getApiUrl()  
  // const sysConfigRef = ref();
  import { SystemService } from '@/api/systemApi'
  import { EnvConfig } from '@/config/types'
  import { useMenuStore } from '@/store/modules/menu'
  // const setConfig = () => {
    // sysConfigRef.value.visibleDialog()
  // }
  // const getSysConfig =async () => {
  //   const resp = await SystemService.getSysConfig()
  //   if (resp.code === ApiStatus.success) {
  //     if(baseUrl.value !== resp.payload.api) {
  //       const envData: EnvConfig =  resp.payload;
  //       settingStore.setEnvConfig(envData);
  //     }
  //   }
  // }

  onMounted( async () => {
    await getCode();
    // await getSysConfig();
  })

</script>

<style lang="scss" scoped>
  @use './index';
</style>
