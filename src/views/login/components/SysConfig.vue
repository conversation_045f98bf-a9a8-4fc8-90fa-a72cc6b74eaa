<template>
    <!-- 配置后端接口api的 -->
    <el-dialog
      v-model="dialogVisible"
      title="配置"
      :width="dialogWidth"
      @close="closeDialog"
    >
      <el-form ref="formRef" :model="sysConfigForm" :rules="rules" label-width="90px">
        <el-form-item label="api地址:" prop="api">
          <el-input v-model="sysConfigForm.api" />
        </el-form-item>
        <el-form-item label="minio地址:" prop="minioApi">
          <el-input v-model="sysConfigForm.minioApi" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeDialog">取消</el-button>
          <el-button type="primary" @click="handleSubmit">提交</el-button>
        </div>
      </template>
    </el-dialog>
</template>

<script setup lang="ts">
 import { SystemService } from '@/api/systemApi'
import { FormRules } from 'element-plus'
import { useI18n } from 'vue-i18n'
const { t } = useI18n()
import { ApiStatus } from '@/utils/http/status'
import { useSettingStore } from '@/store/modules/setting'
import { useUserStore } from '@/store/modules/user'
import { EnvConfig } from '@/config/types'
  onMounted(()=>{
    window.addEventListener('resize', handleResize)
    handleResize() // 初始化响应式布局
  })
  onBeforeMount(() => {
    window.removeEventListener('resize', handleResize)
  })

  const emit = defineEmits(["setComplate"])

  const rules = computed<FormRules>(() => ({
    api:     [{ required: true, message: "请输入api地址", trigger: 'blur' }],
    minioApi:     [{ required: true, message: "请输入minio api地址", trigger: 'blur' }],
  }))
  
  const settingStore = useSettingStore()


  const dialogVisible = ref(false)
  const sysConfigForm  = ref<EnvConfig>({
    api: configStore.getApiUrl() , //  string  // 后台api地址前缀
    minioApi: configStore.getMinioApi(), //  string  // minio api地址
    version: configStore.getVersion(), //  string  // 版本号
    baseUrl:  configStore.getBaseUrl(), //  string  // 网站地址前缀
    lockEncryptKey: configStore.getLockEncryptKey() , //  string // 锁屏加密秘钥
    dropConsole: configStore.getDropConsole() , //  boolean // 是否删除console信息
    openRouteInfo: configStore.getOpenRouteInfo(), //  boolean // 是否打开路由信息
  });
  const dialogWidth = ref("30%")
  const handleResize = () => {
      // 根据屏幕宽度调整对话框宽度
    const windowWidth = window.innerWidth;
    if (windowWidth < 768) {
      dialogWidth.value = "90%"
    } else if(windowWidth > 768 && windowWidth < 1000) {
      dialogWidth.value = "70%"
    } else if(windowWidth > 1000 && windowWidth < 1440) {
      dialogWidth.value = "50%"
    } else {
      dialogWidth.value = "30%"
    }
  }

  const handleSubmit =async () => {
    const envData: EnvConfig = sysConfigForm.value;
    // console.log("envData:", envData)
    // settingStore.setEnvConfig(envData);
    let data = {"config": sysConfigForm.value}
    const resp = await SystemService.setSysConfig(data)
    if(resp.code === ApiStatus.success) {
      useUserStore().saveUserData()
      ElMessage.success("设置成功")
      emit("setComplate")
      closeDialog()
    }else{
        ElMessage.error(resp.message)
    }
  }
  const closeDialog = () => {
    dialogVisible.value = false ;
  }
  // 配置后端接口
  const visibleDialog = () => {
    dialogVisible.value = true;
  }


  //导出方法
defineExpose({
    visibleDialog
});

</script>