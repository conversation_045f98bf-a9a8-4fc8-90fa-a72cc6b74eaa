<template>
    <div class="map-mode-div">
        <div class="layout-container">
            <!-- 左侧栏 -->
            <div class="left-column" :style="{ width: leftWidth + 'px' }"  >
                <LeftInfo  ref="leftInfoRef"/>
            </div>
            
            <!-- 拖动条 -->
            <div class="drag-handle" @mousedown="handleMouseDown"></div>
            
            <!-- 右侧内容 -->
            <div class="right-column">
              <el-row class="row-75 h-full" >
                <el-col :span="leftSpan" class="h-full">
                  <component :is="leftComponent"  :mapModel="mapModel"  @modelChange="modelChange" />
                </el-col>
                <el-col :span="rightSpan" class="h-full">
                  <component :is="rightComponent" :mapModel="mapModel"  @modelChange="modelChange" />
                </el-col>
              </el-row>
                <el-row class="row-25">
                    <el-col :span="24"><BottomTable /></el-col>
                </el-row>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import CenterMap from './centerMap.vue';
import BottomTable from './bottomTable.vue';
import LeftInfo from './leftInfo.vue';
import VideoList from './videoList.vue';
import { MapModelEnum } from '@/enums/car';


const mapModel = ref<string>(MapModelEnum.Map)
const leftSpan = computed(() => mapModel.value === MapModelEnum.Map ? 18 : 14);
const rightSpan = computed(() => mapModel.value === MapModelEnum.Map ? 6 : 10);

const leftComponent = computed(() => mapModel.value === MapModelEnum.Map ? CenterMap : VideoList )
const rightComponent = computed(() =>  mapModel.value === MapModelEnum.Map ? VideoList : CenterMap)

const leftInfoRef = ref<InstanceType<typeof LeftInfo> | null>(null)
// 拖动状态
const isDragging = ref(false)
const startX = ref(0)
const startWidth = ref(230) // 初始宽度
const minWidth = ref(110)
const maxWidth = ref(window.innerWidth - 800 - 252)


const modelChange = (newModel:string) => {
  mapModel.value = newModel
}

// 计算左侧宽度（带边界限制）
const leftWidth = computed(() => {
  return Math.min(Math.max(startWidth.value, minWidth.value), maxWidth.value)
})

const handleMouseDown = (e: MouseEvent) => {
  isDragging.value = true
  startX.value = e.clientX
  startWidth.value = leftWidth.value

}

const handleMouseUp = () => {
  isDragging.value = false

}

const handleMouseMove = (e: MouseEvent) => {
  if (!isDragging.value) return
  const diff = e.clientX - startX.value
  startWidth.value += diff
  startX.value = e.clientX 
  nextTick(() => {
    leftInfoRef.value?.updateMaxWidth()
  })
}


// 清理事件监听
onMounted(() => {
    document.addEventListener('mousemove', handleMouseMove)
    document.addEventListener('mouseup', handleMouseUp)
})

onBeforeUnmount(() => {
  document.removeEventListener('mousemove', handleMouseMove)
  document.removeEventListener('mouseup', handleMouseUp)
})
</script>

<style lang="scss" scoped>


.map-mode-div {
  height: calc(100vh - 122px);
  overflow: hidden;
}

.layout-container {
  display: flex;
  height: 100%;
  position: relative;
}

.left-column {
    margin-right: 10px;
    flex-shrink: 0;

    background: var(--art-main-bg-color);
    overflow-y: auto;
}

.right-column {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-width: 800px;
}

.drag-handle {
  width: 1px;
  cursor: col-resize;
  background: #ddd;
  z-index: 100;
  &:hover {
    background: #409eff;
  }
}

/* 父组件样式调整 */
.row-75 {
  height: 75%;
  
  .el-row, .el-col { /* 强制继承高度 */
    height: 100%;
    min-height: 0; /* 修复 flex 容器收缩问题 */
  }
}
.row-70 { height: 70%; }
.row-30 { height: 30%; }
.row-25 { height: 25%; }


/* 响应式处理 */
@media (max-width: 1200px) {
  .left-column {
    display: none !important;
  }
  .drag-handle {
    display: none;
  }
  .right-column {
    width: 100% !important;
    padding-left: 0;
  }
}
</style>