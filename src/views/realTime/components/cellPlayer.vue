<template>
    <div class="player">
        <!-- player{{ props.title }} -->
        <el-empty
          v-if="item.videoUrl === ''"
          :image="EmptyVideo"
          description=" "
          :image-size="50"
          @click="selectOneVideo(item)"
        />
        <ArtVideoPlayer 
            v-else
            :playerId="getPlayID(item.id)"
            :videoUrl="item.videoUrl"
            :posterUrl="item.posterUrl"
            :autoplay="false"
            :volume="0.5"
            :playbackRates="[0.5, 1, 1.5, 2]"
            @click="selectOneVideo(item)"
        />
    </div>
</template>

<script setup lang="ts">
import EmptyVideo from "@imgs/empty/empty_video.png"

export type VideoItem = {
    id: number
    videoUrl: string
    posterUrl: string 
}


const props = defineProps({
    title: {
        type: Number,
        default: 0
    },
    item: {
        type: Object as PropType<VideoItem>,
        default: () => ({
            id: 0,
            videoUrl: "",
            posterUrl: ""
        }) // 默认空对象
    }
})

const item = ref(props.item || {})

const selectOneVideo = (item: VideoItem) => {

}

const getPlayID = (index: number) => {
    return "my_player_id--" + String(index)
}
</script>

<style scoped>
.player {
    background-color: black;
    height: 100%;
    border: 1px solid white;
    color: white;
    text-align: center;
}
</style>