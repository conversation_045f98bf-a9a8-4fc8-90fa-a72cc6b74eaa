<template>
    <div :class="menuType === 'left' || menuType === 'top-left' ? 'bottom-tables-left' : 'bottom-tables-top' " >
        <el-radio-group v-model="tabItem" class="btn-group" size="small" @change="tabChange">
            <el-radio-button :value="position">
                <i class="iconfont-sys iconsys-dangqianweizhi">定位列表</i>
            </el-radio-button>
            <el-radio-button :value="follow"> 
                <i class="iconfont-sys iconsys-shoucang">关注列表</i>
            </el-radio-button>
            <el-radio-button :value="carList">
                <!-- iconsys-xiaoche -->
                <i class="iconfont-sys iconsys-xiaoche">车辆列表</i>
            </el-radio-button>
            <el-radio-button :value="alarm">
                <i class="iconfont-sys iconsys-jingbao">报警列表</i> 
            </el-radio-button>
            <el-radio-button :value="sysLog">
                <i class="iconfont-sys iconsys-bianqian">系统日记</i> 
            </el-radio-button>
            <el-radio-button :value="clockIn">
                <i class="iconfont-sys iconsys-shuaka">刷卡列表</i> 
            </el-radio-button>
            <el-radio-button :value="mediaInfo">
                <i class="iconfont-sys iconsys-paizhao">多媒体信息</i> 
            </el-radio-button>
            <el-radio-button :value="stationReport">
                <i class="iconfont-sys iconsys-shangchuan1">报站列表</i> 
            </el-radio-button>
        </el-radio-group>
        <div :class="menuType === 'left' || menuType === 'top-left' ? 'bottom-tables-left' : 'bottom-tables-top' ">
            <!-- <MiniTable :data="dataList" :columns="columns" ref="tableRef"  ></MiniTable> -->
            <MyTable  :data="dataList" :columns="columns" ref="tableRef" ></MyTable>
        </div>
        
    </div>
</template>

<script setup lang="ts">
import MiniTable from "@/components/core/tables/MiniTable.vue";
import MyTable from "@/components/custom/tables/MyTable.vue";
import { PositionColumns, FollowColumns } from "./columns";
import { useSettingStore } from "@/store/modules/setting";
const settingStore = useSettingStore();

const menuType =  computed(() => settingStore.menuType);
console.log("menuType:", menuType.value)

const position = "position";
const follow = "follow";
const carList = "carList";
const alarm = "alarm";
const sysLog = "sysLog";
const clockIn = "clockIn";
const mediaInfo = "mediaInfo";
const stationReport = "stationReport";



const dataList = ref([]);
const tableRef = ref();
const columns = ref<any>(PositionColumns);
const tabItem = ref<string>("top")
const tabChange = (btn: any) => {
    console.log("btn:", btn, "tabItem:", tabItem.value)
    if (btn === position) {
        // tabItem.value = position;
        columns.value = PositionColumns;
    } else if (btn === follow) {
        columns.value = FollowColumns;
    }
}


</script>


<style lang="scss" scoped>
.btn-group {
    margin-top: 5px;
}

.bottom-tables-left {
    width: calc(100vw - 230px - 252px - 5px);
}


.bottom-tables-top {
    width: calc(100vw - 230px  - 5px);
}



</style>