<template>
    <div  class="container h-full">

      <!-- 1. 如果是地图模式 -->
      <div v-for="(item, index) in videoList" :key="index" :class="item.id === selected ? 'video-item-select' : 'video-item'"  v-if="mapModel === MapModelEnum.Map">
        <el-empty
          v-if="item.videoUrl === ''"
          :image="EmptyVideo"
          description=" "
          :image-size="50"
          @click="selectOneVideo(item)"
        />
        <ArtVideoPlayer 
            v-else
            :playerId="getPlayID(index)"
            :videoUrl="item.videoUrl"
            :posterUrl="item.posterUrl"
            :autoplay="false"
            :volume="0.5"
            :playbackRates="[0.5, 1, 1.5, 2]"
            @click="selectOneVideo(item)"
        />
      </div>

      <!-- 2. 如果是视频模式 -->
      <div class="cell" v-else>
        
          <div class="cell-tool">
              <div class="el-button-group">
                  <span @click="cellCount = 1"  title="1分屏">
                    <img src="@/assets/img/cellplayer/1_select.png" class="cell-icon" v-if="cellCount === 1" />
                    <img src="@/assets/img/cellplayer/1.png"  class="cell-icon" v-else />
                  </span>
                  <span @click="cellCount = 4"  title="4分屏">
                    <img src="@/assets/img/cellplayer/4_select.png" class="cell-icon" v-if="cellCount === 4" />
                    <img src="@/assets/img/cellplayer/4.png" class="cell-icon" v-else />
                  </span>
                  <span @click="cellCount = 6"  title="6分屏">
                    <img src="@/assets/img/cellplayer/6_select.png" class="cell-icon" v-if="cellCount === 6" />
                    <img src="@/assets/img/cellplayer/6.png" class="cell-icon" v-else />
                  </span>
                  <span @click="cellCount = 9"  title="9分屏">
                    <img src="@/assets/img/cellplayer/9_select.png" class="cell-icon" v-if="cellCount === 9" />
                    <img src="@/assets/img/cellplayer/9.png" class="cell-icon" v-else />
                  </span>
                  <span @click="cellCount = 16"  title="16分屏">
                    <img src="@/assets/img/cellplayer/16_select.png" class="cell-icon" v-if="cellCount === 16" />
                    <img src="@/assets/img/cellplayer/16.png" class="cell-icon" v-else />
                  </span>
              </div>
          </div>
          <div class="cell-player">
              <div :class="cellClass(i)" v-for="i in cellCount" :key="i">
                  <cellPalyer :title="i" :item="videoList[i]" v-if="cellCount != 6"></cellPalyer>
                  <cellPalyer :title="i" :item="videoList[i]" v-if="cellCount == 6 && i != 2 && i != 3"></cellPalyer>
                  <template v-if="cellCount == 6 && i == 2">
                      <div class="cell-player-6-2-cell">
                          <cellPalyer :title="i"  :item="videoList[i]"></cellPalyer>
                          <cellPalyer :title="(i+1)"  :item="videoList[i]"></cellPalyer>
                      </div>
                  </template>
              </div>
          </div>
        
      </div>
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { MapModelEnum } from "@/enums/car";
import lockImg from '@imgs/lock/lock_screen_1.png'
import EmptyVideo from "@imgs/empty/empty_video.png"
import cellPalyer from "./cellPlayer.vue"

const props = defineProps({
  mapModel: {
      type: String,
  },
})

const mapModel = ref(props.mapModel);
const selected = ref<number | null>(null)  // 选中的,初始为null 
const cellCount = ref(4);

const cellClass = (index: number) => {
      switch (cellCount.value) {
          case 1:
              return ['cell-player-1']
          case 4:
              return ['cell-player-4']
          case 6:
              if (index == 1)
                  return ['cell-player-6-1']
              if (index == 2)
                  return ['cell-player-6-2']
              if (index == 3)
                  return ['cell-player-6-none']
              return ['cell-player-6']
          case 9:
              return ['cell-player-9']
          case 16:
              return ['cell-player-16']
          default:
              break;
      }
}

const videoList = ref([
  {
    id:1,
    // videoUrl: "http://lf3-static.bytednsdoc.com/obj/eden-cn/nupenuvpxnuvo/xgplayer_doc/xgplayer-demo.mp4",
    videoUrl: "",
    posterUrl: lockImg,
  },  {
    id: 2,
    videoUrl: "",
    posterUrl: "",
  },  {
    id: 3,
    videoUrl: "",
    posterUrl: "",
  },  {
    id: 4,
    videoUrl: "",
    posterUrl: "",
  }
])


const selectOneVideo = (item: any) => {
  // 点击时切换选中状态
  selected.value = item.id === selected.value ? null : item.id
  console.log("selected:", selected.value)
}

const getPlayID =  (index: number) => {
  return "my_player_id--" + String(index)
}


watch(
  () => props.mapModel,
  (newValue) => {
    mapModel.value = newValue;
  }
)


</script>


<style lang="scss" scoped>
/* 子组件样式调整 */
.container {
  height: 100%; /* 确保继承父容器高度 */
  display: flex;
  margin-left: 1px;
  margin-right: 1px;
  flex-direction: column;
  .video-item-select {
    cursor: pointer;
    flex: 1;
    min-height: 0; /* 修复 flex 项溢出问题 */
    border: 1px solid var(--main-color) !important;
    margin-top: 1px;
    box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
    :deep(.art-video-player) {
      height: 100% !important; /* 强制播放器填满容器 */
    }
  }

  .video-item {
    cursor: pointer;
    flex: 1;
    min-height: 0; /* 修复 flex 项溢出问题 */
    border: 1px dashed var(--el-border-color);
    margin-top: 1px;
    // background: rgba(255,255,255,0.1);
    transition: all 0.3s ease;
    
    :deep(.art-video-player) {
      height: 100% !important; /* 强制播放器填满容器 */
    }
  }
}
/* 修复 Element Plus 栅格高度问题 */
.el-row {
  &.flex-grow {
    flex-grow: 1;
    min-height: 0;
  }

  .el-col {
    height: 100%;
  }
}
// .video-item {
//   flex: 1; /* 关键属性：平分剩余空间 */
//   min-height: 0; /* 修复内容超出容器的问题 */
//   object-fit: scale-down; /*; /* 保持视频比例 */
// }

// /* 确保播放器填充容器 */
// .video-item :deep(.art-video-player) {
//   width: 100%;
//   height: 100%;
//   object-fit: scale-down; /*; /* 保持视频比例 */

// }

.cell-icon {
  cursor: pointer;
  margin-left: 3px;
  width: 25px;
  height: 20px;
  font-size: 14px; /* 确保文字可显示 */
  color: #000;    /* 明确文字颜色 */
}

.cell-tool {
    // height: 40px;
    // line-height: 30px;
    // padding: 0 7px;
}
.cell-player {
    flex: 1;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
}
.cell-player-4 {
    width: 50%;
    height: 50% !important;
    box-sizing: border-box;
}
.cell-player-1 {
    width: 100%;
    box-sizing: border-box;
}
.cell-player-6-1 {
    width: 66.66%;
    height: 66.66% !important;
    box-sizing: border-box;
}
.cell-player-6-2 {
    width: 33.33%;
    height: 66.66% !important;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
}
.cell-player-6-none {
    display: none;
}
.cell-player-6-2-cell {
    width: 100%;
    height: 50% !important;
    box-sizing: border-box;
}
.cell-player-6 {
    width: 33.33%;
    height: 33.33% !important;
    box-sizing: border-box;
}
.cell-player-9 {
    width: 33.33%;
    height: 33.33% !important;
    box-sizing: border-box;
}
.cell-player-16 {
    width: 25%;
    height: 25% !important;
    box-sizing: border-box;
}
.cell {
    display: flex;
    flex-direction: column;
    height: 100%;
}
</style>