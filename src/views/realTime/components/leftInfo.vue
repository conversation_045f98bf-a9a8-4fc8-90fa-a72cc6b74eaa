<template>
    <div  class="left-div" ref="containerRef">
        <!-- 顶部状态 -->
        <!-- <div class="header">
            CM ({{ activeIndex }}/{{ total }})
        </div> -->

        <!-- 列表主体 -->
        <el-tree
            ref="treeRef"

            :data="data"
            show-checkbox
            default-expand-all
            node-key="id"
            highlight-current
            :props="defaultProps"
        >
            <template v-slot:default="{ node }">
                <element-tree-line
                    :node="node"
                    :showLabelLine="node.data.showLabelLine" 
                >
                    <!-- 自定义label的slot -->
                    <template v-slot:node-label>
                        <span style="font-size: 12px"  @click="handleClick(node)"  :style="getIconStyle(node.data.online)" v-if="node.data.nodeType === 'car'">
                            <i class="iconfont-sys iconsys-qiche" :style="getIconStyle(node.data.online)"></i>
                            
                            <el-text :style="{maxWidth: maxWidth}" size="small"  truncated>
                                {{ node.label }}
                            </el-text>
                        </span>
                        <span style="font-size: 12px" :style="getIconStyle(node.data.online)"  v-else>
                            <i class="iconfont-sys iconsys-chengshi_3" :style="getIconStyle(node.data.online)"></i>
                            <el-text :style="{maxWidth: maxWidth}" size="small"  truncated>
                                {{ node.label }}
                            </el-text>
                        </span>
                    </template>
                    <!-- 在右边追加内容的slot -->
                    <template v-slot:after-node-label v-if="node.data.nodeType === 'car'">
                        <el-dropdown  placement="bottom-start" trigger="click">
                            <span class="el-dropdown-link">
                                <i class="iconfont-sys iconsys-gengduo  right-btn-class"></i>
                            </span>
                            <template #dropdown>
                                <el-dropdown-menu>
                                    <el-dropdown-item disabled>打开视频</el-dropdown-item>
                                    <el-dropdown-item disabled>对讲</el-dropdown-item>
                                    <el-dropdown-item disabled>监听</el-dropdown-item>
                                    <el-dropdown-item disabled>抓拍</el-dropdown-item>
                                    <el-dropdown-item divided>数据透传</el-dropdown-item>
                                    <el-dropdown-item>文本下发</el-dropdown-item>
                                    <el-dropdown-item>终端属性查询</el-dropdown-item>
                                    <el-dropdown-item divided>修改-车辆信息</el-dropdown-item>
                                    <el-dropdown-item divided disabled>设备重启</el-dropdown-item>
                                </el-dropdown-menu>
                            </template>
                        </el-dropdown>
                    </template>
                </element-tree-line>
            </template>
        </el-tree>

        
        <div class="device-table" > 
            <el-divider></el-divider>
            <el-descriptions title="设备信息"  :column="1" size="small" border>
                <el-descriptions-item label="设备号">{{ deviceInfo.plateNumber }}</el-descriptions-item>
                <el-descriptions-item label="版本号">{{ deviceInfo.version }}</el-descriptions-item>
                <el-descriptions-item label="上线时间">{{ deviceInfo.onlineTime }}</el-descriptions-item>
                <el-descriptions-item label="离线时间">{{ deviceInfo.offlineTime }}</el-descriptions-item>
                <el-descriptions-item label="协议类型">{{ deviceInfo.protocolType }}</el-descriptions-item>
            </el-descriptions>
            
        </div>
    </div>
</template>

<script setup lang="ts">
import type Node from 'element-plus/es/components/tree/src/model/node'
import { getElementLabelLine } from 'element-tree-line';
import 'element-tree-line/dist/style.css';


const ElementTreeLine = getElementLabelLine(h);
const activeIndex = ref(2)
const total = ref(3)
const maxWidth = ref('80px');


// const tableData = ref()
const deviceInfo = reactive({
    plateNumber: '80007003340',
    version: 'S51A_1.01.05_AEF25_1.12',
    onlineTime: '2025-04-21 14:45.34',
    offlineTime: '2025-04-21 14:43.23',
    protocolType: 'JT2013'
});
// 原有引入保持不变...
const containerRef = ref<HTMLElement | null>(null) // 容器ref
const leftDivWidth = ref(300);
console.log("leftDivWidth:", leftDivWidth.value)

// 新增方法：计算并更新最大宽度
const updateMaxWidth = () => {
  if (containerRef.value) {
    const containerWidth = containerRef.value.clientWidth
    leftDivWidth.value = containerRef.value.clientWidth;
    console.log("left div width:", leftDivWidth.value)
    maxWidth.value = `${containerWidth * 0.8}px` // 计算80%宽度
  }
}

interface Tree {
  id: number
  plateNumber: string 
  label: string
  children?: Tree[]
  showLabelLine?:boolean
  nodeType: string
  online: boolean
}
const defaultProps = {
  children: 'children',
  plateNumber: "plateNumber",
  label: 'label',
  showLabelLine: "showLabelLine",
  nodeType: "nodeType",
  online: "online",
}

const handleClick = (ndata: Node) => {
    console.log(ndata);
    deviceInfo.plateNumber = ndata.data.plateNumber;
}


const data: Tree[] = [
  {
    
    id: 1,
    label: 'CM (2/3)',
    plateNumber: "",
    showLabelLine: false,
    nodeType: "org",
    online: true,
    children: [
      {
        id: 4,
        plateNumber: "80007003273", 
        label: '80007003273 (ACC)',
        showLabelLine: true,
        nodeType: "car",
        online: true,
      },
      {
        id: 5,
        label: '80007003340 (ACC)',
        plateNumber: "80007003340",
        showLabelLine: true,
        nodeType: "car",
        online: true,
      },
      {
        id: 6,
        label: '80001003340 (ACC)',
        plateNumber: "80001003340",
        showLabelLine: true,
        nodeType: "car",
        online: false,
      },
    ],
  },

  
]




const getIconStyle = (isonline: boolean) => {
    return  isonline? { color: '#19be6b' } : { color: 'var(--art-text-gray-700)' }
}



// 生命周期钩子
onMounted(() => {
  nextTick(() => { // 等待DOM渲染完成
    updateMaxWidth()
    window.addEventListener('resize', updateMaxWidth) // 监听窗口变化
  })
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', updateMaxWidth) // 清除监听
})

defineExpose({
    updateMaxWidth,
})
</script>


<style lang="scss" scoped>



// 展开图标
:deep(.el-tree-node__content > .el-tree-node__expand-icon) {
  width: 14px;
}
.el-tree :deep(.el-tree-node__expand-icon.expanded) {
  -webkit-transform: rotate(0deg);
  transform: rotate(0deg);
}
//有子节点 且未展开
.el-tree :deep(.el-tree-node__expand-icon:before) {
  background: url("@/assets/img/eltree/open.png") no-repeat 0;
  content: "";
  display: block;
  width: 16px;
  height: 16px;
  font-size: 16px;
  background-size: 16px;
}
:deep(.el-icon svg) {
  display: none;
}
//有子节点 且已展开
.el-tree :deep(.el-tree-node__expand-icon.expanded:before) {
  background: url("@/assets/img/eltree/close.png") no-repeat 0;
  content: "";
  display: block;
  width: 16px;
  height: 16px;
  font-size: 16px;
  background-size: 16px;
}
//没有子节点
.el-tree :deep(.el-tree-node__expand-icon.is-leaf::before) {
  background: none;
  content: "";
  display: block;
  width: 16px;
  height: 16px;
  font-size: 16px;
  background-size: 16px;
}


.right-btn-class:hover {
   cursor: pointer; 
   background-color: #6d93ff;
   color: #ffffff;
   align-items: center; /* 垂直居中 */
}


.left-div {
    width: 100%;
    height: 100%;
    position: relative; /* 为绝对定位子元素建立定位上下文 */
    overflow: hidden; /* 隐藏溢出内容 */
}


.device-table {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0; /* 添加右侧约束 */
    width: 100%; /* 设置宽度与父容器一致 */
    height: 250px;
    // background: white;
    overflow-x: hidden; /* 横向溢出隐藏 */
    overflow-y: auto; /* 纵向自动滚动 */
}

// 设备信息表格样式
:deep(.el-descriptions) {
  .el-descriptions__body {
    // 固定表格布局
    .el-descriptions__table {
      table-layout: fixed;
      width: 100%;
    }

    // 内容单元格
    .el-descriptions__content {
      display: block;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      min-width: 0; // 关键：允许内容区域收缩
    }

    // 标签单元格
    .el-descriptions__label {
      flex: 0 0 auto; // 禁止标签收缩
      &.is-bordered-label {
        background: var(--el-fill-color-light);
      }
    }
  }
}

.device-table {
  // 确保表格有明确宽度约束
  :deep(.el-descriptions) {
    width: 100%;
    margin: 0 auto;
  }
}
</style>