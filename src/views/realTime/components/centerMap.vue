<template>
  <div class="map-container">
    <ol-map
      :loadTilesWhileAnimating="true"
      :loadTilesWhileInteracting="true"
      :default-controls="false"
      style="width: 100%; height: 100%"
      ref="mapRef"
    >
      <ol-view
        ref="view"
        :center="center"
        :zoom="zoom"
        :projection="projection"
      />

      <ol-tile-layer>
        <ol-source-tianditu
          layerType="img"
          projection="EPSG:4326"
          :tk="token"
          :hidpi="true"
        ></ol-source-tianditu>
      </ol-tile-layer>

      <ol-tile-layer>
        <ol-source-tianditu
          :isLabel="true"
          layerType="img"
          projection="EPSG:4326"
          :tk="token"
          :hidpi="true"
        ></ol-source-tianditu>
      </ol-tile-layer>

      <ol-vector-layer>
          <ol-source-vector>
              <ol-feature ref="animationPath">
                  <ol-geom-line-string :coordinates="data"></ol-geom-line-string>
                  <ol-style-flowline
                      color="rgba(228, 147, 87, 1)"
                      color2="rgba(228, 64, 0, 1)"
                      :width="2"
                      :width2="2"
                      :arrow="1"
                  />
              </ol-feature>
          <ol-animation-path
            ref="pathRef"
            v-if="animationPath"
            :path="animationPath?.feature"
            :duration="4000"
            :repeat="0"
            :speed="0.0005"
          >
              <ol-feature>
                  <ol-geom-point :coordinates="data[0]"></ol-geom-point>
                  <ol-style :zIndex="10">
                      <ol-style-icon :src="iconSrc" :width="30" :height="30" :rotation="angle">
                      </ol-style-icon>
                  </ol-style>
              </ol-feature>
          </ol-animation-path>
        </ol-source-vector>
      </ol-vector-layer>

    </ol-map>
    
    <!-- 新增控制按钮 -->
    <!-- <button class="map-control-button">
      <span class="button-icon">▶</span>
      <span class="button-text">动画控制</span>
    </button> -->
    <el-radio-group v-model="mapModel" size="small" @change="mapModelChange"  class="map-control-button">
      <el-radio-button :value="MapModelEnum.Map">
        <i class="iconfont-sys iconsys-dangqianweizhi">地图模式</i>
      </el-radio-button>
      <el-radio-button :value="MapModelEnum.Video">
        <i class="iconfont-sys iconsys-shipin">视频模式</i>
      </el-radio-button>
    </el-radio-group>

  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import iconSrc from '@/assets/img/car/view.png';
import type AnimationPath from "ol-ext/featureanimation/Path";
import { MapModelEnum } from "@/enums/car";

const props = defineProps({
  mapModel: {
      type: String,
      required: true,
  },
})

const mapRef = ref()
const animationPath = ref<{ feature: AnimationPath } | null>(null);

const token = "b216d7c2423da4d62aa807c2a4a96382"


const mapModel = ref(props.mapModel);
console.log("map model:", mapModel.value)

const center = ref([115, 31]);
const projection = ref("EPSG:4326");
const zoom = ref(6);
const angle = ref(0);

const emit = defineEmits(["modelChange"])



const mapModelChange = () => {
  console.log("mapmodel.value", mapModel.value)
  
  emit("modelChange", mapModel.value)
}

const data = ref([
  [110, 30],
  [110.2, 30],
  [110.4, 30.2],
  [110.8, 30.4],
  [111, 31],
  [111.3, 31],
  [111.6, 31],
  [111.9, 31],
  [112, 31],
  [112.3, 31],
  [112.5, 31],
  [112.8, 31],
  [113, 31],
  [114, 31],
  [115.3, 32],
  [115.5, 32],
  [115.8, 31.8],
  [116, 31.4],
  [116.2, 31.1],
  [116.5, 30.5],
  [115, 30.2],
  [114, 29.8],
  [113, 29.6],
  [112, 29.4],
  [111, 30.2],
  [110, 30.4],
  [109, 30.6],
  [108, 31]
]);


watch(
  () => props.mapModel,
  (newValue) => {
    mapModel.value = newValue;
  }
)

</script>

<style scoped>

/* 双保险：同时使用CSS隐藏 */
:deep(.ol-control.ol-zoom) {
  visibility: hidden !important;
}

.map-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.map-control-button {
  position: absolute;
  top: 2px;
  left: 2px;
  z-index: 1000;
  display: flex;
  align-items: center;
  /* padding: 8px 12px; */
  /* background: rgba(255, 255, 255, 0.9); */
  /* border: 1px solid #ccc; */
  border-radius: 4px;
  /* box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15); */
  cursor: pointer;
  transition: all 0.2s ease;
}


</style>