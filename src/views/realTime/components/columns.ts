

import moment from "moment";

// export const PositionColumns = [
//     {
//         label: "车牌号",
//         prop: "contractor_code",
//         width: 110,
//         display: true,
//     },
//     {
//         label: "承建商",
//         prop: "contractor_name",
//         display: true,
//     },
//     {
//         label: "承建商编码",
//         prop: "contractor_code",
//         width: 110,
//         display: true,
//     },
//     {
//         label: "承建商",
//         prop: "contractor_name",
//         display: true,
//     },
//     {
//         label: "承建商编码",
//         prop: "contractor_code",
//         width: 110,
//         display: true,
//     },
//     {
//         label: "承建商",
//         prop: "contractor_name",
//         display: true,
//     },
//     {
//         label: "承建商编码",
//         prop: "contractor_code",
//         width: 110,
//         display: true,
//     },
//     {
//         label: "承建商",
//         prop: "contractor_name",
//         display: true,
//     },
//     {
//         label: "承建商编码",
//         prop: "contractor_code",
//         width: 110,
//         display: true,
//     },
//     {
//         label: "承建商",
//         prop: "contractor_name",
//         display: true,
//     },
//     {
//         label: "承建商编码",
//         prop: "contractor_code",
//         width: 110,
//         display: true,
//     },
//     {
//         label: "承建商",
//         prop: "contractor_name",
//         display: true,
//     },
//     {
//         label: "RTU制造商",
//         prop: "manufacturer",
//         width: 110,
//         display: true,
//     },
//     {
//         label: "RTU型号",
//         prop: "model",
//         width: 100,
//         display: true,
//     },
//     {
//         label: "检测类型",
//         prop: "elements",
//         required: true,
//         width: 90,
//         type: "enum",
//         display: true,
//         multiple: true,  // 多选
//         dict: [
//             // 对应后端的表 mapTemplateDataPointType
//             {label: "降水量", value: "1"}, { label: "图像", value: "2"},
//             { label: "工况", value: "3"},{ label: "渗流压力", value: "4"},
//             { label: "渗流量", value: "5"},{ label: "表面水平位移", value: "6"},
//             { label: "表面垂直位移", value: "7"},{ label: "水位整编", value: "8"},
//             { label: "降水整编", value: "9"},{ label: "白蚁监测", value: "10"},
//             { label: "气象五要素", value: "11"},{ label: "坡面径流", value: "12"},
//             { label: "降水分配", value: "13"},{ label: "土壤墒情", value: "14"},
//         ],
//     },
    
//     {
//         label: "市",
//         prop: "city",
//         display: true,
//     },
//     {
//         label: "区",
//         prop: "district",
//         display: true,
//     },
 
//     {
//         label: "创建时间",
//         prop: "created_at",
//         formatter: (row:any) => moment(row.created_at).format("YYYY-MM-DD HH:mm:ss"),
//         width: 150,
//     },
//     {
//         label: "操作",
//         prop: "operation",
//         rowSlot: true,
//         fixed: "right",
//         width: 100,
//     }
// ]
export const PositionColumns = [
    {
        label: "车牌号",
        prop: "plateNumber",
        width: 100,
        required: true,
        display: true,
    },
    {
        label: "设备号",
        prop: "deviceNumber",
        width: 100,
        required: true,
        display: true,
    },
    {
        label: "组织",
        prop: "org",
        width: 80,
        display: true,
    }, {
        label: "ACC",
        prop: "acc",
        width: 80,
        display: true,
    }, {
        label: "定位时间",
        prop: "position_time",
        width: 130,
        display: true,
    }, {
        label: "接收时间",
        prop: "receive_time",
        width: 130,
        display: true,
    },{
        label: "速度(KM/H)",
        prop: "speed",
        width: 100,
        display: true,
    }, {
        label: "停车时长",
        prop: "stop_time",
        width: 100,
        display: true,
    }, {
        label: "今日里程(KM)",
        prop: "day_km",
        width: 120,
        display: true,
    }, {
        label: "平台里程(KM)",
        prop: "platform_km",
        width: 120,
        display: true,
    }, {
        label: "网络信号",
        prop: "network_signal",
        width: 100,
        display: true,
    }, {
        label: "卫星个数",
        prop: "satellites",
        width: 100,
        display: true,
    } , {
        label: "报警信息",
        prop: "alarm_info",
        width: 100,
        display: true,
    }, {
        label: "驾驶员",
        prop: "driver",
        width: 100,
        display: true,
    }, {
        label: "油量",
        prop: "amount_oil",
        width: 100,
        display: true,
    }, {
        label: "经纬度",
        prop: "lat_lng",
        width: 120,
        display: true,
    }, {
        label: "位置信息",
        prop: "address",
        width: 150,
        display: true,
    }, {
        label: "操作",
        prop: "operation",
        rowSlot: true,
        fixed: "right",
        width: 100,
    }
]

export const FollowColumns = [
    {
        label: "车牌号",
        prop: "plateNumber",
        required: true,
        display: true,
    },
    {
        label: "设备号",
        prop: "deviceNumber",
        required: true,
        display: true,
    }
]