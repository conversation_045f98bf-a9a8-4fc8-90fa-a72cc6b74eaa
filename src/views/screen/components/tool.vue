<template>
    <div>
        <div id="cesiumContainer"></div>
        <div class="form">
            <el-form ref="form" :model="form" label-width="80px">
                <el-form-item>
                    <div class="title">模型姿态动态调整</div>
                </el-form-item>
                <el-form-item label="经度">
                    <el-input v-model.number="form.lon"></el-input>
                </el-form-item>
                <el-form-item label="纬度">
                    <el-input v-model.number="form.lat"></el-input>
                </el-form-item>
                <el-form-item label="高度">
                    <el-input v-model.number="form.height"></el-input>
                </el-form-item>
                <el-form-item label="缩放比例">
                    <el-slider v-model.number="form.scale" :min="1" :max="50"></el-slider>
                </el-form-item>
                <el-form-item label="平移-X">
                    <el-slider v-model.number="form.offsetX"></el-slider>
                </el-form-item>
                <el-form-item label="平移-Y">
                    <el-slider v-model.number="form.offsetY"></el-slider>
                </el-form-item>
                <el-form-item label="平移-Z">
                    <el-slider v-model.number="form.offsetZ"></el-slider>
                </el-form-item>
                <el-form-item label="航向角">
                    <el-slider v-model.number="form.heading" :min="0" :max="360"></el-slider>
                </el-form-item>
                <el-form-item label="俯仰角">
                    <el-slider v-model.number="form.pitch" :min="0" :max="360"></el-slider>
                </el-form-item>
                <el-form-item label="翻滚角">
                    <el-slider v-model.number="form.roll" :min="0" :max="360"></el-slider>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="locatePos">定位</el-button>
                </el-form-item>
            </el-form>
        </div>
    </div>
</template>

<script setup>
import { onMounted  } from 'vue';
let model;

const   form =  {
    lon: 110,
    lat: 30,
    height: 300,
    scale: 1,
    offsetX: 0,
    offsetY: 0,
    offsetZ: 0,
    heading: 0,
    pitch: 0,
    roll: 0,
}

onMounted(() => {
    initViewer();
    loadModel();
})

    // watch: {
    //     form: {
    //         handler: function(){
    //             this.changePos();
    //         },
    //         immediate: false,
    //         deep: true
    //     }
    // },

const initViewer = () => {
    let defaultRect = Cesium.Rectangle.fromDegrees(90, 20, 130, 50);
    Cesium.Camera.DEFAULT_VIEW_RECTANGLE = defaultRect;
    window.viewer = new Cesium.Viewer("cesiumContainer", {
        animation: false,
        shouldAnimate: true,
        baseLayerPicker: false,
        fullscreenButton: false,
        geocoder: false,
        homeButton: false,
        sceneModePicker: false,
        selectionIndicator: false,
        timeline: false,
        navigationHelpButton: false,
        infoBox: false,
        navigationInstructionsInitiallyVisible: false,
        imageryProvider: new Cesium.UrlTemplateImageryProvider({
            url: "https://webst01.is.autonavi.com/appmaptile?style=6&x={x}&y={y}&z={z}",
        }),
    });
    window.viewer.scene.debugShowFramesPerSecond = true;
}

// 加载模型
const loadModel = () => {
            let origin = Cesium.Cartesian3.fromDegrees(form.lon, form.lat, form.height);
            let modelMatrix = Cesium.Transforms.eastNorthUpToFixedFrame(origin);
            model = Cesium.Model.fromGltf({
                url: "data/Cesium_Air.glb",
                modelMatrix: modelMatrix,
            });
            model.readyPromise.then(function(model) {
                // Play all animations when the model is ready to render
                model.activeAnimations.addAll();
            });
            viewer.scene.primitives.add(model);
            locatePos();
        }
        
// 改变位置
const changePos = () => {
            // 确定矩阵变化的原点
            let origin = Cesium.Cartesian3.fromDegrees(form.lon, form.lat, form.height);
            let transform = Cesium.Transforms.eastNorthUpToFixedFrame(origin);
            // 缩放
            let scale = Cesium.Matrix4.fromScale(new Cesium.Cartesian3(form.scale, form.scale, form.scale));
            Cesium.Matrix4.multiply(transform, scale, transform);
            // 平移
            let translation = Cesium.Matrix4.fromTranslation(new Cesium.Cartesian3(form.offsetX, form.offsetY, form.offsetZ));
            Cesium.Matrix4.multiply(transform, translation, transform);
            // 旋转
            let heading = Cesium.Math.toRadians(form.heading);
            let pitch = Cesium.Math.toRadians(form.pitch);
            let roll = Cesium.Math.toRadians(form.roll);
            let hpr = new Cesium.HeadingPitchRoll(heading, pitch, roll);
            let matrix3 = Cesium.Matrix3.fromHeadingPitchRoll(hpr);
            let rotation = Cesium.Matrix4.fromRotationTranslation(matrix3);
            Cesium.Matrix4.multiply(transform, rotation, transform);
            // 设置
            model.modelMatrix = transform;
        }
        
// 定位
const locatePos = () => {
    viewer.camera.flyTo({
        destination: Cesium.Cartesian3.fromDegrees(form.lon, form.lat, form.height + 100),
    });
}


</script>

<style scoped>
#cesiumContainer {
    position: absolute;
    width: 100%;
    height: 100%;
}
.title{
    font-size: 18px;
    font-weight: bold;
}
.form {
    position: absolute;
    background-color: #ffffff;
    padding: 5px;
    top: 5px;
    left: 5px;
}
</style>