<template>
    <div class="map-box" id="cesiumContainer">
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import * as Cesium from 'cesium';


const cesium_token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiJhYThjODZiMy1jNTU0LTQyMWUtOTQ4NC1lMmE5YmExOWVhN2EiLCJpZCI6MjQ0ODc1LCJpYXQiOjE3Mjc1OTQ0NjJ9.KA-izbDCA9vLKfz3xuOoRbwKwKXNNrLJHG2g4H6U08U";

const positionLatLng = [108.04, 30.31] ; //[109.9522, 28.7350];




const gviewer = ref(null);

const init =async () => {
    Cesium.Ion.defaultAccessToken = cesium_token;
    Cesium.Camera.DEFAULT_VIEW_RECTANGLE = Cesium.Rectangle.fromDegrees(90, -20, 110, 90);

    gviewer.value = new Cesium.Viewer('cesiumContainer', {
      geocoder: false, // 是否显示位置查找工具（true表示是，false表示否）
      homeButton: false, // 是否显示首页位置工具
      sceneModePicker: false, //是否显示视角模式切换工具
      baseLayerPicker: false, //是杏显示默认图层选择工具
      navigationHelpButton: false, //是否显示导航帮助工具
      animation: false, //是杏显示动画工具
      timeline: false, //是否显示时间轴工具
      fullscreenButton: true, //是否显示全屏按钮工具
    })
    gviewer.value._cesiumWidget._creditContainer.style.display = "none"; //取消版权信息
    //
    await addWorldTerrainAsync(gviewer.value)
    defaultflyTo(); 



    //关闭双击事件
    gviewer.value.cesiumWidget.screenSpaceEventHandler.removeInputAction(
        Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK
    );
    // 倾斜视图 鼠标左键旋转
    gviewer.value.scene.screenSpaceCameraController.tiltEventTypes = [
        Cesium.CameraEventType.LEFT_DRAG,
    ];

    getPoint();

}


// 点击获取坐标
const getPoint = () => {
    // 获取当前场景的ScreenSpaceEventHandler
    var handler = gviewer.value.screenSpaceEventHandler;

    // 添加LEFT_CLICK事件处理器
    handler.setInputAction(function(click) {
        // 获取点击位置的屏幕坐标
        var cartesian = gviewer.value.camera.pickEllipsoid(click.position, gviewer.value.scene.globe.ellipsoid);
        
        if (Cesium.defined(cartesian)) {
            // 将笛卡尔坐标转换为地理坐标（经度、纬度、高度）
            var cartographic = Cesium.Cartographic.fromCartesian(cartesian);
            
            // 转换弧度到度数
            var longitudeString = Cesium.Math.toDegrees(cartographic.longitude).toFixed(4);
            var latitudeString = Cesium.Math.toDegrees(cartographic.latitude).toFixed(4);
            var heightString = cartographic.height.toFixed(2);

            console.log('Longitude: ' + longitudeString + ', Latitude: ' + latitudeString + ', Height: ' + heightString + ' meters');
        } else {
            console.log('Click position is outside the globe.');
        }
    }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
}

// 添加地形数据
const addWorldTerrainAsync = async (viewer) => {
  try {
    const terrainProvider = await Cesium.createWorldTerrainAsync({
      requestWaterMask: true,
      requestVertexNormals: true,
    });  

    viewer.terrainProvider = terrainProvider;
  } catch (error) {
    console.log(`Failed to add world imagery: ${error}`);
  }
};




const defaultflyTo = () => {
    gviewer.value.camera.flyTo({  
        destination :Cesium.Cartesian3.fromDegrees(...positionLatLng, 1000.0), // 设置位置
        orientation: {    
            heading :Cesium.Math.toRadians(140.0), // 方向
            pitch :Cesium.Math.toRadians(-20.0),// 倾斜角度
            roll :0,   // 滚转角 (通常为0)
        },  
        duration: 10, // 设置飞行持续时间，默认会根据距离来计算
        // complete:function () {//TODO}, // 到达位置后执行的回调函数
        // cancle:function () {//TODO},   // 如果取消飞行则会调用此函数
        // pitchAdjustHeight:-90, // 如果摄像机飞越高于该值，则调整俯仰俯仰的俯仰角度，并将地球保持在视口中。
        // maximumHeight:5000, // 相机最大飞行高度
        // flyOverLongitude:100, // 如果到达目的地有2种方式，设置具体值后会强制选择方向飞过这个经度(这个，很好用)});
    })
}



onMounted(() => {
    init();
    // initTianDiMap();
});

</script>

<style scoped>
.map-box {
    height: 100%;
    width: 100%;
    margin: 0;
    padding: 0;
    display: block; /* 确保无布局冲突 */
}

.cesium-widget-credits{
    display: none !important;
}
</style>