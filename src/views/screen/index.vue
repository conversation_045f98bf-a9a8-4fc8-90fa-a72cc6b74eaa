<template>
    <div class="gis-div-class" ref="screenBgRef">
        <!-- <TiandituMap ref="TiandituMapRef"></TiandituMap> -->
        <!-- <UI ></UI> -->
        <Map3d ></Map3d>
        <div class="ui-class">
            <ProjectUI 
            :title="title"
            :menuList="menuList"
            :leftBtnList="leftBtnList" 
            :rightBtnList="rightBtnList"
            :cardContents="cardContents"
            @menuClick="menuClick"
            @btnClick="btnClick">
            </ProjectUI>
        </div>
    </div>
</template>


<script setup lang="ts">
import { ref, provide } from "vue";

import Map3d from "./components/map3d.vue";
import ProjectUI from "@/components/custom/ScreenUI/project/index.vue";
import { router } from "@/router";
import { ElLoading } from "element-plus";




// 
const cesium_token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiJhYThjODZiMy1jNTU0LTQyMWUtOTQ4NC1lMmE5YmExOWVhN2EiLCJpZCI6MjQ0ODc1LCJpYXQiOjE3Mjc1OTQ0NjJ9.KA-izbDCA9vLKfz3xuOoRbwKwKXNNrLJHG2g4H6U08U";

const screenBgRef = ref(null);
// 提供 screenBgRef 给所有子组件
provide('screenBgRef', screenBgRef);

const title = ref("数据大屏管理")


const leftBtnList = [
    { id: 1, position: "left",  name:"测站管理",  key:"station_manager" },
    { id: 2, position: "left",  name:"数据服务",  key:"data_service"},
]

const rightBtnList = [
    { id: 1, position:"right",  name: "系统设置", key:"system_config" },
    { id: 2, position: "right", name: "设备管理", key:"device_manager"},
]

const menuList = ref([
            { id: 1, name: "实时监控", key: "menu-1", path: "/realTime/index" },
            { id: 2, name: "轨迹回放", key: "menu-2", path: "/playback/index"},
            { id: 3, name: "文件管理", key: "menu-3", path: "/fileBrowser"},
            { id: 4, name: "用户管理", key: "menu-4", path: "/system/account" },
            { id: 5, name: "公司管理", key: "menu-5", path: "/system/company" },
            { id: 6, name: "个人中心", key: "menu-6", path: "/system/user" },
        ])

const cardContents: any[] = []

export type btnType = {
    id: number,
    position: string, 
    name: string ,
    key: string,
    path: string, 
}

const btnClick = (item: btnType) =>{
    console.log("click item:", item)
    if (item.key === "home") {
        // 首页
        router.push("/")
    } else if (item.key === "system_config") {
        router.push("/system/config")
    }
}

const menuClick = (item: btnType) => {
    if(item.path) {
        router.push(item.path)
    }
}




</script>


<style scoped>

.gis-div-class {
    position: relative; /* 确保子元素可以相对于此容器进行绝对定位 */
    width: 100%;
    height: 100vh;
}


/* 设置 TiandituMap 的尺寸和位置 */
.TiandituMap {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}


.ui-class {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none; /* 让点击事件通过 */
}

</style>