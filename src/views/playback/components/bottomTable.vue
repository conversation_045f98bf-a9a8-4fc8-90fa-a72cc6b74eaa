<template>
    <div>
        <MyTable  :data="dataList" :columns="columns" ref="tableRef" row-key="id"></MyTable>
    </div>
</template>

<script setup lang="ts">
import { HistoryInfo, PositionInfo } from '@/types/jt808';
import MyTable from '@/components/custom/tables/MyTable.vue';
import moment from 'moment';

  
const tableRef = ref<InstanceType<typeof MyTable>>()
const dataList = ref<HistoryInfo[]>([])

// altitude: number  // 海拔
// direction: number // 方向
// gpstime: string // 定位时间
// lat: number // 纬度
// lng: number // 经度
// speed: number // 速度
const columns = ref([
    {
        label: "序号",
        width: 80,
        display: true,
        type: "index",
        index: (index: number) => index + 1
    },
    {
        label: "定位时间",
        prop: "gpstime",
        display: true,
        formatter: (row: HistoryInfo) => moment(row.position?.gpstime, 'YYYYMMDDHHmmss').format("YYYY-MM-DD HH:mm:ss"),
    },
    {
        label: "海拔(m)",
        prop: "altitude",
        width: 100,
        display: true,
        formatter: (row: HistoryInfo) => row.position?.speed,
    },
    {
        label: "方向",
        prop: "direction",
        width: 100,
        display: true,
        formatter: (row: any) => {
            const direction = row.position?.direction;
            if (direction >= 0 && direction < 45) return '北';
            if (direction >= 45 && direction < 90) return '东北';
            if (direction >= 90 && direction < 135) return '东';
            if (direction >= 135 && direction < 180) return '东南';
            if (direction >= 180 && direction < 225) return '南';
            if (direction >= 225 && direction < 270) return '西南';
            if (direction >= 270 && direction < 315) return '西';
            if (direction >= 315 && direction <= 360) return '西北';
            return '--';
        }
    },
    {
        label: "纬度",
        prop: "lat",
        width: 120,
        display: true,
        formatter: (row: HistoryInfo) => row.position?.lat,
    },
    {
        label: "经度",
        prop: "lng",
        width: 120,
        display: true,
        formatter: (row: HistoryInfo) => row.position?.lng,
    },
    {
        label: "位置",
        prop: "result",
        display: true,
        minWidth: 200,
        formatter: (row: HistoryInfo) => row.position?.result,
    },

    {
        label: "时速(km/h)",
        prop: "speed",
        width: 100,
        display: true,
        formatter: (row: HistoryInfo) => row.position?.speed,
    }
])


const refresh  = (infoList: HistoryInfo[]) => {
    dataList.value = infoList
}

defineExpose({
    refresh,
})
</script>