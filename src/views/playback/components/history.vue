<template>
    <div class="map-mode-div">
        <el-row :gutter="20" class="full-height-row">
            <el-col :xs="24" :sm="10" :md="10" :lg="6" :xl="4" class="full-height-col">
                <LeftInfo @selectDeviceID="selectDeviceID" @selectStartEnd="selectStartEnd"></LeftInfo>
            </el-col>
            <el-col :xs="24" :sm="14" :md="14" :lg="18" :xl="20" class="full-height-col">
                <!-- 右侧 -->
                <el-row :gutter="20" class="row-70">
                    <!-- 上面地图和摄像头 -->
                    <el-col :span="24">
                        <CenterMap v-loading="loading" ref="centerMapRef"></CenterMap>
                    </el-col>
                </el-row>   
                <el-row :gutter="20" class="row-30">
                    <!-- 下面表格 --> 
                    <el-col :span="24">
                        <BottomTable ref="bottomTableRef"></BottomTable>
                    </el-col>
                </el-row>
            </el-col>
        </el-row>
    </div>
</template>

<script setup lang="ts">
import CenterMap from './centerMap.vue';
import BottomTable from './bottomTable.vue';
import LeftInfo from './leftInfo.vue';
import { historyService } from "@/api/jt808/history";
import { ApiStatus } from "@/utils/http/status";
import { HistoryInfo, PositionInfo } from "@/types/jt808";

const loading = ref(false)
const bottomTableRef = ref<InstanceType<typeof BottomTable>>()
const centerMapRef  = ref<InstanceType<typeof CenterMap>>()


// const positionList = ref<HistoryInfo[]>([])

// 定义API响应类型
type ApiResponse = {
  code: number
  message: string
  payload: {
    name?: string
    device_id?: string
    list?: HistoryInfo[]
    total?: number
  }
}

// 定义轨迹信息类型，与 centerMap.vue 保持一致
type TrackInfo = {
  name: string
  device_id: string
  list: HistoryInfo[]
}

const positionList = ref<TrackInfo>({ name: '', device_id: '', list: []  })


const params = ref({
    device_id: "",
    device_type_id: "",
    start: 0,
    end: 0,
})

const selectStartEnd  = async (start: number, end: number )=> {
    params.value.start = start
    params.value.end = end
    await initHistoryPositon()
    nextTick(() => {
        bottomTableRef.value?.refresh(positionList.value.list)
        centerMapRef.value?.refresh(positionList.value)
    })
       
}

const selectDeviceID = async (dev_id: string, device_type_id: string )=> {
    console.log(dev_id)
    params.value.device_id = dev_id
    params.value.device_type_id = device_type_id
} 

const initHistoryPositon = async () => {
    console.log("init history position")
    if (params.value.device_id === "" || params.value.device_type_id === "") {
        return 
    }
    // 重置为默认空对象，而不是空数组
    positionList.value = { name: '', device_id: params.value.device_id, list: [] }
    loading.value = true
    let resp = await historyService.findPositionList({
        ...params.value,
        identifier: "position",
    }) as unknown as ApiResponse
    if (resp.code === ApiStatus.success) {
        // 直接使用新格式的数据
        positionList.value = {
            name: resp.payload.name || params.value.device_id,
            device_id: params.value.device_id,
            list: resp.payload.list || []
        }
        loading.value = false
    } else {
        ElMessage.error(resp.message)
    }
}

console.log("history ...")
onMounted( async () => {
  await initHistoryPositon()
})

</script>

<style lang="scss" scoped>
.map-mode-div {
    width: 100%;
    height: calc(100vh - 122px); /* 或 calc(100vh - 其他固定高度) */
}

.full-height-row {
    height: 100%;
}

.full-height-col {
    height: 100%;
}

.row-70 {
    height: 70%;
}

.row-30 {
    height: 30%;
}
</style>