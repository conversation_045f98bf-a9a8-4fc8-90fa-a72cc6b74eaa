<template>
  <ol-map
  :loadTilesWhileAnimating="true"
  :loadTilesWhileInteracting="true"
  style="width: 100%; height: 100%"
  ref="mapRef"
>
  <ol-view
    ref="view"
    :center="center"
    :zoom="zoom"
    :projection="projection"
  />

  <ol-tile-layer>
    <ol-source-tianditu
      layerType="img"
      projection="EPSG:4326"
      :tk="token"
      :hidpi="true"
    ></ol-source-tianditu>
  </ol-tile-layer>

  <ol-tile-layer>
    <ol-source-tianditu
      :isLabel="true"
      layerType="img"
      projection="EPSG:4326"
      :tk="token"
      :hidpi="true"
    ></ol-source-tianditu>
  </ol-tile-layer>

  <ol-vector-layer>
      <ol-source-vector>
          <ol-feature ref="animationPath">
              <ol-geom-line-string :coordinates="data"></ol-geom-line-string>
              <ol-style-flowline
                  color="rgba(228, 147, 87, 1)"
                  color2="rgba(228, 64, 0, 1)"
                  :width="2"
                  :width2="2"
                  :arrow="1"
              />
          </ol-feature>
      <ol-animation-path
        ref="pathRef"
        v-if="animationPath"
        :path="animationPath?.feature"
        :duration="4000"
        :repeat="0"
        :speed="0.0005"
      >
          <ol-feature v-if="trace.length">
              <ol-geom-point :coordinates="trace[trace.length-1][1]"></ol-geom-point>
              <ol-style :zIndex="10">
                  <ol-style-icon 
                    :src="iconSrc" 
                    :width="30" 
                    :height="30" 
                    :rotation="angle"
                    @pointermove="showPopup"
                    @pointerout="hidePopup"
                    >
                  </ol-style-icon>
              </ol-style>
          </ol-feature>
      </ol-animation-path>
    </ol-source-vector>
  </ol-vector-layer>

      <!-- 弹窗 Overlay -->


  <ol-overlay
    v-if="showPopupFlag"
    :position="currentPosition"
    positioning="bottom-center"
    class="vehicle-popup"
    :stopEvent="false"
  >
    <div class="popup-container">
      <div class="popup-arrow"></div>
      <div class="popup-header">
        <div class="vehicle-icon">
          <img :src="iconSrc" alt="Vehicle" />
        </div>
        <div class="header-text">
          <h3>车辆编号: {{ vehicleInfo.device_id || '--' }}</h3>
          <div class="status-badge" :class="vehicleInfo.status">
            {{ vehicleInfo.status === 'moving' ? '行驶中' : '静止' }}
          </div>
        </div>
      </div>
      
      <div class="popup-content">
        <div class="info-row">
          <span class="info-label">速度:</span>
          <span class="info-value">{{ vehicleInfo.speed || '--' }} km/h</span>
        </div>
        <div class="info-row">
          <span class="info-label">方向:</span>
          <span class="info-value">{{ vehicleInfo.direction || '--' }}</span>
        </div>
        <div class="info-row">
          <span class="info-label">时间:</span>
          <span class="info-value">{{ vehicleInfo.gpstime || '--' }}</span>
        </div>
        <div class="info-row">
          <span class="info-label">位置:</span>
          <span class="info-value">{{ vehicleInfo.address || '--' }}</span>
        </div>
      </div>
      
      <!-- <div class="popup-footer"> -->
        <!-- <button class="popup-btn details" @click="showDetails">详情</button> -->
        <!-- <button class="popup-btn track" @click="showTrack">轨迹</button> -->
      <!-- </div> -->
    </div>
  </ol-overlay>

</ol-map>
</template>

<script setup lang="ts">
import { ref } from "vue";
import iconSrc from '@/assets/img/car/view.png';
import type AnimationPath from "ol-ext/featureanimation/Path";
import { HistoryInfo, PositionInfo } from "@/types/jt808";
import moment from "moment";

const pathRef = ref();
const mapRef = ref()
const animationPath = ref<{ feature: AnimationPath } | null>(null);

const token = "b216d7c2423da4d62aa807c2a4a96382"


const center = ref([115, 31]);
const projection = ref("EPSG:4326");
const zoom = ref(6);
const angle = ref(0);
const trace = ref<any>([]);

// 定义轨迹列表类型
type TrackInfo = {
  name: string
  device_id: string
  list: HistoryInfo[]
}

const positionList = ref<TrackInfo>({ name: '', device_id: '', list: [] });

const data = ref<any>([
  [110, 30],
]);

const vehicleInfo = ref({
  device_id: '京A12345',
  status: 'moving',
  speed: '62',
  direction: '东北方向',
  gpstime: '2023-11-15 14:30:22',
  address: '北京市海淀区中关村南大街5号',
});


// 弹窗相关状态
const showPopupFlag = ref(false);
const currentPosition = ref([0, 0]);

// 显示弹窗
const showPopup = (event: any) => {
  currentPosition.value = event.coordinate;
  showPopupFlag.value = true;
};

// 隐藏弹窗
const hidePopup = () => {
  showPopupFlag.value = false;
};

const directionNumToStr = (direction: number) => {
  if (direction >= 0 && direction < 45) return '北';
  if (direction >= 45 && direction < 90) return '东北';
  if (direction >= 90 && direction < 135) return '东';
  if (direction >= 135 && direction < 180) return '东南';
  if (direction >= 180 && direction < 225) return '南';
  if (direction >= 225 && direction < 270) return '西南';
  if (direction >= 270 && direction < 315) return '西';
  if (direction >= 315 && direction <= 360) return '西北';
  return '--';
}
const setVehicleInfo = (index: number) => {
try{
  // 使用list属性访问对应索引的定位信息
  let curInfo = positionList.value.list[index]
  vehicleInfo.value.device_id = positionList.value.device_id
    // status: curInfo.status,
  vehicleInfo.value.speed = String(curInfo.position?.speed)
  vehicleInfo.value.direction = directionNumToStr(curInfo.position?.direction);
  // 使用新数据结构中的时间信息
  if (curInfo.ts !== undefined && curInfo.ts !== null && curInfo.ts !== 0) {
    vehicleInfo.value.gpstime = moment.unix(curInfo.ts).format("YYYY-MM-DD HH:mm:ss")
  } else {
    vehicleInfo.value.gpstime = curInfo.position.gpstime || '--'
  }
  vehicleInfo.value.address = curInfo.position.result || '--'
} catch(e) {
  console.log(e)
}
}

const myrun = () => {
  showPopupFlag.value = true;
  let i = 0;
  const interval = setInterval(() => {
    if (data.value[i + 1]) {
      let arc = 0;
      if (
        (data.value[i + 1][0] - data.value[i][0] >= 0 &&
        data.value[i + 1][1] - data.value[i][1] >= 0) ||
        (data.value[i + 1][0] - data.value[i][0] < 0 &&
          data.value[i + 1][1] - data.value[i][1] > 0)
      ) {
        arc = Math.atan(
          (data.value[i + 1][0] - data.value[i][0]) / (data.value[i + 1][1] - data.value[i][1])
        );
      } else if (
        (data.value[i + 1][0] - data.value[i][0] > 0 &&
          data.value[i + 1][1] - data.value[i][1] < 0) ||
        (data.value[i + 1][0] - data.value[i][0] < 0 &&
          data.value[i + 1][1] - data.value[i][1] < 0)
      ) {
        arc =
          Math.PI +
          Math.atan(
            (data.value[i + 1][0] - data.value[i][0]) /
              (data.value[i + 1][1] - data.value[i][1])
          );
      }
      angle.value=arc
      trace.value.push([data.value[i], data.value[i + 1]]);
      center.value = data.value[i];
      currentPosition.value = data.value[i];
      setVehicleInfo(i);
      i++;
    } else {
      clearInterval(interval);
    }
  }, 200);
}

const refresh = async (trackData: { name: string; device_id: string; list: HistoryInfo[] }) => {
  data.value = []
  // 使用新的数据结构中的list属性
  data.value = trackData.list.map(pinfo => [pinfo.position.lng, pinfo.position.lat]);
  if(data.value.length <= 1) {
    return 
  }
  // 更新整个positionList对象
  positionList.value = {
    name: trackData.name,
    device_id: trackData.device_id,
    list: trackData.list
  }
  zoom.value = 13
  // 等待DOM更新
  await nextTick();
  center.value = data.value[0];
  myrun()
}

defineExpose({
  refresh,
})


</script>



<style lang="scss" scoped>
.vehicle-popup {
  transform: translateY(-15px);
  filter: drop-shadow(0 2px 10px rgba(0, 0, 0, 0.2));
}

.popup-container {
  width: 280px;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  font-family: 'Arial', sans-serif;
  margin-bottom: 30px;
}

.popup-arrow {
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-top: 10px solid white;
  margin-bottom: 30px;
}

.popup-header {
  display: flex;
  align-items: center;
  padding: 12px;
  background: linear-gradient(135deg, #4a6bdf, #3a56c8);
  color: white;

  .vehicle-icon {
    width: 40px;
    height: 40px;
    margin-right: 12px;
    
    img {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
  }

  .header-text {
    flex: 1;
    
    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
    }
  }

  .status-badge {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: bold;
    width: 38px;
    &.moving {
      background: #4caf50;
    }
    
    &.stopped {
      background: #f44336;
    }
  }
}

.popup-content {
  padding: 12px;

  .info-row {
    display: flex;
    margin-bottom: 8px;
    font-size: 14px;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .info-label {
      width: 60px;
      color: #666;
    }
    
    .info-value {
      flex: 1;
      color: #333;
      font-weight: 500;
    }
  }
}

.popup-footer {
  display: flex;
  padding: 8px;
  border-top: 1px solid #eee;

  .popup-btn {
    flex: 1;
    padding: 8px;
    margin: 0 4px;
    border: none;
    border-radius: 4px;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    
    &:hover {
      opacity: 0.9;
      transform: translateY(-1px);
    }
    
    &.details {
      background: #e0e0e0;
      color: #333;
    }
    
    &.track {
      background: #4a6bdf;
      color: white;
    }
  }
}
</style>
