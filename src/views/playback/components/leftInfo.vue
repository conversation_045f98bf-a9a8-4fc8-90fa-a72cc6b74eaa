<template>
    <div class="left-class">
        <el-radio-group v-model="backModel" size="small" @change="backModelChange"  class="map-control-button">
            <el-radio-button :value="PlayBackEnum.History">
                <i class="iconfont-sys iconsys-jiegou">轨迹回放</i>
            </el-radio-button>
            <el-radio-button :value="PlayBackEnum.Video">
                <i class="iconfont-sys iconsys-shipin">视频回放</i>
            </el-radio-button>
        </el-radio-group>
        
        <div v-if="backModel === PlayBackEnum.History">
            <div class="select-device">
                <el-select v-model="selectDeviceID" @change="changeDeviceID" placeholder="请选择车辆">
                    <el-option
                    v-for="item in deviceOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    />
                </el-select>
            </div>
            <div class="adaptive-calendar">
                <el-calendar>
                    <!-- <template #header="{ date }" >
                    空模板，不显示任何内容
                    <span @click="selectDate(date)">{{ date }}</span>
                    </template> -->
                    <template #date-cell="{ data }">
                        <div class="day-cell" @click="handleDateClick(data.day)">
                            <el-badge is-dot :offset="[3, -3]" class="custom-badge">
                                {{ data.day.split('-')[2] }}
                            </el-badge>
                        </div>
                    </template>
                </el-calendar>
                <div class="select-time">
                    <el-date-picker
                        v-model="selectTime"
                        type="datetimerange"
                        start-placeholder="开始时间"
                        end-placeholder="结束时间"
                        format="YYYY-MM-DD HH:mm:ss"
                        date-format="YYYY/MM/DD ddd"
                        time-format="A hh:mm:ss"
                    />
                </div>
                <div class="query-btn">
                    <el-button-group>
                        <!-- <el-button  size="default" type="primary"  @click="download" >下载</el-button> -->
                        <el-button  size="default" type="primary"  @click="searchQuery">查询</el-button>
                    </el-button-group>
                </div>
                <div class="info-div">
                    
                </div>
            </div>
        </div>
        <div v-else>
            <div class="text-h3">视频回放</div>
        </div>
    
    </div>
</template>

<script setup lang="ts">
import { PlayBackEnum } from "@/enums/car";
import { deviceService } from "@/api/device/device";
import { ApiStatus } from "@/utils/http/status";
import { Device } from "@/types/device";

const emit = defineEmits(["selectDeviceID", "selectStartEnd"])

const calDay = ref(new Date())
const backModel = ref(PlayBackEnum.History);

const today =  new Date();
const selectTime =  ref<[Date, Date]>([
    new Date(today.setHours(0, 0, 0, 0)), // 创建新的Date对象
    new Date(today.setHours(23, 59, 59, 999)) // 创建新的Date对象
])
// 在组件顶部定义 selectTime 的类型
// type DateTimeRange = [Date, Date] | null
// const selectTime: Ref<DateTimeRange> = ref(null)
// const selectTime: Ref<[Date, Date] | null> = ref(null);


// 测试默认的 00000000041010430267
const deviceTypeIDMap = ref<any>({"00000000041010430267": "00000000041010430267"})

const selectDeviceID = ref<string>("")
const deviceOptions = ref([
    { key: "1", label:"测试车辆", value: "00000000041010430267"}
])



const changeDeviceID = () => {
    console.log("change deviceid:", selectDeviceID.value)
    const curDeviceTypeID = deviceTypeIDMap.value[selectDeviceID.value]
    console.log("curDeviceTypeID:", curDeviceTypeID)
    emit("selectDeviceID", selectDeviceID.value, curDeviceTypeID)
}

const backModelChange = () => {

}

const download = () => {

}


const handleDateClick = (dateString: string) => {
    console.log("select date string", dateString)
 const date = new Date(dateString);
  
  // 设置当天时间范围
  const start = new Date(date);
  start.setHours(0, 0, 0, 0);
  
  const end = new Date(date);
  end.setHours(23, 59, 59, 999);
  
  selectTime.value = [start, end];
}

const searchQuery = () => {
    if (!selectTime.value || selectTime.value.length < 2) {
        ElMessage.error("请选择完整的时间范围");
        return;
    }
    
    const start: Date = selectTime.value![0]; // 非空断言
    const end: Date = selectTime.value![1];
       // 验证时间有效性
    if (start > end) {
        console.error("开始时间不能晚于结束时间");
        return;
    }
    // 获取秒级时间戳
    // const startTimestamp = Math.floor(start.getTime();
    // const endTimestamp = Math.floor(end.getTime() / 1000);
    emit("selectStartEnd", start.getTime(), end.getTime())
}

const params = ref({})
const initDeviceList = async () => {
    let resp = await deviceService.findDeviceList(params.value)
    console.log("device list:", resp)
    if (resp.code === ApiStatus.success ) {
        console.log("device list:", resp)
        resp.payload.list.map((item: Device) => {
            deviceOptions.value.push( {
                value:  item.device_id,
                key: item.device_id,
                label: item.name,
            })
            deviceTypeIDMap.value[item.device_id] = item.device_type_id;
        })
    }
}
onMounted(async () => {
    await initDeviceList()
    selectDeviceID.value = deviceOptions.value[0].value;
    changeDeviceID()
})

</script>

<style lang="scss" scoped>
.left-class {
    margin-left: 5px;
    width: 100%;
}

.map-control-button {
    width: 100%;
    
    /* 深度穿透修改 Element Plus 组件内部样式 */
    :deep(.el-radio-group) {
        width: 100%;
        display: flex;  // 启用弹性布局
    }
    
    :deep(.el-radio-button) {
        flex: 1;  // 让两个按钮均分宽度
        
        /* 按钮内部样式 */
        .el-radio-button__inner {
            width: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 8px 0;  // 适当调整内边距
        }
    }
}

.grid-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  width: 100%;
  height: 10px;
}

.select-device {
    margin-top: 10px;
}
.adaptive-calendar {
  height: 100px;
  min-height: 100px;
  max-height: 800px;
}

/* 深度选择器修改内部元素样式 */
:deep(.el-calendar-table .el-calendar-day) {
  height: 3em;
  text-align: left;
}

.day-cell {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 调整红点大小 */
.custom-badge :deep(.el-badge__content.is-fixed.is-dot) {
  width: 5px;
  height: 5px;
  border-radius: 50%;
}




// 
.select-time {
    width: 98%;
    height: 30px;
    // margin-top: 10px;
    
    :deep(.el-date-editor--datetimerange) {
        width: 95% !important;
        height: 30px;
        .el-range-input {
            width: 50%; // 调整输入框宽度比例
        }   
        .el-range-separator {
            width: 5%; // 调整分隔符宽度
        }
    }
}

.query-btn {
    width: 100%;
    margin-top: 5px;

    // 让按钮组占满宽度
    :deep(.el-button-group) {
        display: flex;
        width: 100%;
    }

    // 让两个按钮均分宽度
    :deep(.el-button) {
        flex: 1;
        min-width: 0; // 防止文本溢出
        text-align: center;
        // padding: 0 8px; // 调整内边距
    }

   
}
</style>