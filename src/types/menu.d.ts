export type MenuListType = {
  menu_id: number
  parent_id?: number // 父级菜单ID
  path: string // 路由
  name: string // 组件名
  component?: string // 改为字符串类型，表示组件路径
  meta: {
    lang_tag: string // 中英文menus.xxx.tag
    title: string // 菜单名称
    icon?: string // 菜单图标
    show_badge?: boolean // 是否显示徽标
    show_text_badge?: string // 是否显示新徽标
    is_hide?: boolean // 是否在菜单中隐藏
    is_hide_tab?: boolean // 是否在标签页中隐藏
    link?: string // 链接
    is_iframe?: boolean // 是否是 iframe
    keep_alive: boolean // 是否缓存
    auth_list?: Array // 可操作权限
    isInMainContainer?: boolean // 是否在主容器中
  }
  updated_at?: string // 最后更新时间
  children?: MenuListType[] // 子菜单
}

export type MenuListResp = {
  code: number
  message: string
  payload: MenuListType[]
}