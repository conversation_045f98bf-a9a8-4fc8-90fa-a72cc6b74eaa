/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    ArtBackToTop: typeof import('./../components/core/base/ArtBackToTop.vue')['default']
    ArtBarChart: typeof import('./../components/core/charts/ArtBarChart.vue')['default']
    ArtBarChartCard: typeof import('./../components/core/cards/ArtBarChartCard.vue')['default']
    ArtBasicBanner: typeof import('./../components/core/banners/ArtBasicBanner.vue')['default']
    ArtBreadcrumb: typeof import('./../components/core/layouts/art-breadcrumb/index.vue')['default']
    ArtButtonMore: typeof import('./../components/core/forms/ArtButtonMore.vue')['default']
    ArtButtonTable: typeof import('./../components/core/forms/ArtButtonTable.vue')['default']
    ArtCardBanner: typeof import('./../components/core/banners/ArtCardBanner.vue')['default']
    ArtChatWindow: typeof import('./../components/core/layouts/art-chat-window/index.vue')['default']
    ArtCutterImg: typeof import('./../components/core/media/ArtCutterImg.vue')['default']
    ArtDataListCard: typeof import('./../components/core/cards/ArtDataListCard.vue')['default']
    ArtDonutChartCard: typeof import('./../components/core/cards/ArtDonutChartCard.vue')['default']
    ArtDragVerify: typeof import('./../components/core/forms/ArtDragVerify.vue')['default']
    ArtDualBarCompareChart: typeof import('./../components/core/charts/ArtDualBarCompareChart.vue')['default']
    ArtExcelExport: typeof import('./../components/core/forms/ArtExcelExport.vue')['default']
    ArtExcelImport: typeof import('./../components/core/forms/ArtExcelImport.vue')['default']
    ArtException: typeof import('./../components/core/views/exception/ArtException.vue')['default']
    ArtFastEnter: typeof import('./../components/core/layouts/art-fast-enter/index.vue')['default']
    ArtFestivalTextScroll: typeof import('./../components/core/text-effect/ArtFestivalTextScroll.vue')['default']
    ArtFireworksEffect: typeof import('./../components/core/layouts/art-fireworks-effect/index.vue')['default']
    ArtFormInput: typeof import('./../components/core/forms/ArtFormInput.vue')['default']
    ArtFormSelect: typeof import('./../components/core/forms/ArtFormSelect.vue')['default']
    ArtGlobalSearch: typeof import('./../components/core/layouts/art-global-search/index.vue')['default']
    ArtHBarChart: typeof import('./../components/core/charts/ArtHBarChart.vue')['default']
    ArtHeaderBar: typeof import('./../components/core/layouts/art-header-bar/index.vue')['default']
    ArtHorizontalMenu: typeof import('./../components/core/layouts/art-menus/art-horizontal-menu/index.vue')['default']
    ArtIconSelector: typeof import('./../components/core/base/ArtIconSelector.vue')['default']
    ArtImageCard: typeof import('./../components/core/cards/ArtImageCard.vue')['default']
    ArtKLineChart: typeof import('./../components/core/charts/ArtKLineChart.vue')['default']
    ArtLineChart: typeof import('./../components/core/charts/ArtLineChart.vue')['default']
    ArtLineChartCard: typeof import('./../components/core/cards/ArtLineChartCard.vue')['default']
    ArtMapChart: typeof import('./../components/core/charts/ArtMapChart.vue')['default']
    ArtMenuRight: typeof import('./../components/core/others/ArtMenuRight.vue')['default']
    ArtMixedMenu: typeof import('./../components/core/layouts/art-menus/art-mixed-menu/index.vue')['default']
    ArtNetwork: typeof import('./../components/core/base/ArtNetwork.vue')['default']
    ArtNotification: typeof import('./../components/core/layouts/art-notification/index.vue')['default']
    ArtPageContent: typeof import('./../components/core/layouts/art-page-content/index.vue')['default']
    ArtProgressCard: typeof import('./../components/core/cards/ArtProgressCard.vue')['default']
    ArtRadarChart: typeof import('./../components/core/charts/ArtRadarChart.vue')['default']
    ArtRingChart: typeof import('./../components/core/charts/ArtRingChart.vue')['default']
    ArtScatterChart: typeof import('./../components/core/charts/ArtScatterChart.vue')['default']
    ArtScreenLock: typeof import('./../components/core/layouts/art-screen-lock/index.vue')['default']
    ArtSettingsPanel: typeof import('./../components/core/layouts/art-settings-panel/index.vue')['default']
    ArtSidebarMenu: typeof import('./../components/core/layouts/art-menus/art-sidebar-menu/index.vue')['default']
    ArtStatsCard: typeof import('./../components/core/cards/ArtStatsCard.vue')['default']
    ArtTable: typeof import('./../components/core/tables/ArtTable.vue')['default']
    ArtTableBar: typeof import('./../components/core/tables/ArtTableBar.vue')['default']
    ArtTextScroll: typeof import('./../components/core/text-effect/ArtTextScroll.vue')['default']
    ArtTimelineListCard: typeof import('./../components/core/cards/ArtTimelineListCard.vue')['default']
    ArtVideoPlayer: typeof import('./../components/core/media/ArtVideoPlayer.vue')['default']
    ArtWangEditor: typeof import('./../components/core/forms/ArtWangEditor.vue')['default']
    ArtWatermark: typeof import('./../components/core/others/ArtWatermark.vue')['default']
    ArtWorkTab: typeof import('./../components/core/layouts/art-work-tab/index.vue')['default']
    AttrHistory: typeof import('./../components/custom/Dialog/AttrHistory.vue')['default']
    Bar3d: typeof import('./../components/custom/ScreenUI/project/demoCard/bar3d.vue')['default']
    BlockVideo: typeof import('./../components/custom/ScreenUI/station/components/block-video.vue')['default']
    CommentItem: typeof import('./../components/custom/comment-widget/widget/CommentItem.vue')['default']
    CommentWidget: typeof import('./../components/custom/comment-widget/index.vue')['default']
    CurTime: typeof import('./../components/custom/ScreenUI/station/components/curTime.vue')['default']
    Drap: typeof import('./../components/custom/Upload/drap.vue')['default']
    ElAlert: typeof import('element-plus/es')['ElAlert']
    ElAvatar: typeof import('element-plus/es')['ElAvatar']
    ElBadge: typeof import('element-plus/es')['ElBadge']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElButtonGroup: typeof import('element-plus/es')['ElButtonGroup']
    ElCalendar: typeof import('element-plus/es')['ElCalendar']
    ElCard: typeof import('element-plus/es')['ElCard']
    ElCascader: typeof import('element-plus/es')['ElCascader']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElCheckboxGroup: typeof import('element-plus/es')['ElCheckboxGroup']
    ElCol: typeof import('element-plus/es')['ElCol']
    ElConfigProvider: typeof import('element-plus/es')['ElConfigProvider']
    ElDatePicker: typeof import('element-plus/es')['ElDatePicker']
    ElDescriptions: typeof import('element-plus/es')['ElDescriptions']
    ElDescriptionsItem: typeof import('element-plus/es')['ElDescriptionsItem']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDivider: typeof import('element-plus/es')['ElDivider']
    ElDrawer: typeof import('element-plus/es')['ElDrawer']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElEmpty: typeof import('element-plus/es')['ElEmpty']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElImage: typeof import('element-plus/es')['ElImage']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElInputNumber: typeof import('element-plus/es')['ElInputNumber']
    ElLink: typeof import('element-plus/es')['ElLink']
    ElMenu: typeof import('element-plus/es')['ElMenu']
    ElMenuItem: typeof import('element-plus/es')['ElMenuItem']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElPopover: typeof import('element-plus/es')['ElPopover']
    ElProgress: typeof import('element-plus/es')['ElProgress']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElRadioButton: typeof import('element-plus/es')['ElRadioButton']
    ElRadioGroup: typeof import('element-plus/es')['ElRadioGroup']
    ElRow: typeof import('element-plus/es')['ElRow']
    ElScrollbar: typeof import('element-plus/es')['ElScrollbar']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSlider: typeof import('element-plus/es')['ElSlider']
    ElSpace: typeof import('element-plus/es')['ElSpace']
    ElSubMenu: typeof import('element-plus/es')['ElSubMenu']
    ElSwitch: typeof import('element-plus/es')['ElSwitch']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTabPane: typeof import('element-plus/es')['ElTabPane']
    ElTabs: typeof import('element-plus/es')['ElTabs']
    ElTag: typeof import('element-plus/es')['ElTag']
    ElText: typeof import('element-plus/es')['ElText']
    ElTimeline: typeof import('element-plus/es')['ElTimeline']
    ElTimelineItem: typeof import('element-plus/es')['ElTimelineItem']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    ElTree: typeof import('element-plus/es')['ElTree']
    ElTreeSelect: typeof import('element-plus/es')['ElTreeSelect']
    ElUpload: typeof import('element-plus/es')['ElUpload']
    ElWatermark: typeof import('element-plus/es')['ElWatermark']
    Footer: typeof import('./../components/custom/ScreenUI/project/components/footer.vue')['default']
    Fullscreen: typeof import('./../components/custom/ScreenUI/fullscreen.vue')['default']
    Header: typeof import('./../components/custom/ScreenUI/project/components/header.vue')['default']
    HeaderBanner: typeof import('./../components/custom/bannel/headerBanner.vue')['default']
    HorizontalSubmenu: typeof import('./../components/core/layouts/art-menus/art-horizontal-menu/widget/HorizontalSubmenu.vue')['default']
    ItemCard: typeof import('./../components/custom/ScreenUI/project/components/itemCard.vue')['default']
    JsonEditor: typeof import('./../components/custom/jsonEditor/index.vue')['default']
    Line: typeof import('./../components/custom/ScreenUI/project/demoCard/line.vue')['default']
    LoginLeftView: typeof import('./../components/core/views/login/LoginLeftView.vue')['default']
    MiddleButtonList: typeof import('./../components/custom/ScreenUI/station/components/middle-button-list.vue')['default']
    MiniTable: typeof import('./../components/core/tables/MiniTable.vue')['default']
    Mp4Video: typeof import('./../components/custom/ScreenUI/station/components/mp4-video.vue')['default']
    MyTable: typeof import('./../components/custom/tables/MyTable.vue')['default']
    MyTableBar: typeof import('./../components/custom/tables/MyTableBar.vue')['default']
    PictorialBar: typeof import('./../components/custom/ScreenUI/project/demoCard/pictorialBar.vue')['default']
    Pie3d: typeof import('./../components/custom/ScreenUI/project/demoCard/pie3d.vue')['default']
    Project: typeof import('./../components/custom/ScreenUI/project/index.vue')['default']
    Radar: typeof import('./../components/custom/ScreenUI/project/demoCard/radar.vue')['default']
    Rain: typeof import('./../components/custom/ScreenUI/station/components/rain.vue')['default']
    RainWaterCtrol: typeof import('./../components/custom/ScreenUI/station/components/rain-water-ctrol.vue')['default']
    ReturnControl: typeof import('./../components/core/layouts/return-control/index.vue')['default']
    RightItemCard: typeof import('./../components/custom/ScreenUI/project/components/rightItemCard.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    ScaleScreen: typeof import('./../components/custom/ScreenUI/project/ScaleScreen/scale-screen.vue')['default']
    Screen: typeof import('./../components/custom/ScreenUI/station/screen.vue')['default']
    SidebarSubmenu: typeof import('./../components/core/layouts/art-menus/art-sidebar-menu/widget/SidebarSubmenu.vue')['default']
    StationFooter: typeof import('./../components/custom/ScreenUI/station/components/stationFooter.vue')['default']
    Summary: typeof import('./../components/custom/ScreenUI/station/components/summary.vue')['default']
    Template: typeof import('./../components/custom/ScreenUI/project/demoCard/template.vue')['default']
    TotalPie: typeof import('./../components/custom/ScreenUI/project/demoCard/totalPie.vue')['default']
    TypeKindCard: typeof import('./../components/custom/cards/TypeKindCard.vue')['default']
    TypesCard: typeof import('./../components/custom/cards/TypesCard.vue')['default']
    Upload: typeof import('./../components/custom/Upload/index.vue')['default']
    WarnList: typeof import('./../components/custom/ScreenUI/station/components/warn-list.vue')['default']
    WarnSummary: typeof import('./../components/custom/ScreenUI/station/components/warn-summary.vue')['default']
    Water: typeof import('./../components/custom/ScreenUI/station/components/water.vue')['default']
    Waterfall: typeof import('./../components/custom/ScreenUI/station/components/waterfall.vue')['default']
    Weather: typeof import('./../components/custom/ScreenUI/station/components/weather.vue')['default']
    Xyz3D: typeof import('./../components/custom/ScreenUI/project/demoCard/xyz3D.vue')['default']
  }
  export interface ComponentCustomProperties {
    vLoading: typeof import('element-plus/es')['ElLoadingDirective']
  }
}
