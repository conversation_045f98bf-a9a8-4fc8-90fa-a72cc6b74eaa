export type KeyValue = {
  key: string 
  value: string  
}
export type Device  = {
  device_type_id: string 
  device_id: string
  name: string 
  desc: string 
  status: number 
  device_code: string 
  device_key: string 
  extend_info: object
  active_online: number 
  active_status: number 
  alarm: object
  groups: string[]
  debug: boolean
  tags: KeyValue[]
  device_type_info: DeviceType
  group_info: DeviceGroup[]
  created_at: string 
  updated_at: string 
}

export type DeviceType = {
  id: number
  device_type_id: string
  device_type_code: string 
  name: string
  desc: string
  icon: string
  auth_type: number 
  access_type: number
  conn_type: string
  device_count: number
  status: number
  tags: KeyValue[]
  extend_info: ExtendInfo[]
  created_at: string
  updated_at: string 
}

export type GroupInfo = {
  name: string 
  group_id: string 
  desc: string 
}


export type ConnectInfo = {
  conn_protocol: string 
  msg_protocol: string 
  host: string 
  port: number  
  extend_info: object
}
export type ConnectMqttInfo = {
  client_id: string 
  username: string 
  password: string 
}

export type ConnectTcpInfo = {
  host: string 
  port: number  
}


export type AccessPoint = {
  access_point_id: string
  access_point_code: string 
  name: string 
  conn_protocol: string
  msg_protocol: string
  host: string
  port: number
  extend_info: object
  status: number
}



// 定义mqtt连接信息返回结构
export type AuthInfo = {
  client_id: string 
  username: string 
  password: string 
}

// 定义接口类型
export interface Attribute {
  device_type_id: string 
  attr_id: string 
  name: string
  identifier: string
  attr_type: string
  data_type: string
  data_options: object
  desc: string 
}

export interface Param {
  name: string 
  identifier: string
  data_type: string 
  data_options: object
  desc: string 
} 

export interface Event {
  device_type_id: string
  event_id: string 
  name: string
  identifier: string
  desc: string 
  params: Param[]
}

export interface Command {
  device_type_id: string
  command_id: string 
  name: string
  identifier: string
  desc: string 
  send_params: Param[]
  send_params_default: object
  reply_params: Param[]
  reply_params_default: object
}

// 
export type NumberDataOption = {
  default: string 
  unit: string 
  step: string 
  max: string 
  min: string 
}

export type SwitchDataOption = {
  value_type: string 
  on_text: string 
  off_text: string 
}


export type EnumDataOption = {
  enum_map: Record<string, string>;
}

export type TextDataOption = {
  default: string 
}

export type ObjectDataOption = {
  attr_list: Param[] // 属性列表
}


export type ListDataOption = {
  param_list: Param[] // 参数列表
}


export type ExtendInfo = {
  name: string 
  identifier: string 
  data_type: string
  enabled: boolean
  data_options: object
  desc: string 
  create_time: number  // 时间戳
  is_standard: boolean 
}