import { MenuListType } from "./menu"
import { RoleType  } from "./role"
import { UserInfo } from "./store"

export type CompanyType = {
    id: number                   
    created_at: string           
    updated_at: string           
    name:  string                
    fullname: string             
    logo: string                 
    desc: string                 
    uuid: string                 
    parent_id:  number           
    is_dev_man: boolean          
    province_id: number          
    city_id:  number             
    district_id: number          
    title: string         
    roles:  RoleType[]
    meta: any
    children: CompanyType
}
  
export type SwitchCompanyResp = {
    company: CompanyType
    menus: MenuListType[]
    token: string 
    user: UserInfo
}


export interface Company {
    id: number
    uuid: string
    name: string 
    fullname: string 
    district_id: number
    city_id: number
    province_id: number
  }
  