export type ErrorMessageMode = 'none' | 'modal' | 'message' | undefined

export interface RequestOptions {
  joinParamsToUrl?: boolean
  formatDate?: boolean
  isTransformResponse?: boolean
  isReturnNativeResponse?: boolean
  joinPrefix?: boolean
  apiUrl?: string
  errorMessageMode?: ErrorMessageMode
  joinTime?: boolean
  ignoreCancelToken?: boolean
  withToken?: boolean
}

// 基础接口返回的数据结构
export interface BaseResult<T = any> {
  code: number // 状态码
  message: string // 消息
  payload: T // 数据
  token?: string // 可选字段，用于返回 token
}

// 分页数据结构，继承基础结果结构
export interface PaginationResult<T> extends BaseResult {
  code: number // 状态码
  message: string // 消息
  payload: PageInfo<T> // 数据
}


export interface PageInfo<T>  extends BaseResult {
  page: number // 当前页
  page_size: number // 每页条数
  // lastPage: number // 总页数
  len: number // 当前页数量
  total: number // 总条数
  list: T;
}


// 
export interface  CaptchPayload<T = any> {
  captcha_id: string 
  img_data: string
}

// 验证码返回结构
export interface CaptchResult<T = any> {
  code: number 
  payload: CaptchPayload
  message: string
}
