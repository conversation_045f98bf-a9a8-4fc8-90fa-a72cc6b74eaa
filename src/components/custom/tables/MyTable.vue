<template>
  <div class="table_page">
    <div
      class="table_header"
      :style="{ 'margin-bottom': slots.tableHeaderSlot ? '10px' : '' }"
    >
      <slot name="tableHeaderSlot" />
    </div>
    <div class="table_main">
      <el-table
        ref="tableRef"
        border
        v-loading="loading"
        :header-cell-style="headerCellStyle"
        :cell-style="cellStyle"
        :max-height="$attrs['max-height'] || tableHeight"
        :data="tableData"
        style="width: 99.99%; min-height:450px"
        v-bind="$attrs"
        empty-text="暂无数据"
        @selection-change="selectionChange"
        @row-click="rowClick"
      >
        <el-table-column type="selection" width="32"  v-if="showSelect" />
        <template v-for="(item, index) in props.columns" :key="index">
          <!-- 表格序号 -->
          <el-table-column v-if="item.type === 'index'" v-bind="item">
            <template #default="scope">
              <span> {{ serialNumber(scope) }}</span>
            </template>
          </el-table-column>
          <el-table-column
            v-else-if="Object.hasOwn(item, 'dict')"
            v-bind="item"
            show-overflow-tooltip
            :label-class-name="item.required === true ? 'point-xin' : '' "  
          >
            <template v-if="item.headerSlot" #header="scope">
              <slot
                :name="item.prop + 'Header'"
                :scope="scope"
                :row="scope.row"
              />
            </template>
            <template #default="scope">
              <!-- <Dict
                :dictKey="item.dict"
                :dictValue="scope.row[item.prop]"
                v-if="typeof item.dict === 'string'"
                :key="Math.random()"
              /> -->
              <div v-html="dictHtml(item, scope)"></div>
            </template>
          </el-table-column>
          <!-- default -->
          <el-table-column
            v-else
            v-bind="item"
            :show-overflow-tooltip="!item.rowSlot"
            :label-class-name="item.required === true ? 'point-xin' : '' "  
          >
            <template v-if="item.headerSlot" #header="scope">
              <slot
                :name="item.prop + 'Header'"
                :scope="scope"
                :row="scope.row"
              />
            </template>
            <template v-if="item.rowSlot" #default="scope">
              <slot :name="item.prop" :scope="scope" :row="scope.row" />
            </template>
          </el-table-column>
        </template>
      </el-table>
    </div>
    <div class="table_footer">
      <slot name="tableFooterSlot">
        <div />
      </slot>
      <div>
        <el-pagination
          v-if="props.pagination"
          v-model:currentPage="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :pager-count="5"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          background
          @size-change="sizeChange"
          @current-change="currentChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import {
  useSlots,
  ref,
  reactive,
  useAttrs,
  onMounted,
  nextTick,
  onBeforeUnmount,
  watch
} from "vue";


const props = defineProps({
  // 列数据
  columns: {
    type: Array,
    required: true,
  },
  pageSize: {
    type: Number,
    default: 10,
  },
  showSelect: {
    // 是否显示第一列的选择框
    type:Boolean,
    default: false,
  },
  // 筛选数据
  queryParams: {
    type: Object,
    default: () => {},
  },
  pagination: {
    type: Boolean,
    default: true,
  },
  autoRequest:{
    type: Boolean,
    default: true,
  }
});

const emit = defineEmits(["pageRequest", "selectionChange", "rowClick", "getTableData"]);
const slots = useSlots();
const currentPage = ref(1);
const pageSize = ref(props.pageSize);
const showSelect = ref(props.showSelect);
// console.log("pageSize:", pageSize.value, props.pageSize)
// if (props.pageSize > 0) {
//   pageSize.value = props.pageSize;
// }
const total = ref(0);
const attrs = useAttrs();
const tableData = ref([]);
// const defaultHeight = ref(150);
const tableHeight = ref(750);
const loading = ref(false);
const selectList = ref([]);
const autoRequest = ref(props.autoRequest);

// 主动查询
const handleSearch = (e = {}) => {
  if (e.pageIndex) {
    currentPage.value = e.pageIndex;
  } else {
    currentPage.value = 1;
  }
  pageRequest();
};

// 请求
const pageRequest = () => {
  // console.log("table page request:", pageSize.value)
  loading.value = true;
  const params = {
    page: currentPage.value,
    page_size: pageSize.value,
  };
  // console.log("params:", params)
  Object.keys(props.queryParams).forEach((key) => {
    const type = Object.prototype.toString.call(props.queryParams[key]);
    if (type === "[object String]" && props.queryParams[key].trim() !== "") {
      params[key] = props.queryParams[key];
    }
    if (type === "[object Number]") {
      params[key] = props.queryParams[key];
    }
    if (
      type === "[object Object]" &&
      Object.keys(props.queryParams[key]).length
    ) {
      params[key] = props.queryParams[key];
    }
    if (type === "[object Array]" && props.queryParams[key].length) {
      params[key] = props.queryParams[key];
    }
  });

  emit("pageRequest", params, (callback) => {
    if (Object.prototype.toString.call(callback) === "[object Promise]") {
      callback
        .then((result) => {
          tableData.value = result.data;
          total.value = result.total;
          emit("getTableData", tableData.value);
        })
        .finally(() => {
          loading.value = false;
        });
    } else {
      throw new Error("callback should Promise");
    }
  });
};




const sizeChange = (v) => {
  pageSize.value = v;
  pageRequest();
  // toggleRowSelection();
};

const currentChange = (v) => {
  currentPage.value = v;
  pageRequest();
  // toggleRowSelection();
};
if (!attrs.data) {
  console.log("auto Request:", autoRequest.value, autoRequest.value === true)
  if(autoRequest.value === true) {
    pageRequest();
  }
} else {
  tableData.value = reactive({ ...props.data });
}

// 序号
const serialNumber = (scope) => {
  const index = scope.$index;
  if (currentPage.value === 1) {
    return index + 1;
  }
  return index + 1 + (currentPage.value - 1) * pageSize.value;
};

// dict rander
const dictHtml = (column, scope) => {
  if (column.dict) {
    if(!Array.isArray(scope.row[column.prop]) && String(scope.row[column.prop]).includes(",")) {
      // 不是数组，并且包含 ","  那么直接返回 
      return scope.row[column.prop];
    } else if (Array.isArray(scope.row[column.prop])){
      // 如果是数组的话
      let decodeList = [];
      scope.row[column.prop].forEach((value) => {
        let obj = column.dict.find((cur) => cur.value === value);
        if (obj) { decodeList.push(obj.label)}
      });
      return `<span>${decodeList.join(", ")}</span>`;
    } else {
      for (let i = 0; i < column.dict.length; i += 1) {
        if (!column.prop) break;
        if (scope.row[column.prop] === column.dict[i].value) {
          if (column.dict[i].color && column.dict[i].color?.includes("#")) {
            return `<span style="color:${column.dict[i].color}">${column.dict[i].label}</span>`;
          }
          return `<span class="${column.dict[i].color}">${column.dict[i].label}</span>`;
        }
      }
    }

  }
  return "";
};

// 表格高度
const getTableHeight = () => {
  if (props.autoRHeight) return;
  if (attrs.height) return;
  if (attrs["max-height"]) return;
  // tableHeight.value = "450px";
  nextTick(() => {
    const box = document.querySelector(".table_main");
    if (!box) return;
    const centerHeight = window.getComputedStyle(box).height;
    tableHeight.value = `${parseInt(centerHeight, 10)}px`;
  });
};
// 多选
const selectionChange = (values) => {
  emit("selectionChange", values);
  selectList.value = values;
};
// 用于多选表格 控制是否选中
// const tableRef = ref(null);
// const toggleRowSelection = () => {
//   selectList.value.forEach((select) => {
//     tableData.value.forEach((row, index) => {
//       if (select.id === row.id) {
//         tableRef.value.toggleRowSelection(tableData.value[index], true);
//       }
//     });
//   });
// };

// 点击某一行后的操作
const tableRef = ref(null);
const rowClick = (row, event, column) => {
  emit("rowClick", row, event, column)
  nextTick(() => {
    tableRef.value.toggleRowSelection(row, true)
  })
}

// 选中行
const selectRows  = (rows) => {
  nextTick(() => {
    rows.forEach((row) => {
      tableRef.value.toggleRowSelection(row, true)
    })
  })
}


//导出方法
defineExpose({
  handleSearch,
  selectRows,
});

onMounted(() => {
  getTableHeight();
  window.addEventListener("resize", getTableHeight, true);
});

onBeforeUnmount(() => {
  window.removeEventListener("resize", getTableHeight, true);
});

// 表头style;
const headerCellStyle = () => ({
  "background-color": 'var(--art-bg-color)',
  "font-size": "12px",
});

//单元格style
const cellStyle = () => ({
  color: 'var(--art-main-color)',
  "font-size": "12px",
});

watch(
    () => props.pageSize,
    (value) => {
        if (value && value > 0){
          pageSize.value = value;
          pageRequest();
        }
       
    }
);


</script>

<style lang="scss" scoped>
.table_page {
  flex: 1;
  display: flex;
  flex-direction: column;
  // background-color: #fff;
  background-color: var(--art-bg-color);
  // padding: 10px;
  height: 100%;
  box-sizing: border-box;

  .table_header {
  }

  .table_main {
    flex: 1;
  }

  .table_footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 10px;
  }

 
  /* 如果存在requred，前面给 * 样式调整 */
  :deep(.el-table th.el-table__cell.point-xin > div::before) {
      content: "*"; /* 使用星号字符 */
      color: #ff4d51; /* 设置为红色 */
      font-size: 18px; /* 确保星号足够大 */
      margin-right: 1px;
      vertical-align: middle;
  }
}
</style>