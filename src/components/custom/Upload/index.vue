<template>
  <el-upload
    :class="[
      { 'avatar-uploader': props.uploadType === 'image' },
      { hide: hide },
    ]"
    action="#"
    :http-request="httpRequest"
    :show-file-list="props.uploadType === 'image'"
    :before-upload="beforeUpload"
    :accept="props.accept"
    :list-type="props.uploadType === 'image' ? 'picture-card' : 'text'"
    :file-list="fileList"
    :on-preview="handlePictureCardPreview"
    :on-remove="handleRemove"
  >
    <!-- <template #file="{ file }" v-if="props.uploadType === 'image'">
      <div>
        <img class="el-upload-list__item-thumbnail" :src="file.url" alt="" />
      </div>
    </template> -->
    <div v-if="props.uploadType === 'image'">
      <el-icon class="avatar-uploader-icon"><Plus /></el-icon>
    </div>
    
    <div v-if="props.uploadType === 'file'" class="file-warp">
      <ul @click.stop="" v-if="fileList.length">
        <li v-for="item in fileList" :key="item">
          <p>{{ item.name }}</p>
          <el-icon
            color="#f56c6c"
            style="margin-left: 10px"
            @click.stop="handleRemove(item)"
          >
            <CircleCloseFilled />
          </el-icon>
        </li>
      </ul>
      <el-button type="primary" v-if="fileList.length < props.limit">
        点击上传
      </el-button>
    </div>
    <el-dialog append-to-body v-model="dialogVisible" @click.stop="">
      <img w-full :src="dialogImageUrl" alt="Preview Image" />
    </el-dialog>
  </el-upload>
</template>

<script setup>
import { ref, watch } from "vue";
import { Plus, CircleCloseFilled } from "@element-plus/icons-vue";
import { UploadFilled } from '@element-plus/icons-vue'
// import { imageToBase64, base64ToFile } from "@/utils/tools.js";

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => [],
  },
  //上传类型
  uploadType: {
    type: String, //file image
    required: true,
  },
  //返回数据格式 url file
  dataFormat: {
    type: String,
    default: "url",
  },
  accept: {
    type: String,
  },
  limit: {
    type: Number,
    default: 100,
  },
  size: {
    type: Number,
    default: 100,
  },
});

// 回显
const hide = ref(false);
const echo = (v) => {
  v.forEach((item) => {
    const noHasFile = fileList.value.every((file) => item.uid !== file.uid);
    if (noHasFile) {
      fileList.value.push({
        url: item.url,
      });
      // handleImgToBase64(item.url, (res) => {
      //   console.log(res, "ss");
      //   fileList.value.push(res);
      // });
      // fileList.value.push({
      //   name: item.name ? item.name : item.url.split("/").pop() || "",
      //   url: item.url,
      // });
    }
  });
  if (fileList.value.length < props.limit) {
    hide.value = false;
  } else {
    hide.value = true;
  }
};
// const handleImgToBase64 = (url, cb) => {
//   let image = new Image();
//   image.crossOrigin = "";
//   image.src = url;
//   image.onload = function () {
//     let base64 = imageToBase64(image); //图片转base64
//     let file = base64ToFile(base64, "file"); //base64转File
//     // 根据自身需求调整【因个人项目逻辑不一样，这里使用回调函数】
//     cb && typeof cb == "function" && cb(file);
//     return file;
//   };
// };
const emit = defineEmits(["update:modelValue"]);
const fileList = ref([]);
const beforeUpload = (file) => {
  return new Promise((resolve, reject) => {
    if (props.accept) {
      let acceptList = props.accept.split(",");
      let fileType = file.name.split(".").pop().toLocaleLowerCase();
      let isHas = acceptList.some((f) => f === `.${fileType}`);
      if (!isHas) {
        ElMessage.error(`上传文件格式只能是${props.accept}格式!`);
        return reject(false);
      }
    }
    let isSize = file.size / 1024 / 1024 <= props.size;
    if (!isSize) {
      ElMessage.error(`上传文件大小超过${props.size}M!`);
      return reject(false);
    }
    resolve(true);
  });
};

const httpRequest = ({ file }) => {
  if (props.dataFormat === "file") {
    let isNo = fileList.value.every((c) => c.uid !== file.uid);
    if (!isNo) return;
    fileList.value.push(file);
    emit("update:modelValue", fileList);
    if (fileList.value.length < props.limit) {
      hide.value = false;
    } else {
      hide.value = true;
    }
  }
};
const handleRemove = (file) => {
  let index = fileList.value.findIndex((c) => c.uid === file.uid);
  fileList.value.splice(index, 1);
  emit("update:modelValue", fileList);
};
const dialogVisible = ref(false);
const dialogImageUrl = ref("");
const handlePictureCardPreview = (file) => {
  dialogImageUrl.value = file.url;
  dialogVisible.value = true;
};

const uploadEcho = (newModelValue) => {
    fileList.value = [];
    echo(newModelValue)
}

defineExpose({
  uploadEcho,
})
// echo(props.modelValue)
// watch(
//   () => props.modelValue,
//   (value) => {
//     fileList.value = [];
//     echo(value);
//   },
//   {
//     deep: true,
//     immediate: true,
//   }
// );
</script>
<style lang="scss" scoped>
.avatar-uploader .avatar {
  width: 178px;
  height: 178px;
  display: block;
}
.file-warp {
  display: flex;
  flex-wrap: wrap;
  ul {
    display: flex;
    flex-wrap: wrap;
    margin-right: 20px;
    li {
      display: flex;
      align-items: center;
      p {
        max-width: 200px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        color: var(--el-color-primary);
      }
    }
  }
}
</style>

<style lang="scss" scoped>
.avatar-uploader .el-upload {
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.avatar-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.el-icon.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  text-align: center;
}
.hide {
  :deep(.el-upload--picture-card) {
    display: none;
  }
}
</style>
