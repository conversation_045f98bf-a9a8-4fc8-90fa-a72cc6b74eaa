<template>
  <div class="line-chart-card art-custom-card" :style="{ height: `${height}rem` }">
    <div class="card-body">
      <div class="chart-header">

          <div class="metric">
            <h1 class="value">{{ title }}</h1>
            <p class="label">{{ desc }}</p>
          </div>
      
          <div class="chart-container" :class="{ 'is-mini-chart': isMiniChart }" >
            <div class="container-icon">
              <i
                class="iconfont-sys"
                v-html="icon"
                :style="{ color: 'rgb(var(--art-primary))', fontSize: 50 + 'px'}"
              ></i>
            </div>
            
          </div>
        
        <div
          class="percentage is-increase"
          :class="{ 'is-increase': deviceCount > 0, 'is-mini-chart': isMiniChart }"
        >
          {{ deviceCount }} 设备
        </div>
        <div class="date" :class="{ 'is-mini-chart': isMiniChart }">
          {{ accessTypeToLabel(accessType) }}
        </div>
      </div>
      
    </div>
  </div>
</template>

<script setup lang="ts">
import { accessTypeToLabel } from "@/utils/device"; 

  interface Props {
    title: string
    desc: string 
    deviceCount: number
    accessType: number
    height?: number
    color?: string
    showAreaColor?: boolean
    isMiniChart?: boolean
    icon: string
  }

  const props = withDefaults(defineProps<Props>(), {
    title: '',
    desc: "",
    deviceCount: 0,
    accessType: 1,
    height: 11,
    color: '',
    showAreaColor: false,
    isMiniChart: true,
    icon: '&#xe784;'
  })



</script>

<style lang="scss" scoped>

 
  .art-custom-card:hover {
    cursor: pointer;
    transform: translateY(-1px);
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
  }

  .line-chart-card {
    position: relative;
    overflow: hidden;
    background-color: var(--art-main-bg-color);
    border-radius: var(--custom-radius);

    .chart-header {
      max-width: 65%;
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      padding: 20px 20px 0;
      margin-bottom: 10px;
    }

    .metric {
      .value {
        font-size: 1.7rem;
        font-weight: 500;
        line-height: 1;
        color: var(--art-text-gray-900);
      }

      .label {
        margin: 4px 0 0;
        font-size: 20px;
        color: var(--art-text-gray-700);
      }
    }

    .percentage {
      font-size: 14px;
      font-weight: 500;
      color: #f56c6c;

      &.is-increase {
        color: #67c23a;
      }

      &.is-mini-chart {
        position: absolute;
        bottom: 20px;
      }
    }

    .date {
      position: absolute;
      right: 20px;
      bottom: 20px;
      font-size: 12px;
      color: var(--art-text-gray-600);
    }

    .chart-container {
      position: absolute;
      right: 0;
      bottom: 0;
      left: 0;
      box-sizing: border-box;
      width: 100%;
      height: 90px;

      .container-icon {
        position: absolute;
        top: 5px;
        right: 5px;
        left: auto;
        width: 20%;
        height: 40px !important;
      }

      &.is-mini-chart {
        position: absolute;
        top: 25px;
        right: 40px;
        left: auto;
        width: 40%;
        height: 60px !important;
      }
    }
  }
</style>
