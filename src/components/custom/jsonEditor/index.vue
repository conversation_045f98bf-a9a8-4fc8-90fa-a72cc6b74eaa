<template>
    <div class="json-editor-container">
      <div class="json-editor">
        <div class="line-numbers">
          <div v-for="(_, index) in lines" :key="index" class="line-number">
            {{ index + 1 }}
          </div>
        </div>
        <div
          class="code-editor"
          ref="editorElement"
          contenteditable="true"
          @input="handleInput"
          @keydown.tab.prevent="handleTab"
          spellcheck="false"
          @keydown="handleKeyDown"
        ></div>
      </div>
      <div v-if="error" class="error-message">{{ error }}</div>
    </div>
  </template>
  
  <script setup lang="ts">
  import { ref, computed, watch, onMounted, onUnmounted } from "vue";
  import Prism from "prismjs";
  import "prismjs/themes/prism-tomorrow.css";
  import "prismjs/components/prism-json";
  
  const props = defineProps({
    modelValue: {
      type: Object,
      default: () => ( {}),
    },
  });
  
  const emit = defineEmits(["update:modelValue"]);
  
  const editorElement = ref<HTMLElement | null>(null);
  const error = ref("");
  const isFormatting = ref(false);
  const debounceTimer = ref<NodeJS.Timeout | null>(null);
  
  // 格式化后的JSON字符串
  const jsonString = computed(() => {
    try {
      return JSON.stringify(props.modelValue, null, 2);
    } catch {
      return "{}";
    }
  });
  
  // 计算行数（用于显示行号）
  const lines = computed(() => {
    return jsonString.value.split("\n");
  });
  
  // 更新编辑器内容（初始加载或外部数据变化时调用）
  const updateEditorContent = () => {
    if (!editorElement.value || isFormatting.value) return;
    try {
      const content = jsonString.value;
      editorElement.value.innerHTML = Prism.highlight(
        content,
        Prism.languages.json,
        "json"
      );
    } catch (e) {
    //   console.error("Syntax highlighting error:", e);
    }
  };
  
  // 防抖格式化（3秒无输入后执行）
  const debounceFormat = () => {
    // 清除之前的计时器
    if (debounceTimer.value) {
      clearTimeout(debounceTimer.value);
    }
    // 启动新的3秒计时器
    debounceTimer.value = setTimeout(() => {
      formatContent();
    }, 3000); // 3秒后执行
  };
  
  // 实际格式化逻辑
  const formatContent = () => {
    if (!editorElement.value || isFormatting.value) return;
  
    try {
      const text = getTextContent(editorElement.value);
      const parsed = JSON.parse(text);
      const formatted = JSON.stringify(parsed, null, 2);
  
      // 只有内容变化时才更新
    //   if (text !== formatted) {
        isFormatting.value = true;
        editorElement.value.innerHTML = Prism.highlight(
          formatted,
          Prism.languages.json,
          "json"
        );
        emit("update:modelValue", parsed);
        error.value = "";
    } catch (e) {
      error.value = "Invalid JSON format";
    } finally {
      isFormatting.value = false;
      placeCaretAtEnd(editorElement.value);
    }
  };
  
  // 处理输入事件
  const handleInput = () => {
    if (!editorElement.value) return;
    
    // 每次输入时重置3秒计时器
    debounceFormat();

    try {
      const text = getTextContent(editorElement.value);
      const parsed = JSON.parse(text);
      emit("update:modelValue", parsed);
      error.value = "";
    } catch (e) {
    //   error.value = "Invalid JSON format";
    }

  };
  
 

  // 获取纯文本内容（去除HTML标签）
  const getTextContent = (element: HTMLElement) => {
    return element.innerText || element.textContent || "";
  };
  
  // 处理Tab键（插入2个空格）
  const handleTab = (e: KeyboardEvent) => {
    e.preventDefault();
    if (!editorElement.value) return;
  
    const selection = window.getSelection();
    if (!selection?.rangeCount) return;
  
    const range = selection.getRangeAt(0);
    const tabNode = document.createTextNode("  "); // 2个空格
    range.insertNode(tabNode);
  
    // 移动光标到插入位置之后
    range.setStartAfter(tabNode);
    range.collapse(true);
    selection.removeAllRanges();
    selection.addRange(range);
  };
  
  // 处理回车键
  const handleKeyDown = (e: KeyboardEvent) => {
    if (e.key === "Enter") {
      e.preventDefault();
      document.execCommand("insertHTML", false, "\n");
    }
  };
  
  // 初始化编辑器
  onMounted(() => {
    updateEditorContent();
    placeCaretAtEnd(editorElement.value!);
  });
  
  // 组件卸载时清理资源
  onUnmounted(() => {
    if (debounceTimer.value) {
      clearTimeout(debounceTimer.value);
    }
  });
  

  // 将光标移动到末尾
  const placeCaretAtEnd = (el: HTMLElement) => {
    const range = document.createRange();
    const selection = window.getSelection();
    range.selectNodeContents(el);
    range.collapse(false);
    selection?.removeAllRanges();
    selection?.addRange(range);
  };


  </script>
  
  <style scoped>
  .json-editor-container {
    font-family: "Fira Code", "Consolas", monospace;
    background-color: #2d2d2d;
    border-radius: 4px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    width: 100%;
  }
  
  .json-editor {
    display: flex;
    min-height: 200px;
    max-height: 500px;
    overflow: auto;
  }
  
  .line-numbers {
    background-color: #252525;
    color: #858585;
    text-align: right;
    padding: 10px 8px;
    user-select: none;
    font-size: 14px;
    line-height: 1.5;
  }
  
  .line-number {
    min-height: 21px;
  }
  
  .code-editor {
    flex: 1;
    padding: 10px;
    outline: none;
    white-space: pre-wrap;
    font-family: inherit;
    font-size: 14px;
    line-height: 1.5;
    color: #f8f8f2;
    tab-size: 2;
  }
  
  .error-message {
    color: #ff5555;
    padding: 8px 16px;
    font-size: 14px;
    background-color: #1e1e1e;
  }
  
  /* Prism语法高亮自定义样式 */
  :deep(.token.property) {
    color: #7ec699;
  }
  
  :deep(.token.number) {
    color: #d19a66;
  }
  
  :deep(.token.boolean) {
    color: #ff79c6;
  }
  
  :deep(.token.string) {
    color: #f8f8f2;
  }
  
  :deep(.token.punctuation) {
    color: #f8f8f2;
  }
  </style>