<template>
  <el-dialog v-model="dialogVisible" title="属性历史数据" width="60vw"  top="5vh"  @close="handleClose()">
    <div class="history-data-container">
      <div class="history-time-range ">
        <el-date-picker
          v-model="timeRange"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="YYYY-MM-DD HH:mm:ss"
          class="shorter-date-picker"
          @change="handleRefresh"
        />
        <div class="action-buttons">
          <el-button type="primary" @click="handleRefresh">刷新</el-button>
          <el-button @click="handleExport">导出</el-button>
        </div>
      </div>
      
      <el-table :data="tableData" style="width: 100%" empty-text="暂无数据">
        <el-table-column prop="time" label="时间"  >
          <template #default="scope">
            {{ moment.unix(scope.row.ts).format('YYYY-MM-DD HH:mm:ss') }} 
          </template>
        </el-table-column>
        <el-table-column prop="attribute" label="属性"  >
          {{ attr?.name }}
        </el-table-column>
        <el-table-column prop="value" label="值"  >
          <template #default="scope"> 
            {{ attr === undefined? "" : scope.row[attr.identifier] }} {{ getAttrUnit()}}
          </template>
        </el-table-column>
        <el-table-column prop="type" label="属性类型" >
          <template #default="scope">
            <el-text class="mx-1" :type="GetAttrTypeCode(attr?.attr_type)" size="small">
                  {{ AttrTypeToLabel(attr?.attr_type) }}
            </el-text>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :background="true"
          layout="total, sizes, prev, pager, next"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { attributeService } from '@/api/device/attribute'
import { Attribute, NumberDataOption } from '@/types/device'
import { AttrTypeToLabel, GetAttrTypeCode } from '@/utils/device'
import { ApiStatus } from '@/utils/http/status'
import moment from 'moment'
import { ref } from 'vue'

const dialogVisible = ref(false)

// const timeRange = ref<[Date, Date] | null>(null);
const timeRange = ref([
  moment().startOf('day').format('YYYY-MM-DD HH:mm:ss'), 
  moment().endOf('day').format('YYYY-MM-DD HH:mm:ss')
])
const tableData = ref([]) // 初始为空数组，显示"暂无数据"

// 分页相关变量
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

const dayStrToNanoUnix = (dateStr: string) => {
  let datet = new Date(dateStr)
  return datet.getTime()
}

const params = ref({
  attr_id: "",
  device_id: "",
  start: dayStrToNanoUnix(timeRange.value[0]),
  end: dayStrToNanoUnix(timeRange.value[1]),
  page: currentPage.value,
  page_size: pageSize.value
})  

const handleClose = () => {
  dialogVisible.value = false 
  // 重置分页
  currentPage.value = 1
  pageSize.value = 10
}

const handleRefresh =async  () => {
  currentPage.value = 1 // 刷新时重置到第一页
  params.value.start =  dayStrToNanoUnix(timeRange.value[0])
  params.value.end =   dayStrToNanoUnix(timeRange.value[1])    
  await initAttrHistory()
}


const getAttrUnit = () => {
    let item = attr.value;
    if(item?.data_type === 'number' ) {
        const numberOption = item.data_options as NumberDataOption;
        if (numberOption?.unit) {
            return numberOption.unit;
        } else {
            return ""
        }
    } else {
        return ""
    }
}

const handleExport = () => {
  console.log('导出数据')
  // 这里可以添加导出数据的逻辑
}

// 分页大小改变
const handleSizeChange = (val: number) => {
  pageSize.value = val
  params.value.page_size = val
  initAttrHistory()
}

// 当前页改变
const handleCurrentChange = (val: number) => {
  currentPage.value = val
  params.value.page = val
  initAttrHistory()
}

const initAttrHistory = async () => {
  params.value.page = currentPage.value
  params.value.page_size = pageSize.value
  
  let resp = await attributeService.history(params.value)
  if (resp.code === ApiStatus.success) {
    tableData.value = resp.payload.list 
    total.value = resp.payload.total || 0
  }
}

const attr = ref<Attribute>()

const show = async (item: Attribute, deviceID: string) => {
  attr.value = item
  params.value.attr_id = item.attr_id
  params.value.device_id  = deviceID 
  await initAttrHistory()
  dialogVisible.value = true
}

defineExpose({
  show,
})
</script>

<style lang="scss" scoped>
.history-data-container {
  padding: 0 20px;
  
  .history-time-range {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    gap: 10px;

    .shorter-date-picker {
      width: 20px; /* 从原来的500px调整为400px */
    }
    
    .action-buttons {
      display: flex;
      gap: 10px;
    }
  }
  
  :deep(.el-table) {
    margin-top: 10px;
    
    .el-table__empty-block {
      width: 100% !important;
    }
  }

  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
}
</style>