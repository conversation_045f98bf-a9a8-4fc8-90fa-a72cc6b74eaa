<template>
    <div @click="toggleFullscreen"  class="full-screen-class">
        <!-- <el-icon v-if="!isFullscreen"><FullScreen /></el-icon> -->
        <!-- <el-icon v-else><Aim /></el-icon> -->
        <div class="fullscreen-div-class" v-if="!isFullscreen"></div>
        <div class="exit-fullscreen-div-class" v-else></div>
  </div>
</template>
<script setup>
import { ref, inject, onMounted, onUnmounted } from 'vue'
import { FullScreen, Aim } from '@element-plus/icons-vue';


const isFullscreen = ref(false)

let screenBgRef = null;

const initRef = () => {
  try{
    screenBgRef = inject('screenBgRef'); // 注入 screenBgRef, 哪个页面需要全屏，就需要提供 provide('screenBgRef', screenBgRef);
  } catch (error) {
    console.log("err:", error)
  }
}



const toggleFullscreen = async () => {
  try {
    if (!isFullscreen.value && screenBgRef.value) {
      await screenBgRef.value.requestFullscreen();
    } else {
      await document.exitFullscreen();
    }
  } catch (error) {
    console.error("无法切换全屏模式：", error);
  }

  isFullscreen.value = !isFullscreen.value;
};

// 监听全屏状态的变化
const updateFullscreenStatus = () => {
  try{
    isFullscreen.value = document.fullscreenElement === screenBgRef.value;
  } catch (error) {
    console.error("err:", error)
  }

};

onMounted(() => {
  initRef();
  document.addEventListener('fullscreenchange', updateFullscreenStatus);
  updateFullscreenStatus(); // 初始化当前的全屏状态
});

onUnmounted(() => {
  document.removeEventListener('fullscreenchange', updateFullscreenStatus);
});
</script>


<style lang="css" scoped>

.full-screen-class {
    pointer-events: all; /* 恢复点击事件 */
    cursor: pointer;
}

.fullscreen-div-class {
  width: 15px;
  height: 15px;
  /* background-image: url("@/assets/icons/svg/fullscreen.svg"); */
  background-size: cover;
  background-repeat: no-repeat;
  cursor: pointer;
  pointer-events: all;
}

.exit-fullscreen-div-class {
  width: 15px;
  height: 15px;
  /* background-image: url("@/assets/icons/svg/exit-fullscreen.svg"); */
  background-size: cover;
  background-repeat: no-repeat;
  cursor: pointer;
  pointer-events: all;
}
</style>