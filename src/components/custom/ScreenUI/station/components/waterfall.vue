<template>
    <div class="waterfall-class">
        <div class="waterfall-show-btn" @click="showControlBtn"  v-if="!visible">
            <img :src="waterfallShowBtnImg"  alt />
        </div>
        <div class="waterfall-show-btn" @click="closeControlBtn"  v-if="visible">
            <img :src="waterfallCloseBtnImg"  alt />
        </div>

        <div class="waterfall-control-btn"   ref="waterfallDialogRef"  v-if="visible">
            <div class="waterfall-control-item">
                <div class="item" @click="waterfallCtrol('close')">
                    <img :src="waterfallClose" alt />
                </div>
                <div class="item" @click="waterfallCtrol('small')">
                    <img :src="waterfallSmall" alt />
                </div>
                <div class="item"  @click="waterfallCtrol('big')">
                    <img :src="waterfallBig" alt />
                </div>
            </div>
            
        </div>
    </div>
</template>

<script setup>
import { ref } from "vue";
import waterfallShowBtnImg from "./icon/waterfall_control.png";
import waterfallCloseBtnImg from "./icon/waterfall_control_2.png";
import waterfallClose from "./icon/waterfall_close.png";
import waterfallSmall from "./icon/waterfall_small.png";
import waterfallBig from "./icon/waterfall_big.png";

const emit = defineEmits(["waterfallCtrol"]);

const visible = ref(false);
const waterfallDialogRef = ref(null);

const showControlBtn = () => {
    visible.value = true;
}

const closeControlBtn = () => {
    visible.value = false;
}

const waterfallCtrol = (item) => {
    emit("waterfallCtrol", item)
}


</script>

<style scoped lang="scss">
.waterfall-class {
    display: flex;
}

.waterfall-show-btn {
    width: 113px;
    height: 33px;
    cursor: pointer;
}


.waterfall-control-btn {
    // position: absolute;
    display: flex;
    opacity: 1;
    // left: 35vw;
    // top: 80vh;
    margin-left: 20px;
    margin-bottom: 100px;
    height: 90px;
    width: 296px;
    z-index: 102;
    background-image: url('./icon/waterfall_control_bg.png');
    background-repeat: no-repeat;
    background-position: center;
}


.waterfall-control-item {
    width: 296px;
    margin-left: 30px;
    margin-top: 40px;
    display: flex;
    z-index: 199;
    pointer-events: all;
}

.item {
    // margin-top: 20px;
    cursor: pointer;
    width: calc((100% - 10px) / 3);
    height: 38px;
    /* 间隙为5px */
    margin: 0 5px 5px 0;
    
    .item-text {
        cursor: pointer;
        font-size: 12px;
    }
}
.item:nth-child(3n) {
    /* 去除第3n个的margin-right */
    margin-right: 0;
}

</style>