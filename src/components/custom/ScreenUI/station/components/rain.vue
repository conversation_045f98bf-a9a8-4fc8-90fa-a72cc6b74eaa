<template>
    <div ref="echarRef"  class="echart-class"  @resize="resize">

    </div>
</template>


<script setup>
import { nextTick, onMounted, ref, onBeforeUnmount } from "vue";
import * as echarts from "echarts";

const echarRef = ref(null);

const option = {
  xAxis: {
    type: 'category',
    data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月'],
    axisLabel:{
        textStyle:{
          color: '#5e8cb0'
        }
      }
  },
  yAxis: {
    type: 'value',
    axisLabel:{
        textStyle:{
                color: '#5e8cb0'
            }
        },
        splitLine: {
        lineStyle:{
            color: "#353b5a",
            type: "dashed"
        }
    },
  },
  series: [
    {
      data: [120, 200, 150, 80, 70, 110, 130],
      type: 'bar'
    }
  ]
};

let myChart;
const initChart = () => {
    if (myChart !== null && myChart !== "" && myChart !== undefined ) {
        myChart.dispose();
    }
    nextTick(() =>{
      myChart = echarts.init(echarRef.value);
      const option = getChartOption();
      console.log("option:", option);
      myChart.setOption(option);
    })
}


const getChartOption = () =>{
    return option;
}

const resize = () => {
  try{
    if (myChart) {
      const containerWidth = echarRef.value.clientWidth;
      const containerHeight = echarRef.value.clientHeight;
      myChart.resize({
        width: containerWidth,
        height: containerHeight
      });
    }
  }catch (error) {
    console.log(error)
  }
};

onMounted(() => {
    nextTick(() => {
        initChart()
    })
    window.addEventListener("resize", resize, true);
})

onBeforeUnmount(() => {
  window.removeEventListener("resize", resize, true);
  myChart?.dispose();
});
</script>


<style scoped lang="scss">
.chart {
  color:cadetblue;
}

.echart-class {
  height: 100%;
  width: 100%;
}
</style>

