<template>
    <div ref="echarRef"  class="echart-class">

    </div>
</template>


<script setup>
import { nextTick, onMounted,onBeforeUnmount , ref } from "vue";
import * as echarts from "echarts";

const echarRef = ref(null);


const option = {
  tooltip: {
    trigger: 'item'
  },
  legend: {
    left: 'center',
    textStyle: { //图例文字的样式
        color: '#fff'
    },
  },
  series: [
    {
      name: 'Access From',
      type: 'pie',
      radius: ['20%', '60%'],
      center: ['50%', '70%'],
      avoidLabelOverlap: false,
      label: {
        show: false,
        position: 'center'
      },
      emphasis: {
        label: {
          show: true,
          fontSize: 40,
          fontWeight: 'bold',
        }
      },
      labelLine: {
        show: false
      },
      data: [
        { value: 1048, name: '黄色预警'  },
        { value: 735, name: '告警'  },
        { value: 580, name: '橙色预警'  },
        { value: 484, name: '红色预警' },
        { value: 300, name: '蓝色预警'  }
      ]
    }
  ]
};

let myChart;
const initChart = () => {
    if (myChart !== null && myChart !== "" && myChart !== undefined ) {
        myChart.dispose();
    }
    nextTick(() =>{
      myChart = echarts.init(echarRef.value);
      const option = getChartOption();
      console.log("option:", option);
      myChart.setOption(option);
    })
}


const getChartOption = () =>{
    return option;
}

const resize = () => {
  try{
    if (myChart) {
      const containerWidth = echarRef.value.clientWidth;
      const containerHeight = echarRef.value.clientHeight;
      myChart.resize({
        width: containerWidth,
        height: containerHeight
      });
    }
  }catch (error) {
    console.log(error)
  }
};

onMounted(() =>{
  nextTick(() => {
      initChart()
  })
  window.addEventListener("resize", resize, true);
})
onBeforeUnmount(() => {
  window.removeEventListener("resize", resize, true);
  myChart?.dispose();
});
</script>


<style scoped lang="scss">
.chart {
  color:cadetblue;
}

.echart-class {
    margin-top: 0px;
  height: 100%;
  width: 100%;
}
</style>

