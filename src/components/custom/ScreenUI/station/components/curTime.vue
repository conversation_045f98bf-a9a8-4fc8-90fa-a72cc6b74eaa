<template>
    <div class="cur-time-div-class">
        {{ currentTime }}
    </div>
</template>


<script setup>
import { ref, onMounted, onBeforeUnmount } from "vue";
import moment from "moment";


const currentTime = ref(moment().format('YYYY-MM-DD HH:mm:ss'));


// 定时器，每秒更新一次当前时间
let interval;

onMounted(() => {
    // 设置定时器，每秒更新一次时间
    interval = setInterval(() => {
        currentTime.value = moment().format('YYYY-MM-DD HH:mm:ss');
    }, 1000);
})

onBeforeUnmount(() => {
    // 清除定时器，防止内存泄漏
    clearInterval(interval);
})

</script>


<style lang="css" scoped>
.cur-time-div-class {
    width: 180px;
    color: #ffffff;
    margin-left: 100px;
}


</style>