<template>
    <div ref="echarRef"  class="echart-class"   @resize="resize">

    </div>
</template>


<script setup>
import { nextTick, onMounted,onBeforeUnmount , ref } from "vue";
import * as echarts from "echarts";

const echarRef = ref(null);


const option = {
  tooltip: {
    trigger: 'item'
  },
  // legend: {
  //   orient: 'vertical',
  //   left: 'left'
  // },
  color: ['#fc8251', '#5470c6', '#91cd77', '#ef6567', '#f9c956', '#75bedc'],
  series: [
    {
      name: '传感器总览',
      type: 'pie',
      radius: '50%',
      data: [
        { value: 735, name: '水位传感器', label:{color: "#FFFFFF"} },
        { value: 580, name: '雨量传感器', label:{color: "#FFFFFF"} },
        { value: 484, name: '视频设备', label:{color: "#FFFFFF"}  },
        { value: 300, name: '振弦传感器', label:{color: "#FFFFFF"}  }
      ],
      emphasis: {
        itemStyle: {
          shadowBlur: 0,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }
  ]
};

let myChart;
const initChart = () => {
    if (myChart !== null && myChart !== "" && myChart !== undefined ) {
        myChart.dispose();
    }
    nextTick(() =>{
      myChart = echarts.init(echarRef.value);
      const option = getChartOption();
      console.log("option:", option);
      myChart.setOption(option);
    })
}


const getChartOption = () =>{
    return option;
}

const resize = () => {
  try{
    if (myChart) {
      const containerWidth = echarRef.value.clientWidth;
      const containerHeight = echarRef.value.clientHeight;
      myChart.resize({
        width: containerWidth,
        height: containerHeight
      });
    }
  }catch (error) {
    console.log(error)
  }
};

onMounted(() =>{
  nextTick(() => {
      initChart()
  })
  window.addEventListener("resize", resize, true);
})

onBeforeUnmount(() => {
  window.removeEventListener("resize", resize, true);
  myChart?.dispose();
});
</script>


<style scoped lang="scss">
.chart {
  color:cadetblue;
}

.echart-class {
  height: 100%;
  width: 100%;
}
</style>

