<template>
 
      <div class="video-container">
          <!-- <div class="video-container-head"> -->
              <!-- <el-select
                v-model="playUrl"
                clearable
                placeholder="请选择视频流"
                style="width: 100%"
              >
                <el-option
                  v-for="item in dataList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select> -->
              <!-- <el-button type="primary" style="margin:0 10px" @click="startPlaying()">开始播放</el-button>
              <el-button type="danger" @click="pausePlayback()">停止播放</el-button> -->
          <!-- </div> -->
          <div class="video-container-body"></div>
      </div>
</template>
<script setup>
import { ElMessage } from 'element-plus';
import { ref , onBeforeUnmount } from 'vue';
import { videoList, getDeviceChannel } from "@/api/video.api.js"; 
import useUser from "@/stores/user.js";


const playUrl = ref('')
const jessibuca = ref()


const genPlayUrl = (deviceId, channelID) => {
  return configStore.getWssVideo() + "/" + deviceId + "_" + channelID + ".live.flv"
}


const currentDev = ref({});
const queryParams = {page:1, count: 10}
videoList(queryParams).then((res) => {
  if (res.code == 0 && res.data.list !== undefined && res.data.list.length > 0) {
    // currentDev.value = res.data.list[0]
    currentDev.value = res.data.list[res.data.list.length - 1];
  } else{
    console.log("res:", res);
  }
}).finally(() => {
  let deviceId = "";
  if(currentDev.value !== undefined ){
    deviceId = currentDev.value.deviceId ;
  }
  getDeviceChannel(deviceId, {}).then((resp)=> {
    if(resp.code == 0 && resp.data.list !== undefined) {
      // let currentChannel = resp.data.list[0];
      let currentChannel = resp.data.list[resp.data.list.length - 1];
      if(currentChannel !== undefined) {
        playUrl.value = genPlayUrl(currentChannel.deviceId, currentChannel.channelId)
        startPlaying();
      }
    } else {
      console.log(resp)
    }
  })
});




const init = () => {
  const options = {};
  jessibuca.value = new window.Jessibuca(Object.assign({
      container: document.querySelector('.video-container-body'), // 播放器容器,若为 string ，则底层调用的是 document.getElementById('id')
      videoBuffer: 1, // 设置最大缓冲时长，单位秒，播放器会自动消除延迟。
      forceNoOffscreen: true, // 是否不使用离屏模式（提升渲染能力）
      hasAudio: false, // 是否有音频
      rotate: 0, // 设置旋转角度，只支持，0(默认) ，180，270 三个值
      isResize: true, // 1.当为true的时候：视频画面做等比缩放后,高或宽对齐canvas区域,画面不被拉伸,但有黑边;2.当为false的时候：视频画面完全填充canvas区域,画面会被拉伸
      isFullSize: false, // 当为true的时候：视频画面做等比缩放后,完全填充canvas区域,画面不被拉伸,没有黑边,但画面显示不全
      debug: false, // 是否开启控制台调试打印
      timeout: 30, // 设置超时时长, 单位秒,在连接成功之前和播放中途,如果超过设定时长无数据返回,则回调timeout事件
      supportDblclickFullscreen: true, // 是否支持屏幕的双击事件，触发全屏，取消全屏事件。
      showBandwidth: false, // 是否显示网速
      operateBtns: {
          fullscreen: true,// 是否显示全屏按钮
          screenshot: true,// 是否显示截图按钮
          play: true,// 是否显示播放暂停按钮
          audio: true,// 是否显示声音按钮
          recorder: false
      },
      keepScreenOn: false, // 开启屏幕常亮，在手机浏览器上, canvas标签渲染视频并不会像video标签那样保持屏幕常亮。
      isNotMute: false, // 是否开启声音，默认是关闭声音播放的。
      loadingText: "加载中...", // 加载过程中文案。
      background: "", // 背景图片。
      decoder: '/js/jessibuca/decoder.js',
      // useMSE: false,
      isFlv: true,
  },
      options
  ));
}
const startPlaying = () => {
  console.log("play url:", playUrl.value)
  if (!playUrl.value) {
      // return message.error('请输入视频流地址')
      return ElMessage.error("请输入视频流地址");
  }
  init();
  // 添加认证header
  const token = useUser().token;
  const headers = {Authorization: "Bearer " + token}
  jessibuca.value.play(playUrl.value,  {"headers": headers}).catch(() => {
      // message.error('播放失败，请检查视频地址是否正确')
      return ElMessage.error("播放失败，请检查视频地址是否正确");
  })
}
const pausePlayback = () => {
  if (jessibuca.value) {
      jessibuca.value.pause()
  }
}

// onMounted(() => {
//   videoList();
// })


onBeforeUnmount(() => {
  if (jessibuca.value) {
      jessibuca.value.destroy()
  }
})
</script>
<style lang="scss" scoped>


.video-container {
  // height: calc(100vh - 140px);
  height: 100%;
  display: flex;
  flex-direction: column;
}


.video-container    .video-container-head {
      background-color: #fff;
      display: flex;
      align-items: center;
      // padding: 20px;
  }

.video-container    .video-container-body {
      flex: 1;
      background-color: #333;
  }

</style>