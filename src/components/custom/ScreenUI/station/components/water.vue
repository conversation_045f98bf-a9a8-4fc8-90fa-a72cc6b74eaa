<template>
    <div ref="echarRef"  class="echart-class"  @resize="resize">

    </div>
</template>


<script setup>
import { nextTick, onMounted, ref, onBeforeUnmount } from "vue";
import * as echarts from "echarts";

const echarRef = ref(null);

const option = {
//   xAxis: {
//     type: 'category',
//     data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
//   },
//   yAxis: {
//     type: 'value'
//   },
//   series: [
//     {
//       data: [150, 230, 224, 218, 135, 147, 260],
//       type: 'line'
//     }
//   ]
  tooltip: {
      trigger: 'axis',
    },
    // legend: {
    //   data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
    //   textStyle:{
    //     color: '#cccccc'
    //   }
    // },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: ['2024-10-15', '2024-10-16', '2024-10-17', '2024-10-18', '2024-10-19', '2024-10-20', '2024-10-21'],
      axisLabel:{
        textStyle:{
          color: '#5e8cb0'
        }
      }
    },
    yAxis: {
      type: 'value',
      axisLabel:{
        textStyle:{
          color: '#5e8cb0'
        }
      },
      splitLine: {
        lineStyle:{
          color: "#353b5a",
          type: "dashed"
        }
      },
    },
    series:  [
        {
        data: [150, 230, 224, 218, 135, 147, 260],
        type: 'line'
        }
    ],
};

let myChart;
const initChart = () => {
    if (myChart !== null && myChart !== "" && myChart !== undefined ) {
        myChart.dispose();
    }
    nextTick(() =>{
      myChart = echarts.init(echarRef.value);
      const option = getChartOption();
      console.log("option:", option);
      myChart.setOption(option);
    })
}


const getChartOption = () =>{
    return option;
}

const resize = () => {
  try{
    if (myChart) {
      const containerWidth = echarRef.value.clientWidth;
      const containerHeight = echarRef.value.clientHeight;
      myChart.resize({
        width: containerWidth,
        height: containerHeight
      });
    }
  }catch (error) {
    console.log(error)
  }
};

onMounted(() => {
    nextTick(() => {
        initChart()
    })
    window.addEventListener("resize", resize, true);
})

onBeforeUnmount(() => {
  window.removeEventListener("resize", resize, true);
  myChart?.dispose();
});
</script>


<style scoped lang="scss">
.chart {
  color:cadetblue;
}

.echart-class {
  height: 100%;
  width: 100%;
}
</style>

