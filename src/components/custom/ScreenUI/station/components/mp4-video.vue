<template>
    <div class="video-content">
        <video 
            class="video-element"
            style="width: 100%;height: 100%;"
            autoplay
            loop
            playsinline
            muted
            controls
        >
            <source :src="videoUrl" type="video/mp4" />
        </video> 
    </div>
</template>

<script setup>
import { ref } from "vue";

const videoUrl = ref("https://nuc.beithing.com:60080/public/mp4/video1.mp4");

</script>

<style lang="scss" scoped>
.video-content {
    // background-color: rgb(40, 46, 83);
    width: 100%;
    height: 100%;
}

.video-element {
    object-fit: fill;
}

</style>