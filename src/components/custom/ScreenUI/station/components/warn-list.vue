<script setup >
import { currentGET } from "@/api/bigScreen.api.js";
import SeamlessScroll from "@/components/SeamlessScroll";
import { computed, onMounted, reactive } from "vue";
import moment from "moment";
// import { useSettingStore } from "@/stores";
import { storeToRefs } from "pinia";
import EmptyCom from "@/components/EmptyCom";

import useSettingStore from "@/stores/screen.setting.js";

import { getWebSocket, unSubWebsocket } from '@/utils/websocket';
import { getWsID } from "@/utils/random.js";
import { onUnmounted, ref  } from "vue";
import { levelMap, warnTypeMap } from "@/utils/earlyWarnData.js";



const wsID = ref( getWsID("big-screen-warn-list"));
const topic = ref("/topic/bigscreen/warn/list");


const  settingStore = useSettingStore();
const { defaultOption, indexConfig } = storeToRefs(settingStore);
const state = reactive({
  list: [],
  defaultOption: {
    ...defaultOption.value,
    singleHeight: 252,
    limitScrollNum: 3,
    // step:3
  },
  scroll: true,

});

const getData = () => {
  getWebSocket(wsID.value, topic.value, {}).subscribe((res) => {
    state.list = res.body;
  });
  currentGET("warnList", { limitNum: 20 }).then((res) => {
    console.log("预警数据:", res)
    if(res.code== 0)  {
      // state.list = res.payload.list;
    } else {
      window.$message({
        text: res.msg,
        type: "warning",
      });
    }
  });
};

const comName = computed(() => {
  if (indexConfig.value.rightBottomSwiper) {
    return SeamlessScroll;
  } else {
    return EmptyCom;
  }
});
function montionFilter (val) {
    // console.log(val);
    return val ? Number(val).toFixed(2) : '--'
}
const handleAddress=(item)=>{
  return `${ item.provinceName }/${item.cityName }/${item.countyName}`
}
onMounted(() => {
  getData();
});


onUnmounted(()=>{
  unSubWebsocket(wsID.value, topic.value);
})





</script>

<template>
  <div
    class="right_bottom_wrap beautify-scroll-def"
    :class="{ 'overflow-y-auto': !indexConfig.rightBottomSwiper }"
  >
    <component
      :is="comName"
      :list="state.list"
      v-model="state.scroll"
      :singleHeight="state.defaultOption.singleHeight"
      :step="state.defaultOption.step"
      :limitScrollNum="state.defaultOption.limitScrollNum"
      :hover="state.defaultOption.hover"
      :singleWaitTime="state.defaultOption.singleWaitTime"
      :wheel="state.defaultOption.wheel"
      v-if="state.list.length > 0 "
    >
      <ul class="right_bottom">
        <li class="right_center_item" v-for="(item, i) in state.list" :key="i">
          <span class="orderNum">{{ i + 1 }}</span>
          <div class="inner_right">
            <div class="dibu"></div>
            <div class="flex">
              <div class="info">
	                <span class="labels">测站编码:</span>
                <span class="text-content zhuyao"> {{ item.station_sn }}</span>
              </div>
              <div class="info">
	                <span class="labels">告警类型:</span>
                <span class="text-content"> {{  warnTypeMap[item.warn_type] }}</span>
              </div>
              <div class="info">
	                <span class="labels">告警值：</span>
                <span class="text-content warning">
                  {{  montionFilter(item.current_value) }}</span
                >
              </div>
            </div>

            <div class="flex">
              <div class="info">
	                <span class="labels shrink-0"> 告警等级：</span>
                <span class=" ciyao truncate" style="font-size: 12px;width: 150px;">
                  {{  levelMap[item.level] }}</span
                >
              </div>
              <div class="info time shrink-0">
	                <span class="labels">时间：</span>
                <span class="text-content" style="font-size: 12px">
                  {{ moment(item.created_at).format("YYYY-MM-DD HH:mm:ss") }}</span
                >
              </div>
            </div>
            <div class="flex">
              <div class="info">
	                <span class="labels">报警内容：</span>
                <span
                  class="text-content ciyao"
                  :class="{ warning: item.message }"
                >
                  {{ item.message || "无" }}</span
                >
              </div>
            </div>
          </div>
        </li>
      </ul>
    </component>
    <div class="not-warn-list-div" v-else>
      <h2>没有告警数据!</h2>
    </div>
  </div>
</template>

<style scoped lang="scss">
.right_bottom {
  width: 100%;
  height: 90%;

  .right_center_item {
    display: flex;
    align-items: center;
    justify-content: center;
    height: auto;
    padding: 10px;
    font-size: 12px;
    color: #fff;

    .orderNum {
      margin: 0 20px 0 -20px;
    }

    .inner_right {
      position: relative;
      height: 100%;
      width: 400px;
      flex-shrink: 0;
      line-height: 1.5;

      .dibu {
        position: absolute;
        height: 2px;
        width: 104%;
        background-image: url("@/assets/img/zuo_xuxian.png");
        bottom: -12px;
        left: -2%;
        background-size: cover;
      }
    }

    .info {
      margin-right: 10px;
      display: flex;
      align-items: center;

      .labels {
        flex-shrink: 0;
        font-size: 12px;
        color: rgba(255, 255, 255, 0.6);
      }

      .zhuyao {
        // color: $primary-color;
        color:  #1890ff;
        font-size: 12px;
      }

      .ciyao {
        color: rgba(255, 255, 255, 0.8);
      }

      .warning {
        color: #e6a23c;
        font-size: 12px;
      }
    }
  }
}

.right_bottom_wrap {
  overflow: hidden;
  width: 100%;
  height: 200px;
}

.overflow-y-auto {
  overflow-y: auto;
}


.not-warn-list-div {
  color: #fff;
  text-align:center;
  padding: 45px 0;
  
}
</style>