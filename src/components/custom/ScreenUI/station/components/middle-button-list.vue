<template>
    <div class="yqlist">
      <ul class="clearfix">
        <li v-for="(item, index) in btnInfoList" :key="index">
          <div class="yq"  :style="item.style + drakgreyColor"  @click="handleClick(item)" v-if="item.show === false">
            {{ item.text }}
          </div>
          <div class="yq"  :style="item.style"  @click="handleClick(item)" v-else>
            {{ item.text }}
          </div>
        </li>
      </ul>
    </div>
  </template>
  
<script setup>
import { ref } from "vue";

const emit = defineEmits(["middleBtnClick"]);

const props = defineProps({
  btnInfoList: {
    type: Array,
    default: [],
  }
})

const btnInfoList = ref(props.btnInfoList);

// const btnInfoList = ref([
//   { text: '水位', key: 'water', style: "--translate-y: -40px;" },
//   { text: '雨量', key: 'rain' , style: "--translate-y: -20px;" },
//   { text: "视频", key: 'video', style:"--translate-y: 0px;"},
//   { text: '渗压', key: 'vwp' ,  style: "--translate-y: -20px;"},
//   { text: '总览', key: 'all' ,  style: "--translate-y: -40px;"},
//   // { text: '切换', class: 'yq-change', clickHandler: clickChangeBg },
// ]); 

const showHiddenMap = ref({}); 
const drakgreyColor = "color: darkgrey;"
const handleClick = (item) => {
  // console.log(`Clicked: ${item.text}`);
  let method = ""; //  "show_" + String(tdata.key);
  if (showHiddenMap.value[item.key] === undefined) {
    showHiddenMap.value[item.key] = item;
  } 
  if (showHiddenMap.value[item.key].show === undefined || showHiddenMap.value[item.key].show === true ) {
    method = "hidden_" + String(item.key);
    item.show = false;
    showHiddenMap.value[item.key] = item;
    item.style = item.style + drakgreyColor;
  } else {
    item.show = true;
    item.style = showHiddenMap.value[item.key].style.replace(drakgreyColor, "");
    // console.log("item.style:", item.style)
    showHiddenMap.value[item.key] = item;
    method = "show_" + String(item.key);
  }
  item["method"] = method;
  emit("middleBtnClick", item)
};


  

</script>
  
<style lang="scss" scoped>
  .yqlist {
    width: 45vw;
    text-align: center;
  
    ul {
      list-style-type: none;
      margin: 0;
      padding: 0;
    }
  
    li {
      float: left;
      width: 20%;
      padding: 10px 0 0 0;
      text-align: center;
    }
  
    .yq {
      width: 5rem;
      height: 5rem;
      line-height: 100%;
      margin: 0 auto;
      position: relative;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      font-size: 1.5rem;
      font-family: electronicFont;  
      color: #fff;
      transform: translateY(var(--translate-y));
      cursor: pointer;
      z-index: 999;
  
      &:before {
        position: absolute;
        width: 100%;
        height: 100%;
        content: "";
        background: url('@/assets/img/bigscreen/img720/img1.png') center center;
        border-radius: 100px;
        background-size: 100% 100%;
        opacity: 0.5;
        left: 0;
        top: 0;
        animation: myfirst2 15s infinite linear;
        // 高亮样式
        // border: 1px solid red; // 添加红色边框来高亮
      }
  
      &:after {
        position: absolute;
        width: 86%;
        background: url('@/assets/img/bigscreen/img720/img2.png') center center;
        border-radius: 100px;
        background-size: 100% 100%;
        opacity: 0.5;
        height: 86%;
        content: "";
        left: 7%;
        top: 7%;
        animation: myfirst 15s infinite linear;
        // 高亮样式
        // border: 1px solid blue; // 添加蓝色边框来高亮
      }
    }
  }
  
  @keyframes myfirst {
    to {
      transform: rotate(-360deg);
    }
  }
  
  @keyframes myfirst2 {
    to {
      transform: rotate(360deg);
    }
  }
</style>