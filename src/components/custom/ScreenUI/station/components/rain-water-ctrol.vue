<template>
    <div class="water-rain-div-class">
        <el-button class="water-rain-btn-class"  @click="rainWaterCtrol">雨水信息表</el-button>
    </div>
</template>

<script setup>

const emit = defineEmits([ "rainWaterCtrol"]);

const rainWaterCtrol = () =>{
    emit("rainWaterCtrol")
}

</script>

<style lang="css" scoped>
.water-rain-div-class {
    background-color: rgb(89,113,145, 0.5); /* 白色背景，半透明 */
    /* opacity: 0.5; 设置为半透明 */
   
}

.water-rain-btn-class {
    /* opacity: 0.5; */
   
    background-color: rgb(89,113,145, 0.5);
    /* background-color: #597191; */
    color: #ffffff;
}
</style>