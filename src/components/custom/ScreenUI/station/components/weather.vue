<template>
    <div class="weather-div">
        <div class="weather-btn"  @click="showWeatherWrap">
           <img :src="currentIcon"  alt style="height:25px;width:25px;" />
           <span class="btn-text">12℃</span>
        </div>
        
        <div class="weather-dialog"  ref="weatherDialogRef"  v-if="visible">
            <!-- 弹窗选择天气 -->
             <div class="header">
                <img :src="currentIcon"   alt style="height:20px;width:20px;"/>
                <span class="btn-text">{{ currentName }}</span>
                <span class="close-text"  @click="closeWeatherWrap">X</span>
             </div>
             <div class="weather-icon">
                <div class="item" v-for="(item, index) in iconList"  @click="clickChangeWeather(item)">
                    <img :src="item.iconImg" alt style="height:20px;width:20px;cursor: pointer;">
                    <p class="item-text">{{ item.name }}</p>
                </div>
             </div>
        </div>
    </div>
</template>


<script setup>
import { ref } from "vue";
import weatherDefaultPng from "./icon/1.png"; 
import ClearSkies from "./icon/1.png"; // 晴天
import Cloudy from "./icon/5.png";  // 多云
import Foggy from "./icon/4.png";   // 雾
import Overcast from "./icon/9.png"; // 阴天
import RainLight from "./icon/2.png"; // 小雨
import Rain from "./icon/6.png";  // 中雨
import RainThundestor from "./icon/10.png"; // 雷阵雨
import SandDustCalm from "./icon/12.png";   // 霾
import SandDustStorm from "./icon/8.png";  // 沙尘暴
import Snow from "./icon/7.png"; //  中雪
import SnowLight from "./icon/3.png"; // 小雪
import SnowBlizzand from "./icon/11.png"; // 大雪

const currentIcon = ref(weatherDefaultPng);
const currentName = ref("晴");
const visible = ref(false);
const weatherDialogRef = ref(null);

const emit = defineEmits(["clickChangeWeather"]);

const iconList = [
    { name: "晴天",  iconImg: ClearSkies,  key: "clear_skies"},
    { name: "多云", iconImg: Cloudy, key:"cloudy"},
    { name: "阴天", iconImg: Overcast, key: "overcast"},
    { name: "雾",   iconImg: Foggy, key: "foggy"},
    { name: "小雨", iconImg: RainLight, key: "rain_light"},
    { name: "中雨", iconImg: Rain, key: "rain"},
    { name: "大雨", iconImg: RainThundestor, key:"rain_thunderstor"},
    { name: "霾",   iconImg: SandDustCalm,   key: "sand_dust_calm"},
    { name: "沙尘暴", iconImg: SandDustStorm, key: "sand_dust_storm"},
    { name: "小雪",  iconImg: SnowLight,  key: "snow_light"},
    { name: "中雪",  iconImg: Snow,       key: "snow"},
    { name: "大雪",  iconImg: SnowBlizzand, key: "snow_blizzard"},
]


const clickChangeWeather = (item) => {
    // console.log(item)
    currentIcon.value = item.iconImg;
    currentName.value = item.name;
    emit("clickChangeWeather", item);
    visible.value = false;
}

const showWeatherWrap = () => {
    visible.value = true;
}

const closeWeatherWrap = () => {
    visible.value = false;
}

</script>



<style scoped lang="scss">


.btn-text{
    margin-left: 5px;
    font-size: 14px;
    font-family: 黑体;
    font-weight: normal;
    font-style: normal;
    color: rgb(255, 255, 255);
    background-color: rgba(255, 255, 255, 0);
    white-space: nowrap;
}


.weather-btn {
    position: absolute;
    width: 32;
    height: 25;
    display: flex;
    align-items: center; /* 垂直居中 */
    justify-content: center; /* 水平居中（如果需要） */
    cursor: pointer;
}

.weather-dialog {
    top: 45px;
    right: 45px;
    position: absolute;
    background-color: rgba(0, 0, 0, 0.7);
    color: #fff;
    width: 190px;
    height: 220px;
    // cursor: pointer;
    margin: 3px;

    .header {
        position: relative;
        margin-left: 20px;
        padding-top: 7px;
        height: 25;
        display: flex;
        align-items: center; /* 垂直居中 */
        // justify-content: center; /* 水平居中（如果需要） */
        .close-text {
            position: absolute;
            top: 10px;
            right: 10px;
            cursor: pointer;
            font-size: 14px;
        }
    }
}


.weather-icon {
    padding-left: 20px;
    padding-top: 10px;
    width: 100%;
    display: flex;
    flex-wrap: wrap;
}
.item {
    margin-top: 20px;
    // flex: 0 0 calc((100% - 10px)/3);
    width: calc((100% - 10px) / 3);
    height: 42px;
    /* 间隙为5px */
    margin: 0 5px 5px 0;
    
    .item-text {
        cursor: pointer;
        font-size: 12px;
    }
}
.item:nth-child(3n) {
    /* 去除第3n个的margin-right */
    margin-right: 0;
}

</style>