<template>
    <div class="screen-bg-div">
        <div class="header-class">
            <!-- 上面天气 -->
             <div class="header-class-left">
                <!-- 左侧 -->
                <CurTime />
             </div>
             <div class="header-class-right">
                <div class="right-weather">
                    <Weather  @clickChangeWeather="clickChangeWeather"   v-if="showWeather" />
                </div>
                <div class="right-fullscreen">
                    <Fullscreen />
                </div>
             </div>
        </div>
        <div class="screen-bg">
            <div class="transparent-foreground"></div>
            <div class="header-title">
                <span class="header-title-text">{{ title }}</span>
            </div>
            <div class="left-card">
                <div class="overview" v-if="cardContents.length > 0">
                    <div class="overview-title">
                        <span class="left-card-overview-title">{{ cardContents[0].title  }}</span>
                    </div>
                    <div class="overview-detail">
                        <!-- <Summary ></Summary> -->
                         <component :is="cardContents[0].content"></component>
                    </div>
                </div>
                <div class="water" v-if="cardContents.length > 1">
                    <div class="water-title">
                        <span class="left-card-water-title">{{ cardContents[1].title }}</span>
                    </div>
                    <div class="water-detail">
                        <!-- <Water /> -->
                        <component :is="cardContents[1].content"></component>
                    </div>
                </div>
                <div class="rain" v-if="cardContents.length > 2">
                    <div class="rain-title">
                        <span class="left-card-rain-title">{{ cardContents[2].title }}</span>
                    </div>
                    <div class="rain-detail">
                        <!-- <Rain /> -->
                        <component :is="cardContents[2].content"></component>
                    </div>
                </div>
            </div>
            <div class="right-card">
                <!-- 1. 预警  -->
                <div class="warning" v-if="cardContents.length > 3">
                    <div class="warning-title">
                        <span class="warning-title-span">{{ cardContents[3].title }}</span>
                    </div>    
                    <div class="warning-detail">
                        <!-- <WarnSummary /> -->
                        <component :is="cardContents[3].content"></component>
                    </div>
                </div>
                <!-- 2. 预警消息 -->
                <div class="warning-list" v-if="cardContents.length > 4">
                    <div class="warning-list-title">
                        <span class="warning-list-title-span">{{ cardContents[4].title }}</span>
                    </div>    
                    <div class="warning-list-detail">
                        <!-- <WarnList /> -->
                        <component :is="cardContents[4].content"></component>
                    </div>
                </div>
                <!-- 3. 视频监控 -->
                <div class="video" v-if="cardContents.length > 5">
                    <div class="video-title">
                        <span class="video-title-span">{{ cardContents[5].title }}</span>
                    </div>    
                    <div class="video-detail">
                         <!-- <Mp4Video /> -->
                         <component :is="cardContents[5].content"></component> 
                    </div>
                </div>
            </div>
        </div>
        <!-- 中间下方的button按钮 -->
        <div class="midddle-button-list">
            <MiddleButtonList   :btnInfoList="btnInfoList"  @middleBtnClick="middleBtnClick" />
        </div>

        <!-- 控制瀑布的按钮 -->
         <div class="waterfall-button">
            <Waterfall  @waterfallCtrol="waterfallCtrol"  v-if="showWaterfallCtrol" />
            <RainWaterCtrol  @rainWaterCtrol="rainWaterCtrol" v-if="showRainWaterCtrol" />
         </div>

         <div class="screen-bottom">
            <stationFooter 
                ref="footerRef"
                v-model:leftBtnList="leftBtnList"  
                v-model:rightBtnList="rightBtnList"
                @btnClick="btnClick"
            ></stationFooter>
         </div>
    </div>
</template>

<script setup>
import { onBeforeUnmount, onMounted, ref, markRaw, watch, nextTick } from "vue";
import Summary from './components/summary.vue';
import Rain from "./components/rain.vue";
import Water from './components/water.vue';
import WarnSummary from "./components/warn-summary.vue";
import BlockVideo from './components/block-video.vue';
import WarnList from './components/warn-list.vue';
import MiddleButtonList from './components/middle-button-list.vue';
import Mp4Video from "./components/mp4-video.vue";
import Weather from "./components/weather.vue";
import Waterfall from "./components/waterfall.vue";
import RainWaterCtrol from "./components/rain-water-ctrol.vue";
import CurTime from "./components/curTime.vue";
import Fullscreen from "../fullscreen.vue";
import stationFooter from "./components/stationFooter.vue";


const emit = defineEmits(["middleBtnClick", "screenClick", "clickChangeWeather", "waterfallCtrol", "rainWaterCtrol", "btnClick"]);


const props = defineProps({
    btnInfoList: {
        type: Array,
        default:[],
    },
    title: {
        type: String,
        default: "智慧水利大屏演示"
    },
    showWaterfallCtrol: {
        type: Boolean,
        default: false,
    },
    showRainWaterCtrol: {
        type: Boolean,
        default: false,
    },
    showWeather: {
        type: Boolean,
        default: false,
    },  
    cardContents: {
        type: Array,
        default: [],
    },
    leftBtnList: {
        type: Array,
        default: [],
    },
    rightBtnList:  {
        type: Array,
        default: [],
    },
})


const leftBtnList = ref(props.leftBtnList)  // 前面两个在左 标
const rightBtnList = ref(props.rightBtnList) // 后面两个在右边
const cardContents = ref(props.cardContents)
const showWaterfallCtrol = ref(props.showWaterfallCtrol)
const showRainWaterCtrol = ref(props.showRainWaterCtrol)
const showWeather = ref(props.showWeather)

const title = ref(props.title);

const btnInfoList = ref(props.btnInfoList);

const footerRef = ref(null)

// 如果没有给左右两侧的卡片内容，则默认填充demo的卡片数据
const initDemoCardContents = () => {
    if (cardContents.value.length === 0) {
        cardContents.value = [
            { id: 1, title: "测站总数",  content: markRaw(Summary) },
            { id: 2, title:"水位数据", content: markRaw(Water) }, // 可以是文本、HTML字符串或对象等
            { id: 3, title: "雨量数据",  content: markRaw(Rain)  },
            { id: 4, title: "预警总览", content: markRaw(WarnSummary) },
            { id: 5, title: "预警消息", content: markRaw(WarnList) },
            { id: 6, title:"视频监控", content: markRaw(Mp4Video) },
        ]
    }
    if (props.leftBtnList.length === 0) {
        console.log("leftBtnList:", leftBtnList.value)
        leftBtnList.value = [
            { id: 1, position: "left",  name:"测站管理",  key:"station_manager" },
            { id: 2, position: "left",  name:"数据服务",  key:"data_service"},
        ]
    }
    if(props.rightBtnList.length === 0) {
        rightBtnList.value = [
            { id: 3, position:"right",  name: "三维模型", key:"three_model" },
            { id: 4, position: "right", name: "设备管理", key:"device_manager"},
            // { id: 2, position: "right", name: "桥梁测点", key: "new_three_model"}
        ]
    }
}

const btnClick = (data) => {
    emit("btnClick", data);
}

const waterfallCtrol = (data) => {
    emit("waterfallCtrol", data);
}

const middleBtnClick = (data) => {
    emit("middleBtnClick", data)
}

const rainWaterCtrol = () =>{
    emit("rainWaterCtrol")
}

const screenClick = () => {
    emit("screenClick")
}

const clickChangeWeather = (item) => {
    emit("clickChangeWeather", item)
}

const footerBtnClick = (item) => {
    nextTick(() => {
        footerRef.value.footerBtnClick(item);
    })
}

onMounted(() => {
    initDemoCardContents()
    document.addEventListener("click", screenClick)
})

onBeforeUnmount(() => {
    document.removeEventListener("click", screenClick);
})


watch(
  () => props.leftBtnList,
  (valueList) => {
    if (valueList !== undefined && valueList !== "") {
       leftBtnList.value = valueList;
    }
  }
)

watch(
  () => props.rightBtnList,
  (valueList) => {
    if (valueList !== undefined && valueList !== "") {
       rightBtnList.value = valueList;
    }
  }
)

//导出方法
defineExpose({
  footerBtnClick,
});

</script>


<style lang="scss" scoped>

.screen-bg-div {
    width: 100%;
    height: 100%;
    z-index: 9;
    position: relative;
    pointer-events: none; /* 去掉当前div鼠标点击,关键属性 */
    overflow: hidden;

     background-image: url('@/assets/img/bigscreen/img720/virtualbg.png');
    /* 设置背景图片的尺寸 */
    background-size: 100% 100%;
    /* 设置背景图片的位置 */
    background-position: center;
    /* 设置背景图片是否重复 */
    background-repeat: no-repeat;
    /* 设置宽度和高度 */
    width: 100%;
    height: 100%;
}

.header-class {
    position: absolute; 
    top: 0; // 确保它紧贴页面顶部
    left: 0; // 确保它从左边开始
    right: 0; // 确保它延伸到右边
    z-index: 102; // 确保它的层级足够高
    display: flex; // 使用Flexbox布局
    justify-content: space-between; // 左右对齐子元素
    align-items: center; // 垂直居中子元素
    pointer-events: all; // 恢复鼠标事件，允许与子元素交互
    // padding: 10px; // 可选：为内部元素留出空间

    .header-class-left {
        float: left; // 使用浮动来排列左右两侧元素
        margin-top: 10px; // 根据需要调整上下间距
    }

    .header-class-right {
        width: 150px;
        float: right; // 使用浮动来排列左右两侧元素
        // margin-top: 5px; // 根据需要调整上下间距
        display: flex;
        gap: 20px; // 设置左右组件之间的间距
        align-items: center;

        .right-fullscreen {
            display: flex;
            align-items: center;
            margin-top: 20px;
            margin-right: 30px;
        }

        .right-weather {
            display: flex;
            align-items: center;
            margin-right: auto; // 让.weather尽可能靠右，但保持与.fullscreen的距离
        }
    }
}

.screen-bg {
    width: 100%;
    height: 100%;
    opacity: 1;
    z-index: 10;
    position: absolute;
    pointer-events: none; /* 去掉当前div鼠标点击,关键属性 */
    overflow: hidden;
    // display: none;
    .transparent-foreground {
        /* 设置背景图片 */
        // background-image: url('@/assets/img/bigscreen/img720/virtualbg.png');
        background-image: url('/assets/screen/screenbg.png');
        /* 设置背景图片的尺寸 */
        background-size: 100% 100%;
        /* 设置背景图片的位置 */
        background-position: center;
        /* 设置背景图片是否重复 */
        background-repeat: no-repeat;
        /* 设置宽度和高度 */
        width: 100%;
        height: 100%;
        // width: 98vw;
        // height: 89vh;
        /* 设置透明度 */
        opacity: 1;
        /* 如果你想让这个div作为整个页面的前景，可以设置position和z-index */
        position: absolute;
        z-index: 0;
        // pointer-events: none; /* 去掉当前div鼠标点击,关键属性 */
    }

    // 
    .header-title {
        /* 设置背景图片 */
        background-image: url('@/assets/img/bigscreen/img720/header.png');
        /* 设置背景图片的尺寸 */
        background-size: cover;
        /* 设置背景图片的位置 */
        background-position: center;
        /* 设置背景图片是否重复 */
        background-repeat: no-repeat;
        /* 设置宽度和高度 */
        // display: block;
        width: 100%; // 99vw;
        height: 70px;
        // height: 89vh;
        /* 设置透明度 */
        opacity: 1;
        /* 如果你想让这个div作为整个页面的前景，可以设置position和z-index */
        position: absolute;
        z-index: 99;
        pointer-events: none; /* 去掉当前div鼠标点击,关键属性 */
        // span居中
        text-align: center;
        

        // 字体样式
        .header-title-text {
            // left: 50%;
            transform: translate(-50%);
            // font-weight: 400;
            font-size: 30px;
            line-height: 55px;
            letter-spacing: .1rem;

            font-family: YouShe, serif;
            font-style: normal;
            font-weight: 400;
            color: #ffffff;
        }
    }

    .left-card {
        margin-top: 4%; //58px;
        margin-left: 45px;
        width: 15vw;
        height:  82%; // 78vh;
        overflow: hidden;
        display: flex;
        flex-direction: column;


        background-image: url('@/assets/img/bigscreen/img720/card-bg.png');
        /* 设置背景图片的尺寸 */
        background-size: cover;
        /* 设置背景图片的位置 */
        // background-position: center;
        /* 设置背景图片是否重复 */
        background-repeat: no-repeat;
        /* 设置透明度 */
        opacity: 1;
        /* 如果你想让这个div作为整个页面的前景，可以设置position和z-index */
        position: absolute;
        z-index: 101;
        pointer-events: none; /* 去掉当前div鼠标点击,关键属性 */

        // 左侧总览
        .overview {
            width: 100%;
            height: 27%;
            // height: 26vh;
            padding-top: 5px; // 距离上面高度
            // pointer-events: none; /* 去掉当前div鼠标点击,关键属性 */

            overflow: hidden;
            display: flex;
            flex-direction: column;

            .overview-title {
                width: 100%;
                height: 6vh;
                background-image: url('@/assets/img/bigscreen/img720/card-header.png');
                /* 设置背景图片是否重复 */
                background-repeat: no-repeat;
                background-position: center;
                background-size: 90%; /* 宽度为父元素的50% */
                text-align: center;  // 设置下面的span元素居中

                .left-card-overview-title {
                    transform: translate(-50%);
                    font-size: 18px;
                    line-height: 55px;
                    letter-spacing: .1rem;
                    font-family: YouShe, serif;
                    font-style: normal;
                    font-weight: 200;
                    color: #ffffff;
                }
            }
            .overview-detail {
                z-index: 101;
                width: 100%;
                height: 20vh;
                overflow: hidden;
                display: flex;
                flex-direction: column;
            }
        }

        .water {
            width: 100%;
            height: 26vh;
            padding-top: 5px; // 距离上面高度
            pointer-events: all; /* 还原div鼠标点击,关键属性 */

            .water-title {
                width: 100%;
                height: 6vh;
                background-image: url('@/assets/img/bigscreen/img720/card-header.png');
                /* 设置背景图片是否重复 */
                background-repeat: no-repeat;
                background-position: center;
                background-size: 90%; /* 宽度为父元素的50% */
                text-align: center;  // 设置下面的span元素居中

                .left-card-water-title {
                    transform: translate(-50%);
                    font-size: 18px;
                    line-height: 55px;
                    letter-spacing: .1rem;
                    font-family: YouShe, serif;
                    font-style: normal;
                    font-weight: 200;
                    color: #ffffff;
                }
            }
            .water-detail {
                z-index: 101;
                width: 100%;
                height: 20vh;
            }
        }

        .rain {
            width: 100%;
            height: 26vh;
            padding-top: 5px; // 距离上面高度
            pointer-events: all; /* 还原div鼠标点击,关键属性 */

            .rain-title {
                width: 100%;
                height: 6vh;
                background-image: url('@/assets/img/bigscreen/img720/card-header.png');
                /* 设置背景图片是否重复 */
                background-repeat: no-repeat;
                background-position: center;
                background-size: 90%; /* 宽度为父元素的50% */
                text-align: center;  // 设置下面的span元素居中

                .left-card-rain-title {
                    transform: translate(-50%);
                    font-size: 18px;
                    line-height: 55px;
                    letter-spacing: .1rem;
                    font-family: YouShe, serif;
                    font-style: normal;
                    font-weight: 200;
                    color: #ffffff;
                }
            }
            .rain-detail {
                z-index: 101;
                width: 100%;
                height: 20vh;
            }
        }
    }

    // 右侧的列表
    .right-card {
        right: 45px;
        width: 15vw;
        margin-top: 4% ; //60px;
        height: 82%; // 78vh;
        overflow: hidden;
        display: flex;
        flex-direction: column;

        background-image: url('@/assets/img/bigscreen/img720/card-bg.png');
        /* 设置背景图片的尺寸 */
        background-size: cover;
        /* 设置背景图片的位置 */
        // background-position: center;
        /* 设置背景图片是否重复 */
        background-repeat: no-repeat;
        /* 设置透明度 */
        opacity: 1;
        /* 如果你想让这个div作为整个页面的前景，可以设置position和z-index */
        position: absolute;
        z-index: 101;
        // pointer-events: none; /* 去掉当前div鼠标点击,关键属性 */

        .warning {
            width: 100%;
            height: 26vh;
            padding-top: 5px; // 距离上面高度
            // pointer-events: none; /* 去掉当前div鼠标点击,关键属性 */

            /* 让div固定大小 */
            overflow: hidden;
            display: flex;
            flex-direction: column;

            .warning-title {
                width: 100%;
                height: 6vh;
                background-image: url('@/assets/img/bigscreen/img720/card-header.png');
                /* 设置背景图片是否重复 */
                background-repeat: no-repeat;
                background-position: center;
                background-size: 90%; /* 宽度为父元素的50% */
                text-align: center;  // 设置下面的span元素居中

                .warning-title-span {
                    transform: translate(-50%);
                    font-size: 18px;
                    line-height: 55px;
                    letter-spacing: .1rem;
                    font-family: YouShe, serif;
                    font-style: normal;
                    font-weight: 200;
                    color: #ffffff;
                }
            }
            .warning-detail {
                z-index: 103;
                width: 100%;
                height: 20vh;
                // overflow: hidden;
                display: flex;
                flex-direction: column;
            }
        }

        .warning-list {
            width: 100%;
            height: 26vh;
            padding-top: 5px; // 距离上面高度
            // pointer-events: none; /* 去掉当前div鼠标点击,关键属性 */

            .warning-list-title {
                width: 100%;
                height: 6vh;
                background-image: url('@/assets/img/bigscreen/img720/card-header.png');
                /* 设置背景图片是否重复 */
                background-repeat: no-repeat;
                background-position: center;
                background-size: 90%; /* 宽度为父元素的50% */
                text-align: center;  // 设置下面的span元素居中

                .warning-list-title-span {
                    transform: translate(-50%);
                    font-size: 18px;
                    line-height: 55px;
                    letter-spacing: .1rem;
                    font-family: YouShe, serif;
                    font-style: normal;
                    font-weight: 200;
                    color: #ffffff;
                }
            }
            .warning-list-detail {
                z-index: 101;
                width: 100%;
                height: 20vh;
            }
        }

        .video {
            width: 100%;
            height: 26vh;
            padding-top: 1px; // 距离上面高度
            // pointer-events: none; /* 去掉当前div鼠标点击,关键属性 */

            .video-title {
                width: 100%;
                height: 6vh;
                background-image: url('@/assets/img/bigscreen/img720/card-header.png');
                /* 设置背景图片是否重复 */
                background-repeat: no-repeat;
                background-position: center;
                background-size: 90%; /* 宽度为父元素的50% */
                text-align: center;  // 设置下面的span元素居中

                .video-title-span {
                    transform: translate(-50%);
                    font-size: 18px;
                    line-height: 55px;
                    letter-spacing: .1rem;
                    font-family: YouShe, serif;
                    font-style: normal;
                    font-weight: 200;
                    color: #ffffff;
                }
            }
            .video-detail {
                z-index: 999;
                width: 100%;
                height: 20vh;
            }
        }
    }
}



.midddle-button-list {
    width: 100%;
    height: 20px;
    margin-top: 70vh;
    // height: 89vh;
    /* 设置透明度 */
    opacity: 1;
    /* 如果你想让这个div作为整个页面的前景，可以设置position和z-index */
    position: absolute;
    z-index: 102;
    // pointer-events: none; /* 去掉当前div鼠标点击,关键属性 */
    // span居中
    display: flex;
    justify-content: center; /* 让容器内的内容水平居中 */
    pointer-events: all;
}


.waterfall-button {
    position: absolute;
    opacity: 1;
    left: 20vw;
    top: 80vh;
    z-index: 102;
    display: flex;
    pointer-events: all;
}


.screen-bottom{
    bottom: 0;
    width: 100%;
    flex: 0 0 5%; /* 固定高度为屏幕高度的5% */
    position: absolute; /* 确保子元素可以相对于此元素定位 */
}

</style>