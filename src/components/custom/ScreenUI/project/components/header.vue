<template>
    <div class="pro-screen-header-class">
        <div class="header-container">
            <div class="header-title">{{ props.title }}</div>
        </div>
        <div class="header-time">{{ currentTime }}</div>
        <div class="left-menu">
            <div v-for="(item, index) in leftMenus" :key="index"  class="menu-button"  @click="menuClick(item)">
                {{ item.name }}
            </div>
        </div>
        <div class="right-menu">
            <div v-for="(item, index) in rightMenus" :key="index" class="menu-button"  @click="menuClick(item)">
                {{ item.name }}
            </div>
        </div>
        <div class="right-fullscreen-class">
            <Fullscreen />
        </div>
    </div>
</template>


<script setup>
import { ref, onMounted, onBeforeUnmount, watch } from "vue";
import moment from "moment";
import Fullscreen from "../../fullscreen.vue";


const props = defineProps({
  title: {
    type: String,
    default: "智慧水利大屏演示"
  },
  menuList: {
    type: Array,
    default: [],
  }
})


const leftMenus = ref(props.menuList.slice(0, 3))
const rightMenus = ref(props.menuList.slice(3))

// console.log("left menus:", leftMenus.value)

const currentTime = ref(moment().format('YYYY-MM-DD HH:mm:ss'));


const emit = defineEmits(["menuClick"]);


const menuClick = (item) =>{
    emit("menuClick", item)
}



// 定时器，每秒更新一次当前时间
let interval;

onMounted(() => {
    // 设置定时器，每秒更新一次时间
    interval = setInterval(() => {
        currentTime.value = moment().format('YYYY-MM-DD HH:mm:ss');
    }, 1000);
})

onBeforeUnmount(() => {
    // 清除定时器，防止内存泄漏
    clearInterval(interval);
})


watch(
  () => props.menuList,
  (valueList) => {
    if (valueList !== undefined && valueList !== "") {
        leftMenus.value = valueList.slice(0, 3)
        rightMenus.value = valueList.slice(3)
    }
  }
)

</script>


<style lang="css" scoped>

.pro-screen-header-class {
    width: 100%;
    height: 100%; /* 确保不超过父元素的高度 */
    box-sizing: border-box; /* 确保内边距和边框包含在指定的宽度和高度内 */
    position: relative; /* 确保伪元素可以相对于此元素定位 */
}


.pro-screen-header-class::before{
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0; /* 使用 right 和 bottom 来确保宽度和高度是 100% */
    bottom: 0;
    box-sizing: border-box; /* 确保内边距和边框包含在指定的宽度和高度内 */
    background-image: url('/assets/screen/header/header-bg.png');
    background-repeat: no-repeat;
    /* background-size: cover;  或者使用 contain，取决于你希望的拉伸方式 */
    background-size: 100% 100%; /* 这种设置会拉伸图片以完全填充容器，但可能会导致图片变形，因为它不保证保持图片的原始比例。
    opacity: 0.9; /* 设置透明度 */
    z-index: -1; /* 确保伪元素在实际内容之下 */
}



.header-container {
    flex-grow: 1; /* 让标题容器占据剩余的空间 */
    text-align: center; /* 水平居中 .header-title */
    align-items: center; /* 垂直居中 */
}

.left-menu {
    margin-left: 15vw;
}

.right-menu {
    right: 15vw; /* 从右边开始的位置，可以根据需要调整 */
}

.left-menu, .right-menu {
    position: absolute ; /* 确保伪元素可以相对于此元素定位 */
    top: 4vh;
    align-items: center; /* 如果菜单项需要垂直居中 */
    color: #ffffff;
    display: flex; /* 使用flex布局 */
}


.menu-button {
    width: 85px;
    height: 30px;
    margin: 0 5px; /* 给每个菜单项添加一些间隔 */

    cursor: pointer;
    display: flex; /* 使用flex布局 */
    align-items: center; /* 垂直居中 */
    justify-content: center; /* 水平居中 */
    text-align: center;
    font-size: 12px;
    background-image: url("/assets/screen/header/menu-btn.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;

    pointer-events: all; /* 恢复点击事件 */
    transition: background-image 0.3s ease; /* 添加平滑过渡效果 */
}

.menu-button:hover {
    background-image: url("/assets/screen/header/menu-btn-hover.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
}

.header-title {
    margin-top: 25px;
    color: #ffffff; /* 根据背景颜色调整文本颜色 */
    font-size: 24px; /* 根据需要调整字体大小 */
    display: inline-block; /* 确保标题只占用必要的宽度 */
}

.header-time {
    position: absolute ; /* 确保伪元素可以相对于此元素定位 */
    top: 25px;
    left: 2vw;
    color: #ffffff;
    font-size: 14px; /* 根据需要调整字体大小 */
    /* background-color: rgba(37, 52, 70, 0.7); */
}



.right-fullscreen-class {
    z-index: 111;
    position: absolute;
    top: 25px;
    right: 2vw;
    color: #ffffff;
}
</style>