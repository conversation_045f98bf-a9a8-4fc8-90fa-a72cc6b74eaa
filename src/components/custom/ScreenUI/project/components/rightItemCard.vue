<template>
    <div class="title-card-class">
        <div class="item-header-class">
            <div class="header-right-three-point">
                <div class="dot first-dot"></div>
                <div class="dot second-dot"></div>
                <div class="dot three-dot"></div>
            </div>
            <div class="header-center-title">{{ props.title }}</div>
            <div class="header-left-point">
                <div class="left-circ-class"></div>
                <div class="left-point-class"></div>
            </div>
        </div>
        <!-- 动态渲染 content -->
        <div class="item-content">
            <component v-if="contentIsComponent" :is="props.content" />
            <div v-else v-html="safeContent"></div>
        </div>
       
    </div>
</template>


<script setup>
import { ref, computed } from "vue";
import DOMPurify from 'dompurify';

// const title=ref("标题")

// 定义接收到的属性
const props = defineProps({
    title: {
        type: String,
        default: "标题",
    },
    content: {
        type: [String, Object], // 支持字符串和对象（组件）
        default: "",
    }
});

// console.log("itemcard:", props.content)

// 使用 DOMPurify 清理 HTML 内容
// 数据源不可信，强烈建议引入一个 HTML 清洗库，比如 DOMPurify，来清理传入的 HTML，移除任何潜在的危险代码。
const safeContent = computed(() => DOMPurify.sanitize(props.content));

// 判断是否是字符串内容
// 判断是否是组件引用
const contentIsComponent = computed(() =>
  props.content && (typeof props.content === 'object') &&
  (typeof props.content.render === 'function' ||
   typeof props.content.setup === 'function' ||
   props.content.__vccInfo)
);

</script>


<style lang="css" scoped>
.title-card-class {
    width: 100%; /* 让卡片占满父容器的宽度 */
    height: 100%; /* 让卡片占满父容器的高度 */
    background-color:  rgba(37, 52, 70, 0.4);  /* 合并重复的背景颜色声明 */
    box-sizing: border-box; /* 确保内边距和边框包含在指定的宽度和高度内 */
    position: relative; /* 确保伪元素可以相对于此元素定位 */
}

.title-card-class::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0; /* 使用 right 和 bottom 来确保宽度和高度是 100% */
    bottom: 0;
    background-image: url('/assets/screen/titlecard/bg.png');
    background-repeat: no-repeat;
    background-size: 100% 100%; /* 或者使用 contain，取决于你希望的拉伸方式 */
    opacity: 1; /* 设置透明度 */
    mix-blend-mode: multiply; /* 设置混合模式 */
    z-index: -1; /* 确保伪元素在实际内容之下 */
}

/* 确保动态渲染的内容能够拉伸 */
.item-content {
    width: 100%;
    height: 18vh;
    flex-grow: 1;
    font-size: 14px; /* 根据需要调整 */
    color: #DCF3FF; 
    margin-top: 5px;
    padding: 0 10px 10px 10px;  /* 上，右，下，左。 顺时针方向*/
}


.item-header-class {
    position: relative; /* 确保伪元素可以相对于此元素定位 */

    display: flex;
    align-items: center; /* 垂直居中对齐 */
    justify-content: space-between; /* 水平均匀分布 */
    padding: 10px; /* 根据需要调整 */

    height: 15px;

    /* background-image: url("/assets/screen/titlecard/card-titlebg.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: center;  */
}

.item-header-class::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url("/assets/screen/titlecard/card-titlebg.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: center;
    z-index: -1; /* 确保伪元素在实际内容之下 */
    transform: scaleX(-1); /* 只翻转背景图片 */
}




.header-left-point {
    position: relative;
    display: inline-flex;
    align-items: center;
}




.left-circ-class {
    width: 30px; /* 圆圈的宽度 */
    height: 30px; /* 圆圈的高度 */
    background-image: url("/assets/screen/titlecard/circ.png");
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
    position: relative;
    animation: spin 2s linear infinite;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

.left-point-class {
    width: 20px; /* 点的宽度 */
    height: 20px; /* 点的高度 */
    background-image: url("/assets/screen/titlecard/point.png");
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%); /* 将点置于圆圈的中心 */
}

.header-center-title {
    flex-grow: 1; /* 占用剩余空间 */
    text-align: right; /* 文本居右 */
    /*color: white; /* 根据需要调整 */
    color: rgb(220, 243, 255);
    font-size: 16px; /* 根据需要调整 */
    margin-right: 10px;
}

.header-right-three-point {
    font-size: 18px; /* 根据需要调整 */
    display: flex; /* 确保内部元素按行排列 */
    align-items: center; /* 垂直居中对齐 */
    gap: 5px; /* 设置点之间的间距 */
}

.dot {
    width: 6px; /* 根据需要调整 */
    height: 6px; /* 根据需要调整 */
    border-radius: 50%; /* 确保点是圆形 */
    background-color:  #2EF0FF; /* 或者其他颜色 */

    animation-name: fade; /* 指定动画名称 */
    animation-duration: 2s; /* 动画持续时间 */
    animation-iteration-count: infinite; /* 无限循环 */
    animation-timing-function: ease-in-out; /* 动画速度曲线 */
}

.first-dot  {  animation-delay: 0s; }
.second-dot {  animation-delay: 0.5s; }
.three-dot  {  animation-delay: 1s; }

@keyframes fade {
    0% { opacity: 0; }
    30% { opacity: 0.3; }
    50% { opacity: 0.5; }
    80% { opacity: 0.8; }
    100% { opacity: 1; }
}

</style>