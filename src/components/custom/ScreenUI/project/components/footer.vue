<template>
    <div class="pro-screen-bottom-class">
        <!-- 按钮内容 -->
        <div class="bottom-container">
            <div class="left-bottom-class">
                <div v-for="(item, index) in leftBtnList"  :key="index"  class="btn-class" :class="{ active: activeButton === item.key }"   @click="btnClick(item)">
                    {{ item.name }}
                </div>
            </div>
            <div class="bottom-home-class"  @click="homeCilck">
                <div class="home-icon-class"></div>
                <div class="home-round-class"></div>
                <div class="dot top-left-dot"></div>
                <div class="dot top-right-dot"></div>
                <div class="dot bottom-left-dot"></div>
                <div class="dot bottom-right-dot"></div>
            </div>
            <div class="right-bottom-class">
                <div v-for="(item, index) in rightBtnList"  :key="index"  class="btn-class"  :class="{ active: activeButton === item.key }"   @click="btnClick(item)">
                    {{ item.name }}
                </div>
            </div>
        </div>
        
        <!-- 流光 -->
        <div class="bottom-svg-class">
            <div class="bottom-svg-line-left">
                <svg width="100%" height="100%" viewBox="0 0 721 57" fill="none" xmlns="http://www.w3.org/2000/svg" >
                    <defs>
                        <radialGradient id="radialGradient-56" cx="50%" cy="50%" fx="100%" fy="50%" r="50%">
                            <stop offset="0%" stop-color="#fff" stop-opacity="1"></stop>
                            <stop offset="100%" stop-color="#fff" stop-opacity="0"></stop>
                        </radialGradient>
                        <mask id="svgline-56">
                            <circle r="50" cx="0" cy="0" fill="url(#radialGradient-56)">
                                <animateMotion begin="0s" dur="3s" path="M1 56.6105C1 31.5123 185.586 10.0503 451.904 1.35519C458.942 1.12543 465.781 4.00883 470.505 9.22964L484.991 25.2383C487.971 28.4775 492.938 30.4201 498.254 30.4201H720.142" rotate="auto" keyPoints="0;1" keyTimes="0;1" repeatCount="indefinite"></animateMotion>
                            </circle>
                        </mask>
                    </defs>
                    <path class="path-line" d="M1 56.6105C1 31.5123 185.586 10.0503 451.904 1.35519C458.942 1.12543 465.781 4.00883 470.505 9.22964L484.991 25.2383C487.971 28.4775 492.938 30.4201 498.254 30.4201H720.142" stroke="#30DCFF" stroke-width="2" mask="url(#svgline-56)">
                    </path>
                </svg>
            </div>
            <div class="bottom-svg-line-left bottom-svg-line-right">
                <svg width="100%" height="100%" viewBox="0 0 721 57" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <defs>
                        <radialGradient id="radialGradient-57" cx="50%" cy="50%" fx="100%" fy="50%" r="50%">
                            <stop offset="0%" stop-color="#fff" stop-opacity="1"></stop><stop offset="100%" stop-color="#fff" stop-opacity="0"></stop>
                        </radialGradient>
                        <mask id="svgline-57">
                            <circle r="50" cx="0" cy="0" fill="url(#radialGradient-57)">
                                <animateMotion begin="0s" dur="3s" path="M1 56.6105C1 31.5123 185.586 10.0503 451.904 1.35519C458.942 1.12543 465.781 4.00883 470.505 9.22964L484.991 25.2383C487.971 28.4775 492.938 30.4201 498.254 30.4201H720.142" rotate="auto" keyPoints="0;1" keyTimes="0;1" repeatCount="indefinite"></animateMotion>
                            </circle>
                        </mask>
                    </defs>
                    <path class="path-line" d="M1 56.6105C1 31.5123 185.586 10.0503 451.904 1.35519C458.942 1.12543 465.781 4.00883 470.505 9.22964L484.991 25.2383C487.971 28.4775 492.938 30.4201 498.254 30.4201H720.142" stroke="#30DCFF" stroke-width="2" mask="url(#svgline-57)"></path>
                </svg>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref } from "vue";

const props = defineProps({
  leftBtnList: {
    type: Array,
    default: [],
  },
  rightBtnList: {
    type: Array,
    default:[],
  }
})

const emit = defineEmits(["btnClick"]);

const leftBtnList = ref(props.leftBtnList);
const rightBtnList = ref(props.rightBtnList);

const activeButton = ref("");

const btnClick = (item) =>{
    if (activeButton.value === item.key) {
        activeButton.value = null;
    } else {
        activeButton.value = item.key;
    }
    console.log("active button value:", activeButton.value)
    emit("btnClick", item)
}

const homeCilck = () =>{
    let item = { id:"home-btn-id",  name:"home", key: "home" }
    activeButton.value = item.key;
    emit("btnClick", item)
}

</script>


<style lang="css" scoped>

.pro-screen-bottom-class {
    width: 100%;
    height: 100%; /* 确保不超过父元素的高度 */
    box-sizing: border-box; /* 确保内边距和边框包含在指定的宽度和高度内 */
    position: relative; /* 确保伪元素可以相对于此元素定位 */

    display: flex; 
    justify-content: center; /* 水平居中 .bottom-container */
    align-items: center; /* 垂直居中 .bottom-container */
    pointer-events: all; /* 恢复点击事件 */
}


.pro-screen-bottom-class::before{
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0; /* 使用 right 和 bottom 来确保宽度和高度是 100% */
    bottom: 0;
    box-sizing: border-box; /* 确保内边距和边框包含在指定的宽度和高度内 */
    background-image: url('/assets/screen/bottom/bottom-menu-bg.png');
    background-repeat: no-repeat;
    /* background-size: cover; 或者使用 contain，取决于你希望的拉伸方式 */
    background-size: 100% 100%; /* 这种设置会拉伸图片以完全填充容器，但可能会导致图片变形，因为它不保证保持图片的原始比例。*/
    opacity: 0.9; /* 设置透明度 */
    z-index: -1; /* 确保伪元素在实际内容之下 */
}




.bottom-container {
    width: 45vw; /* 设置为 45vw */
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 10px 15px 10px;  /* 上边 | 右边 | 下边 | 左边 , 给左右留出一点空间 */
}

.left-bottom-class {
    flex: 1;
    display: flex;
    justify-content: flex-end;
    margin-right: 5px;
}
.right-bottom-class {
    flex: 1;
    display: flex;
}

.btn-class {
    margin-left: 5px;
    width:  5vw; /* 100px; /* 根据需要调整 */
    height: 30px; /* 根据需要调整 */
    cursor: pointer;
    background-image: url("/assets/screen/bottom/bottom-menu-btn.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    
    opacity: 1;
    z-index: 1;
    display: flex; /* 让按钮内的文本居中 */
    align-items: center; /* 垂直居中文本 */
    justify-content: center; /* 水平居中文本 */
    color: white; /* 根据背景色选择对比度高的颜色 */
    font-size: 12px; /* 根据需要调整 */
    transition: background-image 0.3s ease; /* 添加平滑过渡效果 */
}

/* 鼠标悬停时的背景图片 */
.btn-class:hover, .btn-class.active {
    background-image: url("/assets/screen/bottom/bottom-menu-btn-hover.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
}


.bottom-home-class {
    cursor: pointer;
    width: 50px; /* 根据需要调整宽度 */
    height: 50px; /* 根据需要调整高度 */
    position: relative; /* 确保子元素可以相对于此元素定位 */
    display: inline-block; /* 确保作为一个整体块级元素 */
    margin-bottom: 15px;
}

.home-home-class:hover {
    filter: brightness(3); /* 增加亮度 */
}



.home-round-class {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1; /* 确保 home-round-class 在 home-icon-class 下方 */
}

.home-round-class:hover {
    filter: brightness(3); /* 增加亮度 */
}

.home-round-class::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('/assets/screen/bottom/home_round.png');
    background-repeat: no-repeat;
    background-size: cover;
    opacity: 1; /* 设置透明度 */
    transform-origin: center; /* 设置旋转中心点 */
    z-index: -1; /* 确保伪元素在 .home-round-class 内容的下方 */
    animation: rotate 10s linear infinite; /* 应用动画 */
}


/* 定义关键帧动画 */
@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}


.home-icon-class {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%); /* 精确居中 */
    width: 60%; /* 根据需要调整图标大小 */
    height: 60%; /* 根据需要调整图标大小 */
    background-image: url('/assets/screen/bottom/home_icon.png');
    background-repeat: no-repeat;
    background-size: contain; /* 确保图标按比例缩放 */
    opacity: 1; /* 设置透明度 */
    z-index: 2; /* 确保 home-icon-class 在 home-round-class 上方 */
}

.home-icon-class:hover {
    filter: brightness(3); /* 增加亮度 */
}

.dot {
    position: absolute;
    width: 6px; /* 圆点直径 */
    height: 6px; /* 圆点直径 */
    border-radius: 50%; /* 确保是圆形 */
    background-color: #2EF0FF; /* 圆点颜色 */
    opacity: 0.9;
    z-index: 3; /* 确保圆点在最上方 */

    animation-name: fade; /* 指定动画名称 */
    animation-duration: 2s; /* 动画持续时间 */
    animation-iteration-count: infinite; /* 无限循环 */
    animation-timing-function: ease-in-out; /* 动画速度曲线 */
}



.top-left-dot { top: 0; left: 0; }
.top-right-dot { top: 0; right: 0; animation-delay: 1s; }
.bottom-left-dot { bottom: 0; left: 0; animation-delay: 0.3s; }
.bottom-right-dot { bottom: 0; right: 0; animation-delay: 0.7s; }

@keyframes fade {
    0% { opacity: 0; }
    30% { opacity: 0.3; }
    50% { opacity: 0.5; }
    80% { opacity: 0.8; }
    100% { opacity: 1; }
}



/* 飞线流光 */
.bottom-svg-class {
}

.bottom-svg-line-left, .bottom-svg-line-right {
    position: absolute;
    right: 50%;
    /* width: 721px; */
    width: 37.55vw;
    height: 57px;
    margin-right: -5px;
    bottom: -21px;
}
.bottom-svg-line-right {
    transform: scaleX(-1);
    left: 50%;
    right: inherit;
    margin-right: inherit;
    margin-left: -5px;
}

/* 使 SVG 响应式 */
.bottom-svg-line-left svg,
.bottom-svg-line-right svg {
    width: 100%;
    height: 100%;
}

</style>