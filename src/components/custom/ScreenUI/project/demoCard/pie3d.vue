<template>
    <div class="chart-container border">
        <div ref="echartDivRef" class="echart-div"></div>
    </div>
</template>

<script setup>
import { ref , onMounted, onBeforeUnmount } from "vue";
import * as echarts from 'echarts';
import "echarts-gl";

const echartDivRef = ref(null);


const titleNumValue = ref("30%");
const titleName = ref("");

// 使用3d饼图需要引入echarts-gl    import "echarts-gl";
function getParametricEquation(startRatio, endRatio, isSelected, isHovered, k, h) {
   let scaleFactor = 10; // 调整这个值以适应你的需求

   // 计算
   let midRatio = (startRatio + endRatio) / 2;

   let startRadian = startRatio * Math.PI * 2;
   let endRadian = endRatio * Math.PI * 2;
   let midRadian = midRatio * Math.PI * 2;
   isSelected = false;
   k = typeof k !== 'undefined' ? k : 1 / 3;
   
   let offsetX = isSelected ? Math.sin(midRadian) * 0.1 : 0;
   let offsetY = isSelected ? Math.cos(midRadian) * 0.1 : 0;
   let hoverRate = isHovered ? 1.05 : 1;
   return {
      u: {
         min: -Math.PI,
         max: Math.PI * 3,
         step: Math.PI / 32,
      },

      v: {
         min: 0,
         max: Math.PI * 2,
         step: Math.PI / 20,
      },

      x: function (u, v) {
         if (u < startRadian) {
            return offsetX + Math.cos(startRadian) * (1 + Math.cos(v) * k) * hoverRate  * scaleFactor;
         }
         if (u > endRadian) {
            return offsetX + Math.cos(endRadian) * (1 + Math.cos(v) * k) * hoverRate * scaleFactor;
         }
         return offsetX + Math.cos(u) * (1 + Math.cos(v) * k) * hoverRate * scaleFactor;
      },

      y: function (u, v) {
         if (u < startRadian) {
            return offsetY + Math.sin(startRadian) * (1 + Math.cos(v) * k) * hoverRate * scaleFactor;
         }
         if (u > endRadian) {
            return offsetY + Math.sin(endRadian) * (1 + Math.cos(v) * k) * hoverRate * scaleFactor;
         }
         return offsetY + Math.sin(u) * (1 + Math.cos(v) * k) * hoverRate * scaleFactor;
      },

      z: function (u, v) {
         if (u < -Math.PI * 0.5) {
            return Math.sin(u);
         }
         if (u > Math.PI * 2.5) {
            return Math.sin(u) * h * 0.1;
         }
         return Math.sin(v) > 0 ? 1 * h * 0.1 : -1;
      },
   };
}

function getPie3D(pieData, internalDiameterRatio) {
   let series = [];
   let sumValue = 0;
   let startValue = 0;
   let endValue = 0;
   let legendData = [];
   let k =
      typeof internalDiameterRatio !== 'undefined'
         ? (1 - internalDiameterRatio) / (1 + internalDiameterRatio)
         : 1 / 3;
   for (let i = 0; i < pieData.length; i++) {
      sumValue += pieData[i].value;

      let seriesItem = {
         name: typeof pieData[i].name === 'undefined' ? `series${i}` : pieData[i].name,
         type: 'surface',
         parametric: true,
         wireframe: {
            show: false,
         },
         pieData: pieData[i],
         pieStatus: {
            selected: false,
            hovered: false,
            k: 1 / 10,
         },
      };
      if (typeof pieData[i].itemStyle != 'undefined') {
         let itemStyle = {};
         typeof pieData[i].itemStyle.color != 'undefined' ? (itemStyle.color = pieData[i].itemStyle.color) : null;
         typeof pieData[i].itemStyle.opacity != 'undefined'
            ? (itemStyle.opacity = pieData[i].itemStyle.opacity)
            : null;

         seriesItem.itemStyle = itemStyle;
      }
      series.push(seriesItem);
   }
   for (let i = 0; i < series.length; i++) {
      endValue = startValue + series[i].pieData.value;

      series[i].pieData.startRatio = startValue / sumValue;
      series[i].pieData.endRatio = endValue / sumValue;
      series[i].parametricEquation = getParametricEquation(
         series[i].pieData.startRatio,
         series[i].pieData.endRatio,
         false,
         false,
         k,
         series[i].pieData.value
      );

      startValue = endValue;

      legendData.push(series[i].name);
   }

   let option = {
        // backgroundColor: '#163561',
      legend: {
         textStyle: {
            color:'#FFFFFF',
            fontSize: 12,
         },
         orient: 'vertical',
         top: '30vh',
         right: '30vw',
         data: legendData,
         formatter: (params) => {
            return params;
         },
      },
      title: [
            {
                left: '95vw',
                top: '45vh',
                text: titleNumValue.value,
                textStyle: {
                    color: '#fff',
                    fontSize: 12,
                },
            },
            {
                left: "95vw",
                top: '65vh',
                text: titleName.value,
                textStyle: {
                    color: '#fff',
                    fontSize: 12,
                },
            },
        ],
      xAxis3D: {},
      yAxis3D: {},
      zAxis3D: {},
      grid3D: {
         viewControl: {
            autoRotate: false,
         },
         left: '1px',
         width: '60%',
         show: false,
         boxHeight: 20,
      },
      series: series,
   };
   return option;
}


const optionList = ref(
   [
            {
                name: '上海',
                value: 134,
                itemStyle: {
                    color: '#99D3F3',
                },
            },
            {
                name: '北京',
                value: 156,
                itemStyle: {
                    color: '#007AFF',
                },
            },
            {
                name: '深圳',
                value: 127,
                itemStyle: {
                    color: '#2acf81',
                },
            },
            {
                name: '南京',
                value: 150,
                itemStyle: {
                    color: '#1F9AA7',
                },
            },
            {
                name: '武汉',
                value: 110,
                itemStyle: {
                    color: '#F5B64C',
                },
            },
      ]
)


const getOption  = () => {
    let option = getPie3D( optionList.value,  0.7);
    return option
}

let myChart;
const initChart = () => {
    if (myChart != null && myChart != "" && myChart != undefined) {
        myChart.dispose();
    }
    myChart = echarts.init(echartDivRef.value);
    let option = getOption();
    // 使用刚指定的配置项和数据显示图表。
    myChart.setOption(option);
}




const resize = () => {
  try{
    nextTick(() => {
        const containerWidth = echartDivRef.value.clientWidth;
        const containerHeight = echartDivRef.value.clientHeight;
        myChart.resize({width: containerWidth, height: containerHeight});
    })
  }catch (error) {
  }
};


let newOptionList = [];
const resetOptionData = (n) => {
   newOptionList = [];
   newOptionList =  JSON.parse(JSON.stringify(optionList.value));
   let curIndex =  n % newOptionList.length;
   newOptionList[curIndex].value =  newOptionList[curIndex].value + 150;
   let osum = newOptionList.reduce((accumulator, currentValue) => {
      return accumulator + currentValue.value;
   }, 0)
   titleNumValue.value = (newOptionList[curIndex].value / osum).toFixed(2) * 100 + " %" ;
   titleName.value = newOptionList[curIndex].name;
   let option = getPie3D( newOptionList,  0.7);
   return option
}



// 定时器，每秒更新一次当前时间
let interval;
const initPieGo  = () => {
   let n = 0;
   // 设置定时器，每3秒更新一次时间
    interval = setInterval(() => {
         let newOption =  resetOptionData(n);
         myChart.setOption(newOption);
         n = n + 1;
    }, 5000);
}


onMounted(() => {
   initChart();
	resize();
	window.addEventListener("resize", resize, true);

   // 
   initPieGo();
})

onBeforeUnmount(() => {
	window.removeEventListener("resize", resize, true);
	myChart?.dispose();
   //
   clearInterval(interval);
})





</script>
  
<style lang="css" scoped>
.chart-container {
    width: 100%;
    height: 100%;
    /* position: relative; */
    color: #E6F7FF;
}

.echart-div {
    width: 100%;
    height: 100%;
    color: #E6F7FF;
}
</style>