<template>
    <div class="chart-container">
        <div ref="echartDivRef" class="echart-div"></div>
    </div>
</template>


<script setup>
import { ref , onMounted, onBeforeMount, nextTick, onBeforeUnmount } from "vue";
import * as echarts from 'echarts';

const echartDivRef = ref(null);



let myChart;
const initChart = async () => {
    await nextTick(); // 确保 DOM 更新完成
    if (myChart != null && myChart != "" && myChart != undefined) {
        myChart.dispose();
    }
    try {
        myChart = echarts.init(echartDivRef.value);
        const option = getOption();
        console.log("option:", option);
        myChart.setOption(option);
    } catch (error) {
        console.error('ECharts 初始化失败:', error);
    }

}

let peopleList = [
   { name: '预警', value:3320, percent: 0 },
   { name: '蓝色预警', value:1120, percent: 0 },
   { name: '黄色预警', value:1258, percent: 0 },
   { name: '橙色预警', value:2540, percent: 0 },
]
let total = peopleList.reduce((pre, next) => {
   return pre + next.value;
}, 0);

let numberWidth = String(total).length * 8 + 8;

peopleList.forEach((item) => {
   item.percent = total == 0 ? 0 : ((item.value / total) * 100).toFixed(2);
});

let color = ['rgba(113, 226, 135, 1)', 'rgba(119, 247, 253, 1)', 'rgba(44, 104, 231, 1)', 'rgba(93, 202, 250, 1)']


const getOption = () => {
     
    let option = {
    // backgroundColor: 'rgba(8, 19, 34,1)',
    color: color,
    tooltip: {
        trigger: 'item',
    },
    legend: {
        show: true,
        orient: 'vertical',
        top: "30vh",
        right: '30vw',
        icon: 'rect',
        // itemGap: 20,
        itemWidth: 10,
        itemHeight: 10,
        color: '#fff',
        formatter: function (name) {
            let items = peopleList.find((item) => item.name == name);
            return `{number|${items?.value}}{unit|个}  {percent|${items?.percent + '%' || ''}}`
            // return `{name|${name}}{number| ${items?.value || ''}}{unit|个} {percent|${items?.percent + '%' || ''}}`;
        },
        itemStyle: {
            // borderWidth: 1,
        },
        textStyle: {
            rich: {
                number: {
                    width: numberWidth,
                    color: '#DDF6FD',
                    align: 'left',
                    fontSize: 12,
                // padding: [8, 0, 0, 0]
                },
                name: {
                    color: 'rgba(255,255,255,0.8)',
                    fontSize: 12,
                // fontFamily: 'Source Han Sans CN',
                // padding: [28, 0, 0, 4]
                },
                unit: {
                    color: 'rgba(255,255,255,0.8)',
                    fontSize: 12,
                    // fontFamily: 'Source Han Sans CN',
                    padding: [0, 0, 0, -8]
                },
                percent: {
                    color: '#DDF6FD',
                    align: 'left',
                    fontSize: 12,
                    // padding: [6, 0, 0, 0]
                },
            },
        },
    },
    title: [
        {
            text: '{title|总预警数}',
            left: '29%',
            top: '38%',
            textAlign: 'center',
            textStyle: {
                rich: {
                title: {
                    color: '#fff',
                    fontSize: 12,
                },

                }
            },
        },
        {
            text: '{num|' + total +'},{unit|个}',
            left: '24%',
            top: '47%',
            textStyle: {
                rich: {
                num: {
                    fontSize: 12,
                    color: '#9DDBFB',
                    // fontFamily: 'DIN Alternate',
                },
                unit: {
                    color: '#9DDBFB',
                    fontSize: 12,
                    // padding: [0, 0, -8, 8]
                }
                }
            },
        },
    ],

    series: [
        {
            type: 'pie',
            radius: ['50%', '60%'],
            center: ['30%', '50%'],
            padAngle: 5,
            label: {
                show: false,
            },
            itemStyle: {
                shadowColor: 'rgba(255, 255, 255, 0.5)',
                shadowBlur: 20,
                borderWidth: 5,
                borderRadius: 5,

            },
            emphasis: {
                scale: false
            },
            data: peopleList,
        },
        {
            type: 'pie',
            radius: ['40%', '50%'],
            center: ['30%', '50%'],
            padAngle: 5,
            label: {
                show: false,
            },
            itemStyle: {
                shadowColor: 'rgba(255, 255, 255, 0.5)',
                shadowBlur: 20,
                borderWidth: 5,
                opacity: 0.3,
            },
            emphasis: {
                scale: false
            },
            data: peopleList,
        },
        

        {
            name: "黄线",
            type: "pie",
            startAngle: 85,
            radius: ['35%', '38%'],
            center: ['30%', '50%'],
            hoverAnimation: false,
            startAngle: 90,
            padAngle: 5,
            tooltip: {
                // show: false,
            },
            itemStyle: {
                borderCap: 'round',
                normal: {
                color: function (data) {
                    let tempColor = data.data == 10 ? "rgba(240, 197, 67, 0)" : "rgba(240, 197, 67, 1)"
                    return tempColor
                },
                },
            },
            zlevel: 4,
            labelLine: {
                show: false,
            },
            data: [100, 100, 100, 100],
        },

    ],
    }
    return option;
}


let rotate = 0
let deg = 50



function pieRun() {
    let option = getOption();
   option.series[1].radius[1] = `${deg}%`
   option.series[2].startAngle = rotate
   if(myChart === null || myChart === undefined) {
    return ;
   }
   nextTick(() => {
        myChart.setOption(option)
        myChart.resize()
        rotate += 1
        if (deg > 80) {
            deg = 50
        }
        deg += 0.1
        requestAnimationFrame(pieRun)
   })
}



const resize = () => {
  try{
    nextTick(() => {
        const containerWidth = echartDivRef.value.clientWidth;
        const containerHeight = echartDivRef.value.clientHeight;
        myChart.resize({width: containerWidth, height: containerHeight});
    })
  }catch (error) {
  }
};



onMounted(() => {
    initChart();
	resize();
	window.addEventListener("resize", resize, true);
    pieRun();
})

onBeforeUnmount(() => {
	window.removeEventListener("resize", resize, true);
	myChart?.dispose();
})

</script>



<style lang="css" scoped>
.chart-container {
    width: 100%;
    height: 100%;
    /* position: relative; */
    color: #E6F7FF;
}

.echart-div {
    width: 100%;
    height: 100%;
    color: #E6F7FF;
}
</style>