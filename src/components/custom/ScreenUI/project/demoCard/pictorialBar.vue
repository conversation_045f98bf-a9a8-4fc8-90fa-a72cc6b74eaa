<template>
    <div class="chart-container">
        <div ref="echartDivRef" class="echart-div"></div>
    </div>
</template>


<script setup>
import { ref , onMounted, onBeforeUnmount } from "vue";
import * as echarts from 'echarts';


const echartDivRef = ref(null);



let myChart;
const initChart = () => {
    if (myChart != null && myChart != "" && myChart != undefined) {
        myChart.dispose();
    }
    myChart = echarts.init(echartDivRef.value);
    let option = getOption();
    // 使用刚指定的配置项和数据显示图表。
    myChart.setOption(option);
}


let dataArray = {
    dataNum: [
        { name: '时', data: [20, 30, 40, 40, 60, 70]},
        { name: '日', data: [30, 50, 40, 50, 20, 70]},
        { name: '月', data: [40, 60, 10, 50, 60, 70]},
        { name: '年', data: [10, 30, 40, 20, 60, 70]},
    ],
    name: [20,30,40,50,60,70],
    score: [2, 3, 4, 5, 4, 6]
}

let links = dataArray.score.map( (item, i) => ({ source: i, target: i + 1 }));

let color = [
    [{ offset: 0, color: '#dff7ff' },{ offset: 0.07, color: '#4ad2ff' }, { offset: 1, color: 'rgba(74, 210, 255, .1)' }],
    [{ offset: 0, color: '#ffffff' },{ offset: 0.07, color: '#29ff60' }, { offset: 1, color: 'rgba(41, 255, 96, .1)' }],
    [{ offset: 0, color: '#fffdef' },{ offset: 0.07, color: '#ffd83e' }, { offset: 1, color: 'rgba(255, 216, 62, .1)' }],
    [{ offset: 0, color: '#ffeaea' },{ offset: 0.07, color: '#ff5676' }, { offset: 1, color: 'rgba(255, 86, 118, .1)' }],
]
let color2 = ['#4ad9ff', '#4ad2ff', '#29ff60', '#ffd83e', '#ff5676' ]


const getOption = () => {
    
    // series
    let series = dataArray.dataNum.map((v,i) => ({
        name: v.name,
        type: 'pictorialBar',
        symbol: 'path://M32.000,0.000 L64.000,800.000 L0.000,800.000 L32.000,0.000 Z',
        // 是否裁剪图形
        symbolClip: false,
        barWidth: 10,
        // 间隔
        barGap: '10%',
        data: v.data,
        itemStyle: {
            color: params => {
                return new echarts.graphic.LinearGradient(0, 0, 0, 1, color[i])
            }
        }
    }))

    // 插入关系图配置
    series.splice(0, 0, {
        // 第二坐标轴，即右边
        yAxisIndex: 1,
        name:'温度',
        type: 'graph',
        layout: 'none',
        coordinateSystem: 'cartesian2d',
        symbolSize: 15,
        // 文字显示
        label: { 
            show: true,
            formatter: params => { 
                return params.value
            },
            textStyle: {
                align: 'center', fontSize: 12, color: '#fff'
            }
        },
        // 关系轴样式
        edgeSymbol: ['circle', 'arrow'],
        edgeSymbolSize: [4,10],
        data: dataArray.score,
        links: links,
        lineStyle: { color: color[0] }
    })

    let getLegend = dataArray.dataNum.map((v, i) =>({
        name: v.name,
        textStyle: { 
            fontSize: 12,
            color: '#fff'
        }
    })) 
    // legend
    let legend = [{
        name: '温度',
        data: ['温度'],
        textStyle: { 
            fontSize: 12,
            color: '#fff'
        },
        right: 340,
        top: 6,
    },{
        data: getLegend,
        right: 20,
        top: 6,
        itemGap: 15
    }]


    // tooltip
    let tooltip = {
        trigger: 'axis',
        textStyle: { fontSize: 12 },
        axisPointer: { type: 'cross', label: { backgroundColor: '#283b56' } },
        formatter: v => {
            let [a, b, c, d, e] = v
            return `
                <div class='u-p-1'>
                    <div>满意度评分：${a.value}</div>
                    <div class='u-mt-1'>受理：${b.value}</div>
                    <div class='u-mt-1'>办结：${c.value}</div>
                    <div class='u-mt-1'>即将逾期：${d.value}</div>
                    <div class='u-mt-1'>逾期：${e.value}</div>
                </div>
            `
        }
    }

    // grid
    let grid = { top: '20%', left: '10%', right: '5%', bottom: '15%' }

    // xAxis
    let xAxis = {
        type: 'category',
        boundaryGap: true,
        data: dataArray.name,
        axisLine: { lineStyle: { color: 'rgba(0,129,251,1)' } },
        axisLabel: {
            textStyle: { fontSize: 12, color: '#bdcbfd' },
            rotate: 0
        },
        axisTick: { show: false }, //坐标轴刻度
    }

    // yAxis
    let yAxis =[{
        name: '温度(℃)',
        type: 'value', scale: true, min: 0,
        nameTextStyle: { color: '#81b1ff', fontSize: 12},
        boundaryGap: [0.2, 0.2],
        splitLine: { show: false },
        axisLine: { lineStyle: { color: 'rgba(0,129,251,1)' } },
        axisLabel: { textStyle: { fontSize: 12, color: '#bdcbfd' } }
    },{
        name: '温度',
        // 隐藏第二坐标轴
        show: false
    }]

    // 渲染
    let option = { tooltip, grid, xAxis, yAxis, series, legend, color: color2 }
    return option;
}


const resize = () => {
  try{
    nextTick(() => {
        const containerWidth = echartDivRef.value.clientWidth;
        const containerHeight = echartDivRef.value.clientHeight;
        myChart.resize({width: containerWidth, height: containerHeight});
    })
  }catch (error) {
  }
};



onMounted(() => {
    console.log("pictorialBar onmounted")
    initChart();
	resize();
	window.addEventListener("resize", resize, true);
})

onBeforeUnmount(() => {
	window.removeEventListener("resize", resize, true);
	myChart?.dispose();
})
</script>


<style scoped>
.chart-container {
    width: 100%;
    height: 100%;
    /* position: relative; */
    color: #E6F7FF;
}

.echart-div {
    width: 100%;
    height: 100%;
    color: #E6F7FF;
}
</style>
