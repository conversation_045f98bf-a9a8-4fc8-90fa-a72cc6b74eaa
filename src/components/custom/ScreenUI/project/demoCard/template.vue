<template>
    <div class="chart-container">
        <div ref="echartDivRef" class="echart-div"></div>
    </div>
</template>

<script setup>
import { ref , onMounted, onBeforeUnmount } from "vue";
import * as echarts from 'echarts';
import "echarts-gl";


const echartDivRef = ref(null);



let myChart;
const initChart = () => {
    if (myChart != null && myChart != "" && myChart != undefined) {
        myChart.dispose();
    }
    myChart = echarts.init(echartDivRef.value);
    let option = getOption();
    // 使用刚指定的配置项和数据显示图表。
    myChart.setOption(option);
}


const getOption = () => {
    
}

const resize = () => {
  try{
    nextTick(() => {
        const containerWidth = echartDivRef.value.clientWidth;
        const containerHeight = echartDivRef.value.clientHeight;
        myChart.resize({width: containerWidth, height: containerHeight});
    })
  }catch (error) {
  }
};




onMounted(() => {
    initChart();
	resize();
	window.addEventListener("resize", resize, true);
})

onBeforeUnmount(() => {
	window.removeEventListener("resize", resize, true);
	myChart?.dispose();
})


</script>

  
<style lang="css" scoped>
.chart-container {
    width: 100%;
    height: 100%;
    /* position: relative; */
    color: #E6F7FF;
}

.echart-div {
    width: 100%;
    height: 100%;
    color: #E6F7FF;
}
</style>