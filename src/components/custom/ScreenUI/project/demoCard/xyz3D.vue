<template>
    <div class="chart-container">
        <div ref="echartDivRef" class="echart-div"></div>
    </div>
</template>

<script setup>
import { ref , onMounted, onBeforeUnmount } from "vue";
import * as echarts from 'echarts';
import "echarts-gl";


const echartDivRef = ref(null);



let myChart;
const initChart = () => {
    if (myChart != null && myChart != "" && myChart != undefined) {
        myChart.dispose();
    }
    myChart = echarts.init(echartDivRef.value);
    let option = getOption();
    // 使用刚指定的配置项和数据显示图表。
    myChart.setOption(option);
}


function makeGaussian(amplitude, x0, y0, sigmaX, sigmaY) {
        return function (amplitude, x0, y0, sigmaX, sigmaY, x, y) {
            const exponent = -(
                (Math.pow(x - x0, 2) / (2 * Math.pow(sigmaX, 2)))
                + (Math.pow(y - y0, 2) / (2 * Math.pow(sigmaY, 2)))
            );
            return amplitude * Math.pow(Math.E, exponent);
        }.bind(null, amplitude, x0, y0, sigmaX, sigmaY);
    }

const getOption = () => {
    // 创建一个高斯分布函数
    const gaussian = makeGaussian(50, 0, 0, 20, 20);
    const data = [];
    // 曲面图要求给入的数据是网格形式按顺序分布。
    for (let y = -50; y <= 50; y++) {
        for (let x = -50; x <= 50; x++) {
            const z = gaussian(x, y);
            data.push([x, y, z]);
        }
    }
    let option = {
        grid3D: {},
        xAxis3D: {},
        yAxis3D: {},
        zAxis3D: {max: 60},
        series: [{
            type: 'surface',
            data: data
        }]
    }
    return option;
}

const resize = () => {
  try{
    nextTick(() => {
        const containerWidth = echartDivRef.value.clientWidth;
        const containerHeight = echartDivRef.value.clientHeight;
        myChart.resize({width: containerWidth, height: containerHeight});
    })
  }catch (error) {
  }
};




onMounted(() => {
    initChart();
	resize();
	window.addEventListener("resize", resize, true);
})

onBeforeUnmount(() => {
	window.removeEventListener("resize", resize, true);
	myChart?.dispose();
})



</script>

  
<style lang="css" scoped>
.chart-container {
    width: 100%;
    height: 100%;
    /* position: relative; */
    color: #E6F7FF;
}

.echart-div {
    width: 100%;
    height: 100%;
    color: #E6F7FF;
}
</style>