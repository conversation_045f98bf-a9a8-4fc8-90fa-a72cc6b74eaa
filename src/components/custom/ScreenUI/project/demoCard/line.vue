<template>
	<div class="chart-container">
		<div ref="echartDivRef" class="echart-div"></div>
	</div>
</template>

<script setup>
import { ref, nextTick , onMounted, onBeforeUnmount } from "vue";
import * as echarts from 'echarts';


const echartDivRef = ref(null);

var fontColor = '#30eee9';

let myChart;
const initChart = () => {
    if (myChart != null && myChart != "" && myChart != undefined) {
        myChart.dispose();
    }
    myChart = echarts.init(echartDivRef.value);
    let option = getOption();
    // 使用刚指定的配置项和数据显示图表。
    myChart.setOption(option);
}

const getOption = () => {

    let option ={
		grid: {
	        left: '5%',
            right: '10%',
            top:'20%',
	        bottom: '5%',
	        containLabel: true
		},
		tooltip : {
			show: true,
			trigger: 'item'
		},
		legend: {
			show:true,
			x:'center',
			y:'5',
			icon: 'stack',
			itemWidth:10,
			itemHeight:10,
			textStyle:{
				color:'#1bb4f6'
			},
			data:['测站1水位','测站2水位']
		},
		xAxis : [
	        {
	            type : 'category',
	            boundaryGap : false,
	            axisLabel:{
	            	color: fontColor
	            },
	            axisLine:{
               		show:true,
               		lineStyle:{
		            	color:'#397cbc'
		            }
				},
				axisTick:{
	            	show:false,
	            },  
	            splitLine:{
	            	show:true,
		            lineStyle:{
		            	color:'#195384'
		            }
		        },
	            data : ['1月','2月','3月','4月','5月','6月','7月','8月','9月','10月','11月','12月']
	        }
	    ],
	    yAxis : [
			{
				type : 'value',
				name : '水位',
				min: 0,
				max: 600,
				axisLabel : {
					formatter: '{value} m',
					textStyle:{
						color:'#2ad1d2'
					}
				},
				axisLine:{
					lineStyle:{
						color:'#27b4c2'
					}
				},
				axisTick:{
	            	show:false,
	            },
				splitLine:{
					show:true,
					lineStyle:{
						color:'#11366e'
					}
				}
			},
			{
				type : 'value',
				name : '水位',
				min: 0,
				max: 600,
				axisLabel : {
					formatter: '{value} m',
					textStyle:{
						color:'#1890ff'
					}
				},
				axisLine:{
					lineStyle:{
						color:'#1890ff'
					}
				},
				axisTick:{
	            	show:false,
	            },
				splitLine:{
					show:true,
					lineStyle:{
						color:'#11366e'
					}
				}
			}
		],
		series : [
			{
				name:'测站1水位',
				type:'line',
				stack: '总量',
				symbol:'circle',
				symbolSize: 8,
	            itemStyle: {
			        normal: {
						color:'#0092f6',
			            lineStyle: {
							color: "#0092f6",
							width:1
			            },
			            areaStyle: { 
							//color: '#94C9EC'
							color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [{
								offset: 0,
								color: 'rgba(7,44,90,0.3)'
							}, {
								offset: 1,
								color: 'rgba(0,146,246,0.9)'
							}]),
			            }
			        }
				},
				markPoint:{
					itemStyle:{
						normal:{
							color:'red'
						}
					}
				},
				data:[120, 132, 101, 134, 90, 230, 210, 182, 191, 234, 290, 330]
			},
			{
				name:'测站2水位',
				type:'line',
				stack: '总量',
				symbol:'circle',
				symbolSize: 8,
				
	            itemStyle: {
			        normal: {
			            color:'#00d4c7',
			            lineStyle: {
							color: "#00d4c7",
							width:1
			            },
			            areaStyle: { 
							color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [{
								offset: 0,
								color: 'rgba(7,44,90,0.3)'
							}, {
								offset: 1,
								color: 'rgba(0,212,199,0.9)'
							}]),
			            }
			        }
				},
				data:[220, 182, 191, 234, 290, 330, 310,201, 154, 190, 330, 110]
			}
		]
	};
    return option;
}

const resize = () => {
  try{
    nextTick(() => {
        const containerWidth = echartDivRef.value.clientWidth;
        const containerHeight = echartDivRef.value.clientHeight;
        myChart.resize({width: containerWidth, height: containerHeight});
    })
  }catch (error) {
  }
};



onMounted(() => {
	nextTick(() => {
		initChart();
		resize();
	})
	window.addEventListener("resize", resize, true);
})

onBeforeUnmount(() => {
	window.removeEventListener("resize", resize, true);
	myChart?.dispose();
})
</script>


<style lang="css" scoped>
.chart-container {
    width: 100%;
    height: 100%;
    /* position: relative; */
    color: #E6F7FF;
}

.echart-div {
    width: 100%;
    height: 100%;
    color: #E6F7FF;
}
</style>