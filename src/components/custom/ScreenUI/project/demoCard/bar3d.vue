<template>
    <div class="chart-container border">
        <div ref="echartDivRef" class="echart-div"></div>
    </div>
</template>


<script setup>
import { nextTick, onBeforeUnmount, onMounted, ref } from "vue";
import * as echarts from 'echarts';


const echartDivRef = ref(null);



//组织数据
let setData = function(data, constData, showData) {
    data.filter(function(item) {
        if (item) {
            constData.push(1);
            showData.push(item);
        } else {
            constData.push(0);
            showData.push({
                value: 1,
                itemStyle: {
                    normal: {
                        borderColor: "rgba(0,0,0,0)",
                        borderWidth: 2,
                        color: "rgba(0,0,0,0)",
                    },
                },
            });
        }
    });
}
//组织颜色
let setColor = function(colorArr) {
    let color = {
        type: "linear",
        x: 0,
        x2: 1,
        y: 0,
        y2: 0,
        /* 此处决定阴暗面 若为横向柱状图则x,y轴调换
            x: 0,
            x2: 0,
            y: 0,
            y2: 1, */
        colorStops: [{
                offset: 0,
                color: colorArr[0],
            },
            {
                offset: 0.5,
                color: colorArr[0],
            },
            {
                offset: 0.5,
                color: colorArr[1],
            },
            {
                offset: 1,
                color: colorArr[1],
            },
        ],
    };
    return color
}

var vehicle = [45, 25, 48, 62, 35]
var controlBall = [23, 12, 52, 14, 9]

var barWidth = ref(25);
var constData1 = [];
var showData1 = [];


var constData2 = [];
var showData2 = [];

setData(vehicle, constData1, showData1)
setData(controlBall, constData2, showData2)

var colorArr1 = ["#345A8B", "#387ABD", "#51C0DB"];
var colorArr2 = ["#51C0DB", "#42D9D6", "#45F5F1"];

var color1 = setColor(colorArr1)
var color2 = setColor(colorArr2)





const getOption = ()  =>{
    var option = {
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            }
        },
        legend: {
            show: false
        },
        grid: {
            top: '15%',
            bottom: '15%'
        },
        xAxis: {
            type: 'category',
            axisLabel: {
                color: '#FFFFFF'
            },
            axisLine: {
                show: true,
                lineStyle: {
                    color: '#1B3F66'
                }
            },
            axisTick: {
                show: false
            },
            data:  ['合肥', '安庆', '芜湖', '南京', '杭州']
        },
        yAxis: {
            type: 'value',
            axisLabel: {
                color: '#FFFFFF'
            },
            axisLine: {
                show: true,
                lineStyle: {
                    color: '#1B3F66'
                }
            },
            splitLine: {
                lineStyle: {
                    color: '#1B3F66'
                }
            }
        },
        series: [{
                z: 1,
                type: 'bar',
                name: '柱子1',
                barGap: "15%", //相邻柱子间距
                itemStyle: {
                    borderRadius: [0, 0, 0, 0],//柱子四周圆角
                    color: color1//柱子左右颜色（深，浅）
                },
                data: vehicle //Y轴上的高度
            },
            // ---------------------------------------------
            {
                z: 2,
                name: '柱子1',
                type: "pictorialBar",
                data: constData1,//此数据对应底部组件
                symbol: "diamond",//底部组件形状，不写默认为椭圆
                symbolOffset: ["-58%", "50%"],//与柱子的偏移角度
                symbolSize: [barWidth.value - 4, (10 * (barWidth.value - 4)) / barWidth.value],//底面[宽，高]
                itemStyle: {
                    normal: {
                        color: color1//底面左右颜色（深，浅）
                    },
                },
                tooltip: {
                    show: false,
                },
            },
            {
                z: 3,
                name: '柱子1',
                type: "pictorialBar",
                symbolPosition: "end",
                data: showData1,//此数据对应顶部组件
                symbol: "diamond",
                symbolOffset: ["-55%", "-50%"],
                symbolSize: [barWidth.value - 4, (10 * (barWidth.value - 4)) / barWidth.value],
                itemStyle: {
                    normal: {
                        /* borderColor: colorArr1[2],
                        borderWidth: 2, */ //加上棱角分明
                        color: colorArr1[2]
                    },
                },
                tooltip: {
                    show: false,
                },
            }, {
                z: 1,
                type: 'bar',
                name: '柱子2',
                itemStyle: {
                    borderRadius: [0, 0, 0, 0],
                    color: color2
                },
                data: controlBall
            },
            {
                z: 2,
                name: '柱子2',
                type: "pictorialBar",
                data: constData2,
                symbol: "diamond",
                symbolOffset: ["58%", "50%"],
                symbolSize: [barWidth.value - 4, (10 * (barWidth.value - 4)) / barWidth.value], //=========================
                itemStyle: {
                    normal: {
                        color: color2
                    },
                },
                tooltip: {
                    show: false,
                },
            },
            {
                z: 3,
                name: '柱子2',
                type: "pictorialBar",
                symbolPosition: "end",
                data: showData2,
                symbol: "diamond",
                symbolOffset: ["58%", "-50%"],
                symbolSize: [barWidth.value - 4, (10 * (barWidth.value - 4)) / barWidth.value],
                itemStyle: {
                    normal: {
                    /* 	borderColor: colorArr2[2],
                        borderWidth: 2, */
                        color: colorArr2[2]
                    },
                },
                tooltip: {
                    show: false,
                },
            }
        ]
    }
    return option;
}

let myChart;
const initChart = () => {
    if (myChart != null && myChart != "" && myChart != undefined) {
        myChart.dispose();
    }
    myChart = echarts.init(echartDivRef.value);
    let option = getOption();
    // 使用刚指定的配置项和数据显示图表。
    myChart.setOption(option);
}



onMounted(() => {
    console.log("on mounted...")
    initChart();
})


// 暴露一个方法用于获取 HTML
function getContainerHTML() {
    // initChart();
    return echartDivRef.value ? echartDivRef.value.innerHTML : '';
}


// 使用 defineExpose 明确暴露给父组件的属性或方法
defineExpose({
    getContainerHTML,
});



const resize = () => {
  try{
    nextTick(() => {
        const containerWidth = echartDivRef.value.clientWidth;
        const containerHeight = echartDivRef.value.clientHeight;
        myChart.resize({width: containerWidth, height: containerHeight});
    })
  }catch (error) {
  }
};


onMounted(() => {
    nextTick(() => {
        initChart()
        resize();
    })
    window.addEventListener("resize", resize, true);
    // 使用 ResizeObserver 监听父容器尺寸变化
})


onBeforeUnmount(() => {
  window.removeEventListener("resize", resize, true);
  myChart?.dispose();
});



</script>

<style lang="css" scoped>

 /* 
    文本颜色: #E6F7FF,
    轴线颜色: #BAE7FF,
    颜色1: #1890ff, RGBA(24, 144, 255, 35)渐变, 偏蓝
    颜色2: #1ee7e7, RGBA(30, 231, 231, 35)渐变，偏绿
 */


/* * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

.border {
    border: 1px solid rgba(37, 52, 70);
}

.left {
    width: 25%;
    height: 100%;
    margin: 0 auto;
}	 */



.chart-container {
    width: 100%;
    height: 100%;
    /* position: relative; */
    color: #E6F7FF;
}

.echart-div {
    width: 100%;
    height: 100%;
    color: #E6F7FF;
}
</style>