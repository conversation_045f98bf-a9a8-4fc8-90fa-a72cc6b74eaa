<template>
    <!-- <ScaleScreen width="1920" height="1080" :fullScreen="true"> -->
        <div class="screen-bg"  ref="screenBgRef">
            <div class="screen-header">
                <Header :title="title" v-model:menuList="menuList"  @menuClick="menuClick"></Header>
            </div>
            <div class="content-container"> 
                <div class="screen-left">
                    <div class="item-container">
                        <div v-for="card in cardContents.slice(0, 3)" :key="card.id" class="item-div">
                            <ItemCard :content="getContent(card.content)"  :title="card.title" />
                        </div>
                    </div>
                </div>
                <div class="screen-right">
                    <div class="right-item-container"> 
                        <div v-for="card in cardContents.slice(3)" :key="card.id" class="right-item-div">
                            <RightItemCard :content="getContent(card.content)"  :title="card.title" />
                        </div>
                    </div>
                </div>
            </div>
            <div class="screen-bottom">
                <Footer  
                    :leftBtnList="props.leftBtnList"  
                    :rightBtnList="props.rightBtnList"
                    @btnClick="btnClick"
                >
                </Footer>
            </div>
        </div>
    <!-- </ScaleScreen> -->

</template>


<script setup>
import { ref, watch, markRaw, onMounted, onUnmounted  } from "vue";


import ItemCard from './components/itemCard.vue';
import RightItemCard from "./components/rightItemCard.vue";
import Header from "./components/header.vue";
import Footer from "./components/footer.vue";
import ScaleScreen from "./ScaleScreen";


/* --- 左右两侧例子 -- */
import Line from "./demoCard/line.vue";
import Bar3d from "./demoCard/bar3d.vue";
import Pie3d from "./demoCard/pie3d.vue";
import TotalPie from "./demoCard/totalPie.vue";
import PictorialBar from "./demoCard/pictorialBar.vue";
import Xyz3D from "./demoCard/xyz3D.vue";
import Radar from "./demoCard/radar.vue";


const props = defineProps({
    leftBtnList: {
        type: Array,
        default: [],
    },
    rightBtnList: {
        type: Array,
        default: [],
    },
    title: {
        type: String,
        default: "智慧水利大屏演示"
    },
    cardContents: {
        type: Array,
        default: [],
    },
    menuList: {
        type: Array,
        default: []
    }
})


const emit = defineEmits(["btnClick", "menuClick"]);

const title = ref(props.title)
const screenBgRef  = ref(null);


//
const menuList = ref(props.menuList)

const initDemoMenuData = () => {
    if(menuList.value.length === 0) {
        menuList.value = [
            { id: 1, name: "导航菜单1", key: "menu-1" },
            { id: 2, name: "导航菜单2", key: "menu-2" },
            { id: 3, name: "导航菜单3", key: "menu-3" },
            { id: 4, name: "导航菜单4", key: "menu-4" },
            { id: 5, name: "导航菜单5", key: "menu-5" },
            { id: 6, name: "导航菜单6", key: "menu-6" },
        ]
    }
}


// 定义卡片内容的数据
const cardContents = ref(props.cardContents)

// 如果没有给左右两侧的卡片内容，则默认填充demo的卡片数据
const initDemoCardContents = () => {
    if (cardContents.value.length === 0) {
        cardContents.value = [
            { id: 1, title: "测站总数",  content: markRaw(Pie3d) },
            { id: 2, title:"水位", content: markRaw(Line) }, // 可以是文本、HTML字符串或对象等
            { id: 3, title: "测站分布",  content: markRaw(Bar3d)  },
            { id: 4, title: "预警数据", content: markRaw(TotalPie) },
            { id: 5, title: "巡检数据", content: markRaw(Radar) },
            { id: 6, title:"告警数据", content: markRaw(PictorialBar) },
        ]
    }
}




const btnClick = (item) =>{
    emit("btnClick", item)
}

const menuClick = (item) => {
    emit("menuClick", item)
}

// 判断是否是组件引用并使用 markRaw 标记组件对象
function getContent(content) {
  if (isComponent(content)) {
    return markRaw(content); // 避免将组件对象变为响应式
  }
  return content;
}

// 判断是否是组件引用
// 判断是否是组件引用
function isComponent(content) {
  return content && (typeof content === 'object') &&
         (typeof content.render === 'function' ||
          typeof content.setup === 'function' ||
          content.__vccInfo); // 对于 <script setup> 组件，Vue 会添加 __vccInfo 属性
}

// watch(
//   () => props.cardContents,
//   (value) => {
//     if (value !== undefined && value !== "") {
//         console.log("value:", value)
//       cardContents.value = value;
//     }
//   }
// )



/* -------------- auto scale 自动缩放 ---------------- */
// 假设这是你想要保持的原始宽度和高度
// const originalWidth =  1920; // 2560; // px
// const originalHeight =  1080; // 1440; // px





onMounted(() => {
    initDemoMenuData();
    initDemoCardContents();
 
});


onUnmounted(() => {
  
});

</script>

<style lang="scss" scoped>

.screen-bg {
    // position: fixed;
    position: absolute;
    top: 0; //  70px; // 头部上面的按钮
    left: 0;
    right: 0;
    bottom: 0; /* 确保 screen-bg 占据整个窗口 */
    display: flex;
    flex-direction: column;
    // height: calc(100vh - 70px); /* 确保占据整个视口高度, 减去header的高度 */
    z-index: 10;
    // pointer-events: none; /* 让点击事件通过 */

    background-image: url('/assets/screen/screenbg.png');
    // /* 设置背景图片的尺寸 */
    background-size: 100% 100%; //100% 100%;
    // /* 设置背景图片的位置 */
    background-position: center;
    // /* 设置背景图片是否重复 */
    background-repeat: no-repeat;

}



.screen-header {
    // width: calc(100vw - 20px);
    // margin-left: 10px;
    width: 100%;
    flex: 0 0 8%; /* 固定高度为屏幕高度的8% */
    position: relative; /* 确保子元素可以相对于此元素定位 */
}


.content-container {
    display: flex;
    flex: 1; /* 占用剩余空间 */
    overflow: hidden; /* 防止内容溢出 */
    position: relative;

    // padding: 0   2%; /* 50px; // 2vw; // 20px; /* 添加左右内边距 */
    padding-left: 3vw;
    padding-right: 3vw;

    // 倾斜3D效果
    perspective: 1200px; /* 设置透视效果 */
    transform-style: preserve-3d; /* 保持 3D 变换 */
}

.screen-left {
    // margin-left: 1vw;
    // 倾斜3D效果
    transform: rotateY(30deg) translateZ(10px) scale(0.95);
}

.screen-right {
    margin-left: auto; /* 推动 .screen-right 靠右对齐 */
    // margin-right: 10px;
    // margin-right: 2vw;

    // 倾斜3D效果
    transform: rotateY(-30deg) translateZ(10px) scale(0.95);
}

.screen-left, .screen-right {
    width: 18% ;// 18vw;

    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    padding-top: 1vw;
    // overflow-y: auto; /* 如果内容超出容器高度，则允许滚动 */
}


.item-container, .right-item-container {
    display: flex;
    flex-direction: column;
    gap: 10px;
    // gap: 2vh;
    width: 100%;
    flex-grow: 1; /* 让 item-container 占用所有可用空间 */
}

.item-div, .right-item-div {
    width: 100%;
    height:  31%; /* 23vh; /* 根据 ItemCard 的默认高度调整 */
    box-sizing: border-box; /* 确保内边距和边框包含在指定的宽度和高度内 */
}
.screen-bottom{
    // width: calc(100vw - 20px);
    // margin-left: 10px;
    
    width: 100%;
    flex: 0 0 5%; /* 固定高度为屏幕高度的5% */
    position: relative; /* 确保子元素可以相对于此元素定位 */

}




/* 响应式布局 */
@media screen and (max-width: 1600px) {
    .content-container {
        padding: 0  3vw; // 2vw; // 20px; /* 添加左右内边距 */
    }
    .screen-left .screen-right {
        width: 450px;
    }
    .screen-header {
        font-size: 18px;
    }
    .item-div {
        font-size: 14px;
    }
}

@media screen and (max-width: 1200px) {
    .screen-left .screen-right {
        width: 400px;
    }
    .screen-header {
        font-size: 12px;
    }
}

/* 自适应缩放 */
@media screen and (max-height: 900px) {
    .content-container {
        padding-top: 3vh;
    }
}

</style>