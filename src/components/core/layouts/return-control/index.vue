<template>
    <div v-if="display">
        <el-button size="small" @click="returnControl">返回控制台</el-button>
    </div>
</template>

<script setup lang="ts">
import { useUserStore } from '@/store/modules/user'
import { useMenuStore } from '@/store/modules/menu'


const menuStore = useMenuStore()
const display = computed(() => menuStore.displayCtl);


const  returnControl = () => {
    const userStore = useUserStore()
    menuStore.returnControl(userStore.info.company_id as number)
}


</script>

<style lang="scss" scoped>

</style>