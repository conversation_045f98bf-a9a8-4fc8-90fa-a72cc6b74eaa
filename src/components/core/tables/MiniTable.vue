<!-- 表格组件，带分页（默认分页大于一页时显示） -->
<template>
  <div class="mini-table-container" style="overflow-x: auto;" :class="{ 'header-background': showHeaderBackground }">
    <el-table
      v-loading="loading"
      :data="tableData"
      :row-key="rowKey"
      :border="border"
      :stripe="stripe"
      :height="height"
      :max-height="maxHeight"
      :show-header="showHeader"
      :highlight-current-row="highlightCurrentRow"
      @selection-change="handleSelectionChange"
      @row-click="handleRowClick"
      
    >
      <!-- 选择列 -->
      <el-table-column v-if="selection" type="selection" width="55" align="center" fixed="left" />

   
      <!-- 动态列 -->
      <!-- <slot></slot> -->

      <template v-for="(item, index) in props.columns" :key="index">
          <!-- 表格序号 -->
          <el-table-column v-if="item.type === 'index'" v-bind="item">
            <template #default="scope">
              <span> {{ serialNumber(scope) }}</span>
            </template>
          </el-table-column>
          <el-table-column
            v-else-if="Object.hasOwn(item, 'dict')"
            v-bind="item"
            show-overflow-tooltip
            :label-class-name="item.required === true ? 'point-xin' : '' "  
          >
            <template v-if="item.headerSlot" #header="scope">
              <slot
                :name="item.prop + 'Header'"
                :scope="scope"
                :row="scope.row"
              />
            </template>
            <template #default="scope">
              <!-- <Dict
                :dictKey="item.dict"
                :dictValue="scope.row[item.prop]"
                v-if="typeof item.dict === 'string'"
                :key="Math.random()"
              /> -->
              <div v-html="dictHtml(item, scope)"></div>
            </template>
          </el-table-column>
          <!-- default -->
          <el-table-column
            v-else
            v-bind="item"
            :show-overflow-tooltip="!item.rowSlot"
            :label-class-name="item.required === true ? 'point-xin' : '' "  
          >
            <template v-if="item.headerSlot" #header="scope">
              <slot
                :name="item.prop + 'Header'"
                :scope="scope"
                :row="scope.row"
              />
            </template>
            <template v-if="item.rowSlot" #default="scope">
              <slot :name="item.prop" :scope="scope" :row="scope.row" />
            </template>
          </el-table-column>
      </template>

      <!-- 空数据 -->
      <template #empty>
        <el-empty :description="emptyText" />
      </template>
    </el-table>

    <!-- 分页 -->
    <div v-if="pagination" class="table-pagination" :class="paginationAlign">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="pageSizes"
        :total="total"
        :background="true"
        :size="paginationSize"
        :layout="paginationLayout"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :hide-on-single-page="hideOnSinglePage"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
  interface TableProps {
    columns?: any[]
    /** 表格数据源 */
    data?: any[]
    /** 是否显示加载状态 */
    loading?: boolean
    /** 行数据的 Key，用于标识每一行数据 */
    rowKey?: string
    /** 是否显示边框 */
    border?: boolean
    /** 是否使用斑马纹样式 */
    stripe?: boolean
    /** 是否显示多选列 */
    selection?: boolean
    /** 是否显示序号列 */
    index?: boolean
    /** 表格高度，可以是数字或 CSS 值 */
    height?: string | number
    /** 表格最大高度，可以是数字或 CSS 值 */
    maxHeight?: string | number
    /** 是否显示表头 */
    showHeader?: boolean
    /** 是否高亮当前行 */
    highlightCurrentRow?: boolean
    /** 空数据时显示的文本 */
    emptyText?: string
    /** 是否显示分页 */
    pagination?: boolean
    /** 总条目数 */
    total?: number
    /** 当前页码 */
    currentPage?: number
    /** 每页显示条目个数 */
    pageSize?: number
    /** 每页显示个数选择器的选项列表 */
    pageSizes?: number[]
    /** 只有一页时是否隐藏分页器 */
    hideOnSinglePage?: boolean
    /** 分页器的对齐方式 */
    paginationAlign?: 'left' | 'center' | 'right'
    /** 分页器的大小 */
    paginationSize?: 'small' | 'default' | 'large'
    /** 分页器的布局 */
    paginationLayout?: string
    /** 是否显示表头背景色 */
    showHeaderBackground?: boolean
  }

  const props = withDefaults(defineProps<TableProps>(), {
    data: () => [],
    columns: () => [],
    loading: false,
    rowKey: 'id',
    border: false,
    stripe: false,
    selection: false,
    index: false,
    showHeader: true,
    highlightCurrentRow: false,
    emptyText: '暂无数据',
    pagination: true,
    total: 0,
    currentPage: 1,
    pageSize: 10,
    hideOnSinglePage: true,
    pageSizes: () => [10, 20, 30, 50],
    paginationAlign: 'center',
    paginationSize: 'default',
    paginationLayout: 'total, sizes, prev, pager, next, jumper',
    showHeaderBackground: false
  })

  const emit = defineEmits([
    'update:currentPage',
    'update:pageSize',
    'selection-change',
    'row-click',
    'size-change',
    'current-change'
  ])

  // 表格数据
  const tableData = computed(() => props.data)

  // 当前页
  const currentPage = computed({
    get: () => props.currentPage,
    set: (val) => emit('update:currentPage', val)
  })

  // 每页条数
  const pageSize = computed({
    get: () => props.pageSize,
    set: (val) => emit('update:pageSize', val)
  })

  // 序号
  const serialNumber = (scope:any) => {
    const index = scope.$index;
    if (currentPage.value === 1) {
      return index + 1;
    }
    return index + 1 + (currentPage.value - 1) * pageSize.value;
  };
  // dict rander
  const dictHtml = (column:any, scope:any) => {
    if (column.dict) {
      if(!Array.isArray(scope.row[column.prop]) && String(scope.row[column.prop]).includes(",")) {
        // 不是数组，并且包含 ","  那么直接返回 
        return scope.row[column.prop];
      } else if (Array.isArray(scope.row[column.prop])){
        // 如果是数组的话
        let decodeList = <any>[];
        scope.row[column.prop].forEach((value: any) => {
          let obj = column.dict.find((cur: { value: any }) => cur.value === value);
          if (obj) { decodeList.push(obj.label)}
        });
        return `<span>${decodeList.join(", ")}</span>`;
      } else {
        for (let i = 0; i < column.dict.length; i += 1) {
          if (!column.prop) break;
          if (scope.row[column.prop] === column.dict[i].value) {
            if (column.dict[i].color && column.dict[i].color?.includes("#")) {
              return `<span style="color:${column.dict[i].color}">${column.dict[i].label}</span>`;
            }
            return `<span class="${column.dict[i].color}">${column.dict[i].label}</span>`;
          }
        }
      }

    }
    return "";
  };


  // 选择项改变
  const handleSelectionChange = (selection: any[]) => {
    emit('selection-change', selection)
  }

  // 行点击事件
  const handleRowClick = (row: any, column: any, event: any) => {
    emit('row-click', row, column, event)
  }

  // 每页条数改变
  const handleSizeChange = (val: number) => {
    emit('size-change', val)
  }

  // 当前页改变
  const handleCurrentChange = (val: number) => {
    emit('current-change', val)
  }
</script>

<style lang="scss" scoped>

  .mini-table-container{
    // margin-top: 20px;
    width: 100%;
    max-width: 100%;

    overflow-x: auto;
    overflow-y: auto;
    .el-table {
      min-width: 100%;
      width: auto; // 让表格根据内容自动扩展宽度
    }
    
    // 其他样式...
    
    :deep(.el-table__body-wrapper) {
      overflow-x: auto; // 确保表格体可以水平滚动
    }
    
    .table-pagination {
      display: flex;
      margin-top: 16px;

      // 分页对齐方式
      &.left {
        justify-content: flex-start;
      }

      &.center {
        justify-content: center;
      }

      &.right {
        justify-content: flex-end;
      }
    }
    
    :deep(.el-table__header-wrapper) {
      height: 30px;
    }
    :deep(.el-table__header) {
      height: 30px;
    }
    :deep(.el-table) {
      th.el-table__cell {
        font-weight: 50;
      }

      td.el-table__cell,
      th.el-table__cell {
        padding: 1px 0; // 设置表格单元格高度
      }
    }

    &.header-background {
      :deep(.el-table) {
        th.el-table__cell {
          background-color: var(--el-fill-color-light);
        }
      }
    }

    // 解决el-image 和 el-table冲突层级冲突问题
    ::v-deep(.el-table__cell) {
      position: static !important;
    }
  }
</style>
