@use "sass:map";

@use 'variables';
// 基本

// 定义字体变量
$font-family-base: 'Helvetica Neue', Arial, sans-serif;

// 定义5个级别的字体大小
$font-sizes: (
  h1: 2.5rem,   // 40px
  h2: 2rem,     // 32px
  h3: 1.75rem,  // 28px
  h4: 1.5rem,   // 24px
  h5: 1 rem,     // 24px
  p: 1rem,       // 16px
  desc: 0.94rem  // 15px
);

// 定义字重
$font-weights: (
  h1: 700,
  h2: 600,
  h3: 600,
  h4: 500,
  h5: 450,
  p: 400,
  desc: 380,
);

// 定义行高
$line-heights: (
  h1: 1.2,
  h2: 1.25,
  h3: 1.3,
  h4: 1.35,
  h5: 1,
  p: 1.5,
  desc: 1.38,
);

// 定义间距
$text-margins: (
  h1: 0 0 1.5rem,
  h2: 0 0 1.25rem,
  h3: 0 0 1rem,
  h4: 0 0 0.75rem,
  h5: 0 0 0 0,
  p: 0 0 0.5rem,
  desc: 0 0 0
);

// 定义颜色
$text-colors: (
  h1: --art-text-gray-800,
  h2: --art-text-gray-700, //
  h3: --art-text-gray-600,
  h4: --art-text-gray-500,
  h5: --art-text-gray-400,
  p: --art-text-gray-400,
  desc: --art-text-gray-800,
);

// 文字样式混合宏
@mixin text-style($level) {
  font-family: $font-family-base;
  font-size: map.get($font-sizes, $level);
  font-weight: map.get($font-weights, $level);
  line-height: map.get($line-heights, $level);
  margin: map.get($text-margins, $level);
  color: map.get($text-colors, $level );
}



// 为每个级别创建样式类
.text-h1 {
    @include text-style(h1);
}

.text-h2 {
    @include text-style(h2);
}

.text-h3 {
    @include text-style(h3);
}

.text-h4 {
    @include text-style(h4);
}

.text-h5 {
  @include text-style(h5);
}

.text-p {
    @include text-style(p);
}

.text-desc {
    @include text-style(desc);
}

// 原生HTML元素样式（可选）
// h1 { @extend .text-h1; }
// h2 { @extend .text-h2; }
// h3 { @extend .text-h3; }
// h4 { @extend .text-h4; }
// p { @extend .text-p; }

// 响应式字体大小调整
@mixin responsive-text-styles() {
    @each $level, $size in $font-sizes {
      .text-#{$level} {
        @media (max-width: 768px) {
          font-size: calc(#{$size} * 0.9);
        }
        @media (max-width: 480px) {
          font-size: calc(#{$size} * 0.85);
        }
      }
    }
}
  
@include responsive-text-styles();


.action-elink {
  margin-left: 5px;
}

.table_operation{
  .el-link{
      font-size: 12px;
      
      &:nth-child(n){
          margin-right: 10px;
      }
      &:nth-last-child(1){
          margin-right: 0;
      }
  }
}


.pagination-container {
  position: relative;  
  width: 100%;
  height: 100%;
  // right: 20px;            /* 贴紧父级右边 */
  // bottom: 0px;           /* 贴紧父级底部 */
  margin-top: 20px;    /* 保留原有样式 */
  display: flex;
  justify-content: flex-end;

  .pagination-class {
    margin-top:30px;
    float:right  
  }
}