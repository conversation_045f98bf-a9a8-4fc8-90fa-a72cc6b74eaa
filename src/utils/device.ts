import { Attribute, Device, ExtendInfo, KeyValue } from "@/types/device"

export enum Status {
    Enabled = 1,
    Disabled = -1,
  }
  

export const accessTypeList = [
  { id: 1, label: '直连设备', value: 1 },
  { id: 2, label: '网关子设备', value: 2 },
  { id: 3, label: '网关设备', value: 3 }
]

// 提取 connTypeList 的项类型
type AccessTypeItem = (typeof accessTypeList)[number]

// 转换为以 value 为 key 的对象
const AccessTypeObject: Record<number, AccessTypeItem> = accessTypeList.reduce(
  (obj, item) => {
    obj[item.value] = item
    return obj
  },
  {} as Record<number, AccessTypeItem>
)

export const accessTypeToLabel = (accessType: number) => {
  return AccessTypeObject[accessType]?.label
}


// ====================  属性类型 ==================
// "" | "warning" | "success" | "info" | "primary" | "danger"
export const AttrTypeList = [
    { id:1, label: "设备上报", value: "device", colorType: "warning"},
    { id:2, label: "云端下发", value: "cloud",  colorType: "success"},
    { id:3, label: "设备云端共享", value: "shared", colorType: "info"},
    { id:4, label: "云端私有",  value: "private",  colorType: "danger" },
]

// 提取 connTypeList 的项类型
type AttrTypeItem = typeof AttrTypeList[number];

// 转换为以 value 为 key 的对象
const AttrTypeObject: Record<string, AttrTypeItem> = AttrTypeList.reduce(
    (obj, item) => {
        obj[item.value] = item;
        return obj;
    },
    {} as Record<string, AttrTypeItem>
);

export const AttrTypeToLabel = (attrType: string| undefined) => {
  if (attrType === undefined) {
    return "";
  }  else {
    return AttrTypeObject[attrType]?.label;
  }
}
export const AttrTypeColorType = (attrType: string) => {
    if (AttrTypeObject[attrType] === undefined) {
        return "warning"
    } else {
        return AttrTypeObject[attrType].colorType;
    }
    
}

type ElTextType = 'success' | 'warning' | 'danger' | 'info' | 'primary' | '';
export const GetAttrTypeCode = (attr_type: string | undefined) : ElTextType => {
  if (attr_type === undefined) {
    return "" as ElTextType;
  } else {
    return AttrTypeColorType(attr_type) as ElTextType; 
  }

}


// ====================  数据类型 ==================
export const DataTypeList = [
    { id:1, label: "Number(数值)", value: "number"},
    { id:2, label: "Switch(开关)", value: "switch"},
    { id:3, label: "Text(文本)", value: "text"},
    { id:4, label: "Enum(枚举)",  value: "enum"},
    { id:5, label: "Object(键值对)",  value: "object"},
    { id:6, label: "List(数组)",  value: "list"},
]

// 提取 数据类型 的项类型
type DataTypeItem = typeof DataTypeList[number];

// 转换为以 value 为 key 的对象
const DataTypeObject: Record<string, DataTypeItem> = DataTypeList.reduce(
    (obj, item) => {
        obj[item.value] = item;
        return obj;
    },
    {} as Record<string, DataTypeItem>
);

export const DataTypeToLabel = (dataType: string) => {
    return DataTypeObject[dataType]?.label;
}



export const getAuthTypeName = (authType: number): string => {
    const authTypeMap: Record<number, string> = {
      1: '一型一密',
      2: '一机一密',
    }
    return authTypeMap[authType] || '未知'
  }


export const getDefaultDevice = ():Device => {
  return {
    name: '设备1',
    status: 1,
    device_type_id: '',
    device_id: '',
    desc: '',
    device_code: '',
    device_key: '',
    extend_info: {},
    active_online: 0,
    active_status: 0,
    alarm: {},
    groups: [],
    debug: false,
    tags: [],
    device_type_info: {
      id: 0,
      device_type_id: '',
      device_type_code: '',
      name: '',
      desc: '',
      icon: '',
      status: 1,
      tags: [] as KeyValue[],
      extend_info: [] as ExtendInfo[],
      auth_type: 1,
      access_type: 0,
      conn_type: '',
      device_count: 0,
      created_at: '',
      updated_at: ''
    },
    group_info: [],
    created_at: '',
    updated_at: ''
  } 
}


export const ConnTypeOptions = [
    { key: 1, value: 'wifi', label: 'WIFI' },
    { key: 2, value: 'ethernet', label: '以太网' },
    { key: 3, value: '4g', label: '蜂窝网络4G' },
    { key: 4, value: '5g', label: '蜂窝网络5G' },
    { key: 5, value: 'nb-iot', label: 'NB-IoT' },
    { key: 6, value: 'other', label: '其他' }
]

// 提取 export const ConnTypeOptions  的项类型
type ConnTypeOptionsItem = (typeof ConnTypeOptions)[number]

// 转换为以 value 为 key 的对象
const ConnTypeObject: Record<string, ConnTypeOptionsItem> = ConnTypeOptions.reduce(
  (obj, item) => {
    obj[item.value] = item
    return obj
  },
  {} as Record<string, ConnTypeOptionsItem>
)

export const ConnTypeOptionsLabel = (connType: string) => {
  return ConnTypeObject[connType]?.label
}

