import { useWorktabStore } from '@/store/modules/worktab'
import { RouteLocationNormalized } from 'vue-router'
import { getIframeRoutes } from './menu'
import { is_iframe } from './utils'
import { useSettingStore } from '@/store/modules/setting'


/**
 * 根据当前路由信息设置工作标签页（worktab）
 * @param to 当前路由对象
 */
export const setWorktab = (to: RouteLocationNormalized): void => {
  const worktabStore = useWorktabStore()
  const { meta, path, name, params, query } = to
  if (!meta.is_hide_tab) {
    // 如果是 iframe 页面，则特殊处理工作标签页
    if (is_iframe(path)) {
      const iframeRoute = getIframeRoutes().find((route: any) => route.path === to.path)

      if (iframeRoute?.meta) {
        worktabStore.openTab({
          title: iframeRoute.meta.title,
          path,
          name: name as string,
          keep_alive: meta.keep_alive as boolean,
          params,
          query
        })
      }
    } else if (useSettingStore().showWorkTab || path === useSettingStore().homePage) {
      worktabStore.openTab({
        title: meta.title as string,
        path,
        name: name as string,
        keep_alive: meta.keep_alive as boolean,
        params,
        query
      })
    }
  }
}
