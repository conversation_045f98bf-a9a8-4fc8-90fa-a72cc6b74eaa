import { MenuListType } from '@/types/menu'
import { router } from '@/router'

// 打开外部链接
export const openExternalLink = (link: string) => {
  window.open(link, '_blank')
}

/**
 * 菜单跳转
 * @param item 菜单项
 * @param jumpToFirst 是否跳转到第一个子菜单
 * @returns
 */
export const handleMenuJump = (item: MenuListType, jumpToFirst: boolean = false) => {
  // const loading = ElLoading.service({
  //   lock: true,
  //   text: '正在加载中...,loading...',
  //   background: 'rgba(0, 0, 0, 0.9)',
  //   // fullscreen: true 
  // })
  // 处理外部链接
  const { link, is_iframe } = item.meta
  if (link && !is_iframe) {
    // setTimeout(() => {loading.close()}, 3000)
    return openExternalLink(link)
  }

  // 如果不需要跳转到第一个子菜单，或者没有子菜单，直接跳转当前路径
  if (!jumpToFirst || !item.children?.length) {
    // setTimeout(() => {loading.close()}, 500)
    return router.push(item.path)
  }

  // 获取第一个可见的子菜单，如果没有则取第一个子菜单
  const firstChild = item.children.find((child) => !child.meta.is_hide) || item.children[0]

  // 如果第一个子菜单是外部链接则打开新窗口
  if (firstChild.meta?.link) {
    // setTimeout(() => {loading.close()}, 3000)
    return openExternalLink(firstChild.meta.link)
  }

  // 跳转到子菜单路径
  router.push(firstChild.path)

  // setTimeout(() => {loading.close()}, 500)
}

// ... existing code ...
